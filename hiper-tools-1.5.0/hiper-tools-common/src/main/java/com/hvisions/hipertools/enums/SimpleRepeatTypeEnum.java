package com.hvisions.hipertools.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: SimpleRepeatTypeEnum</p>
 * <p>Description: 简单循环类型</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/7/26</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum SimpleRepeatTypeEnum implements IKeyValueObject {
    //参数用途
    SECOND(1, "秒"),

    MINUTE(2, "分钟"),

    HOUR(3, "小时"),

    DAY(4, "天"),

    WEEK(5, "周"),

    ;

    SimpleRepeatTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}









