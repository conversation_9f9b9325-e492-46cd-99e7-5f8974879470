package com.hvisions.hipertools.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: DayRepeatIntervalTypeEnum</p>
 * <p>Description: 每天重复执行间隔类型</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum DayRepeatIntervalTypeEnum implements IKeyValueObject {
    //参数用途
    SECOND(1, "秒"),
    MINUTE(2, "分钟"),
    HOUR(3, "小时");

    DayRepeatIntervalTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}

    
    
    
    
    
    
    
    
