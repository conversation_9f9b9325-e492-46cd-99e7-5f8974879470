package com.hvisions.hipertools.enums;

import com.hvisions.common.interfaces.BaseErrorCode;
import lombok.Getter;

/**
 * <p>Title: TimerExceptionEnum</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/9/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
public enum TimerExceptionEnum implements BaseErrorCode {
    //异常类型
    TIMER_IS_RUNNING(90001),
    PATTER_NOT_SUPPORT(90002),
    TIMER_FAIL_TO_RUN(90003),
    TIMER_FAIL_TO_STOP(90004),
    TIMER_TIME_ERROR(90005),
    //数据查询不存在
    NO_SUCH_ELEMENT(90006),
    //cron表达式错误
    TIMER_CRON_ERROR(90006),
    //结果超出上限2000
    RESULT_EXCEEDED(90007),
    ;
    private Integer code;

    TimerExceptionEnum(int code) {
        this.code = code;
    }


    @Override
    public String getMessage() {
        return this.toString();
    }
}
