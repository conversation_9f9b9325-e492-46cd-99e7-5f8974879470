package com.hvisions.hipertools.configuration;

/**
 * <AUTHOR>
 */
import com.hvisions.common.config.dynamic.annotation.DynamicConfig;
import com.hvisions.common.config.dynamic.annotation.PropertyDesc;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@ToString
@ConfigurationProperties(prefix = "h-visions.hiper-tools.timer")
@DynamicConfig
@Component
public class HiperToolsConfigProperty {
    @PropertyDesc(description = "开启触发预测",group = "HiperTools",notes = "开启后会每10天缓存一次timer的触发预测，用于大批量的预测统计")
    private Boolean enableTriggerCache;
    @PropertyDesc(description = "触发预测上限",group = "HiperTools",notes = "timer触发预测上限值，防止数据量太大出现内存问题")
    private Integer maxPrediction = 1000;
}
