package com.hvisions.hipertools.configuration;

import com.hvisions.hipertools.consts.TimerConsts;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>Title: TopicRabbitConfig</p>
 * <p>Description:话题模式消息队列 </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/2/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Configuration
public class TopicRabbitConfig {
    @Bean
    TopicExchange exchange() {
        return new TopicExchange(TimerConsts.TIMER_EXCHANGE);
    }
    @Bean
    TopicExchange exchangeV2() {
        return new TopicExchange(TimerConsts.TIMER_EXCHANGE_V2);
    }
}
