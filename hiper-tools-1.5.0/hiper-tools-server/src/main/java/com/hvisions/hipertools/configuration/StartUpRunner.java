package com.hvisions.hipertools.configuration;

import com.hvisions.common.runner.SafetyCommandLineRunner;
import com.hvisions.hipertools.service.TimerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>Title: StartUpRunner</p>
 * <p>Description: 程序启动开启</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/21</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class StartUpRunner extends SafetyCommandLineRunner {
    @Autowired
    TimerService timerService;

    /**
     * 系统启动时，所有是启动状态的Timer都要启动
     *
     * @param args 传参
     */
    @Override
    public void callRunner(String... args) {
        timerService.startAllTimer();
    }
}









