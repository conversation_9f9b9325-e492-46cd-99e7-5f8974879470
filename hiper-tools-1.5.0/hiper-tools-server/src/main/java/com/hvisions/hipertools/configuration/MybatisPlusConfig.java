package com.hvisions.hipertools.configuration;


import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <p>Title: MybatisPlusConfig</p>
 * <p>Description: MybatisPlus 分页配置</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/08/03</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EnableTransactionManagement
@Configuration
public class MybatisPlusConfig {
    @Bean
    public MybatisPlusInterceptor paginationInterceptor() {
        return new MybatisPlusInterceptor();
    }

}
