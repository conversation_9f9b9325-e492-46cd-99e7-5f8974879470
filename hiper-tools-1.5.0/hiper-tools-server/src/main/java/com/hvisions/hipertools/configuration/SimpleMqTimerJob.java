package com.hvisions.hipertools.configuration;

import com.hvisions.hipertools.service.TimerService;
import com.hvisions.hipertools.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;

/**
 * <p>Title: SimpleMqTimerJob</p>
 * <p>Description: Timer触发向消息队列发送普通topic消息</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/19</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Slf4j
public class SimpleMqTimerJob implements Job {
    private static TimerService timerService;

    static {
        timerService = SpringUtil.getBean(TimerService.class);
    }

    /**
     * 执行任务
     *
     * @param jobExecutionContext 任务上下文
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        JobDetail detail = jobExecutionContext.getJobDetail();
        int id = detail.getJobDataMap().getInt("id");
        timerService.triggerTimer(id, false);
    }

}









