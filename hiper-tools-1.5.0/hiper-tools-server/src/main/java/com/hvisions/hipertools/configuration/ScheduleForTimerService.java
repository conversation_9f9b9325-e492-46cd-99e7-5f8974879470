package com.hvisions.hipertools.configuration;

import com.hvisions.hipertools.service.TimerCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@ConditionalOnProperty(prefix = "h-visions.hiper-tools.timer", name = "enableTriggerCache", havingValue = "true")
public class ScheduleForTimerService {
    @Autowired
    TimerCacheService cacheService;

    /**
     * 10天缓存一次
     */
    @Scheduled(cron = "30 4 0/10 * * *")
    public void setTimerCache() {
        log.info("周期计划触发，缓存timer的触发记录信息，为了业务统计用");
        cacheService.saveTimerTriggerTime();
    }
}
