<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.hvisions.hipertools.dao.TimerMapper">
    <delete id="deleteTimeCache">
        delete
        from hv_bm_timer_trigger_cache
        where timer_id = #{timerId}
          and trigger_time between #{begin} and #{end}
    </delete>
    <select id="findAllEnableTimer" resultType="java.lang.Integer">
        select id from hv_bm_timer t1
                  where  t1.
    </select>
</mapper>