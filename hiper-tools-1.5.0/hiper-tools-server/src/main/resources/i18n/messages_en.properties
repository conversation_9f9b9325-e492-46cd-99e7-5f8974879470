SUCCESS=SUCCESS
TIMER_TIME_ERROR=TIMER_TIME_ERROR
SERVER_ERROR=SERVER_ERROR
JSON_PARSE_ERROR=JSON_PARSE_ERROR
ILLEGAL_STRING=ILLEGAL_STRING
NULL_RESULT=NULL_RESULT
VIOLATE_INTEGRITY=VIOLATE_INTEGRITY
IMPORT_FILE_NO_SUPPORT=IMPORT_FILE_NO_SUPPORT
IMPORT_SHEET_IS_NULL=IMPORT_SHEET_IS_NULL
ENTITY_PROPERTY_NOT_SUPPORT=ENTITY_PROPERTY_NOT_SUPPORT
SAVE_SHOULD_NO_IDENTITY=SAVE_SHOULD_NO_IDENTITY
UPDATE_SHOULD_HAVE_IDENTITY=UPDATE_SHOULD_HAVE_IDENTITY
CONST_VIOLATE=CONST_VIOLATE
NO_SUCH_ELEMENT=NO_SUCH_ELEMENT
ENTITY_NOT_EXISTS=ENTITY_NOT_EXISTS
DATA_INTEGRITY_VIOLATION=DATA_INTEGRITY_VIOLATION
COLUMN_PATTERN_ILLEGAL=COLUMN_PATTERN_ILLEGAL
DEMO_EXCEPTION_ENUM=DEMO_EXCEPTION_ENUM
TIMER_IS_RUNNING=TIMER_IS_RUNNING
PATTER_NOT_SUPPORT=PATTER_NOT_SUPPORT
TIMER_FAIL_TO_RUN=TIMER_FAIL_TO_RUN
TIMER_FAIL_TO_STOP=TIMER_FAIL_TO_STOP
TIMER_CRON_ERROR=TIMER_CRON_ERROR
RESULT_EXCEEDED=The result exceeded the upper limit by 2000