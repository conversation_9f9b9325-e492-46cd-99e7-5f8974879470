# suppress inspection "UnusedProperty" for whole file
#éç¨å¼å¸¸
SUCCESS=æå
TIMER_TIME_ERROR=æ¶é´éè¯¯ï¼æ§è¡æ¶é´ä¸è½å°äºå½åæ¶é´
SERVER_ERROR=æå¡å¨å¼å¸¸
JSON_PARSE_ERROR=Jsonè§£æéè¯¯
ILLEGAL_STRING=Jsonè§£æåºéï¼è¯·æ£æ¥jsonç»æ
NULL_RESULT=æ¥è¯¢ä¸ºç©º
VIOLATE_INTEGRITY=è¿åéå®ï¼è¯·æ£æ¥æ¯å¦æéå¤æ°æ®
IMPORT_FILE_NO_SUPPORT=æä»¶ç±»åä¸æ¯æ
IMPORT_SHEET_IS_NULL=æä»¶sheetè¡¨ä¸å­å¨
ENTITY_PROPERTY_NOT_SUPPORT=å®ä½å±æ§ä¸æ¯æï¼è¯·æ£æ¥å¯¼å¥æ°æ®
SAVE_SHOULD_NO_IDENTITY=ä¿å­ä¸åºè¯¥æä¸»é®
UPDATE_SHOULD_HAVE_IDENTITY=æ´æ°åºè¯¥æä¸»é®
CONST_VIOLATE=è¿åéå¶ï¼è¯·æ£æ¥æ°æ®åº
NO_SUCH_ELEMENT=æ°æ®æ¥è¯¢ä¸å­å¨
ENTITY_NOT_EXISTS=å¾æ©å±å¯¹è±¡ä¸å­å¨ï¼è¯·æ¥è¯¢åå¯¹è±¡æ¯å¦å­å¨
DATA_INTEGRITY_VIOLATION=æ°æ®å®æ´æ§éªè¯åºé
COLUMN_PATTERN_ILLEGAL=æ©å±åæ ¼å¼éæ³ï¼åªåè®¸æ°å­ï¼ä¸åçº¿ï¼è±æå­ç¬¦
#èªå®ä¹å¼å¸¸
DEMO_EXCEPTION_ENUM=ç¤ºä¾å¼å¸¸ç±»å
TIMER_IS_RUNNING=Timeræ­£å¨è¿è¡ä¸­ï¼è¯·ååæ­¢
PATTER_NOT_SUPPORT=ä¸æ¯æçå¨æè®¾ç½®
TIMER_FAIL_TO_RUN=Timerå¯å¨å¤±è´¥
TIMER_FAIL_TO_STOP=Timeråæ­¢å¤±è´¥
# cronè¡¨è¾¾å¼éè¯¯
TIMER_CRON_ERROR=Cronè¡¨è¾¾å¼éè¯¯
RESULT_EXCEEDED=ç»æè¶è¿äºä¸é2000