package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: InOrderTypeEnum</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum InOrderTypeEnum implements IKeyValueObject {

    //参数用途
    PURCHASE(1, "采购单入库"),
    MANUAL(2, "手动入库"),
    ;

    InOrderTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

    public static InOrderTypeEnum valueOfCode(int i){
        for (InOrderTypeEnum item :values()){
            if(item.code==i){
                return item;
            }
        }
        return null;
    }
}

    
    
    
    
    
    
    
    
