package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: DeliverEnum</p >
 * <p>Description: 移库单枚举</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/22</p >
 *
 * <AUTHOR> liu<PERSON>
 * @version :1.0.0
 */
public enum DeliverEnum implements IKeyValueObject {

    CREATE(1,"新建"),
    FINISH(2,"完成"),
    NOT_FINISH(3,"未完成"),
    DELIVERING(4,"移库中"),
    GENERAL(5,"普通移库"),
    OTHER(6,"其他类型"),
    MERGE(7,"库存合并")
    ;

    DeliverEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
