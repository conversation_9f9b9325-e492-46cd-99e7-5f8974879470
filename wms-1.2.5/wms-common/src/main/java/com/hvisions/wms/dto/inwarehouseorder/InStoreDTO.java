package com.hvisions.wms.dto.inwarehouseorder;


/**
 * <p>Title: InStoreDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.wms.annotation.GreaterThanNum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

@Getter
@Setter
@ToString
public class InStoreDTO {

    @ApiModelProperty(value = "入库单号")
    private String receiptNumber;

    /**
     * 入库单id
     */
    @ApiModelProperty(value = "入库单id")
    private Integer headerId;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    private String frameCode;
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer material;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNum;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @GreaterThanNum(message = "入库数量要大于0")
    private BigDecimal quantity;

    /**
     * 库位id
     */
    @ApiModelProperty(value = "库位id")
    private Integer locationId;
}









