package com.hvisions.wms.dto.stockouting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>Title: ManualOuting</p >
 * <p>Description: 出库表单</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/3</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
public class StockOutingForm {

    @ApiModelProperty(value = "出库单id")
    private Integer owId;

    @ApiModelProperty(value = "出库单号")
    private String owNumber;

    @ApiModelProperty(value = "物料id")
    @NotNull(message = "物料id不能为空")
    private Integer materialId;

    @ApiModelProperty(value = "批次号")
    @NotNull(message = "批次号不能为空")
    private String batchNumber;

    @ApiModelProperty(value = "数量")
    private BigDecimal num;

    @ApiModelProperty(value = "库位id")
    @NotNull(message = "库位id不能为空")
    private Integer locationId;

    @ApiModelProperty(value = "备注")
    private String description;
}
