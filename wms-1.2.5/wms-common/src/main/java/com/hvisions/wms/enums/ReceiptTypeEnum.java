package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;
import com.hvisions.common.utils.EnumUtil;

/**
 * <p>Title: ReceiptTypeEnum</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/1</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public enum ReceiptTypeEnum implements IKeyValueObject {

    /**
     * 类型列表
     */
    PURCHASE(0, "采购入库"),
    PRODUCT(1, "产品入库"),
    ARTIFICIAL(2, "手动入库"),
    PRODUCTION_RETURN(3, "生产退料"),
    OTHER(4, "其他类型"),
    FROZEN_IN_STOCK(5, "产品冻结入库"),
    PDA_MATERIAL(6, "PDA收料"),
    ;


    ReceiptTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override

    public String getName() {
        return this.name;
    }

    /**
     * 获取对象
     *
     * @param i 编号
     * @return 枚举对象
     */
    public static ReceiptTypeEnum getEnum(Integer i) {
        if (i == null) {
            return ReceiptTypeEnum.OTHER;
        }
        try {
            return EnumUtil.valueOf(i, ReceiptTypeEnum.class);
        } catch (Exception ex) {
            return ReceiptTypeEnum.OTHER;
        }
    }
}