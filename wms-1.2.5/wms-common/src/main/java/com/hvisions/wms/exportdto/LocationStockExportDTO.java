package com.hvisions.wms.exportdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <P> 库区库存导出   <P>
 *
 * <AUTHOR>
 * @date 2025/1/9
 */
@Data
public class LocationStockExportDTO {
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Integer warehouseId;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 库区id
     */
    @ApiModelProperty(value = "库位id")
    private Integer locationId;

    /**
     * 库区名称
     */
    @ApiModelProperty(value = "库区名称")
    private String locationName;
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private BigDecimal quantity;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String state;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unitName;
}
