package com.hvisions.wms.dto.stockWarning;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>Title: BaseStockWarning</p >
 * <p>Description: 库存警告查询结果</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/7</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Data
@ApiModel
public class BaseStockWarning {

    /**
     * 库位编码
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    /**
     * 库位名称
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "库位名称")
    private String locationName;

    /**
     * 物料编码
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 预计最高数量
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "预计最高数量")
    private BigDecimal storeMax;

    /**
     * 预计最低数量
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "预计最低数量")
    private BigDecimal storeMin;

    /**
     * 现有数量
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "现有数量")
    private BigDecimal quantity;

    /**
     * 建议调整值
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "建议调整值")
    private BigDecimal adjustedQuantity;

}
