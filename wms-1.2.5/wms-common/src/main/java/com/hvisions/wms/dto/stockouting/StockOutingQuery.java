package com.hvisions.wms.dto.stockouting;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>Title: StockOutingQuery</p >
 * <p>Description: 出库查询dto</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/3</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockOutingQuery extends PageInfo {
    @ApiModelProperty(value = "出库单号")
    private String owNumber;

    @ApiModelProperty(value = "出库日期(开始)")
    private Date startTime;

    @ApiModelProperty(value = "出库日期(结束)")
    private Date endTime;

    @ApiModelProperty(value = "出库状态")
    private Integer status;

}
