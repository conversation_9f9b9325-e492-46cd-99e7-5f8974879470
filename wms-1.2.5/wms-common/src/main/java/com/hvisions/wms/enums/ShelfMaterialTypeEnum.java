package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: ShelfMaterialTypeEnum</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public enum ShelfMaterialTypeEnum implements IKeyValueObject {
    HAVE_IN_HAND(0, "进行中"),

    FINISH(1, "完成上架");

    ShelfMaterialTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override

    public String getName() {
        return this.name;
    }
}
