package com.hvisions.wms.annotation;

import javax.validation.Constraint;
import java.lang.annotation.*;

/**
 * <p>Title: CheckLockStatus</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/23</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CheckLockStatus {
}
