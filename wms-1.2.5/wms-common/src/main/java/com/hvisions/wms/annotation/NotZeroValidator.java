package com.hvisions.wms.annotation;

import com.hvisions.common.exception.BaseKnownException;
import org.springframework.util.ObjectUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

/**
 * <p>Title: NotZeroValidator</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/10/13</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public class NotZeroValidator implements ConstraintValidator<NotZero, BigDecimal> {

    @Override
    public void initialize(NotZero constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(BigDecimal input, ConstraintValidatorContext constraintValidatorContext) {
        if (ObjectUtils.isEmpty(input)) {
            throw new BaseKnownException(1000, "请录入正确数字!");
        }
        return input.compareTo(BigDecimal.valueOf(0)) != 0;
    }
}
