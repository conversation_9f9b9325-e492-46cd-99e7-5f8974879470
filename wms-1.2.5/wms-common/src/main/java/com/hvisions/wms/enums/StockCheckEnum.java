package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: StockCheckEnum</p >
 * <p>Description: 盘库枚举</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/8</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public enum StockCheckEnum implements IKeyValueObject {
    STOCK_OUTING_FINISHED(1, "已调库"),
    STOCK_OUTING_PROCESSING(0, "调库中");

    private Integer code;
    private String status;

    StockCheckEnum(int code, String status) {
        this.code = code;
        this.status = status;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.status;
    }
}
