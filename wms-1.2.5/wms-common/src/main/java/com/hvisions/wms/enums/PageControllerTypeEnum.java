package com.hvisions.wms.enums;

/**
 *  页面操作类型枚举
 * <AUTHOR>
 * @date 2024-06-26 16:50
 */
public enum PageControllerTypeEnum {
    ADDorEDIT(1,"新增/修改"),
    DELETE(2,"删除");

    private Integer code;
    private String describe;

    private PageControllerTypeEnum(Integer code,String describe){
        this.code = code;
        this.describe = describe;
    }

    public Integer getCode(){
        return this.code;
    }

    public String getDescribe(){
        return this.describe;
    }
}
