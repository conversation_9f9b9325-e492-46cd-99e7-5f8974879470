package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: StockOutingEnum</p >
 * <p>Description: 出库状态枚举</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/3</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public enum StockOutingEnum implements IKeyValueObject {
    STOCK_OUTING_FINISHED(1,"已完成"),
    STOCK_OUTING_PROCESSING(0,"出库中")
    ;

    private Integer code;
    private String status;

    StockOutingEnum(int code, String status) {
        this.code=code;
        this.status=status;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.status;
    }
}
