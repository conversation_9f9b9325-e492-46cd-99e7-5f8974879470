package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;
import lombok.Getter;

/**
 * <p>Title:ProcurementEmum</p>
 * <p>Description:采购单状态枚举</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2020/12/23</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Getter
public enum ProcurementEmum implements IKeyValueObject {
    //采购单状态
    CREATE(1, "新建"),
    DELIVERED(2, "已下发"),
    RECEIVED(3,"已收货"),
    ;

    ProcurementEmum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}