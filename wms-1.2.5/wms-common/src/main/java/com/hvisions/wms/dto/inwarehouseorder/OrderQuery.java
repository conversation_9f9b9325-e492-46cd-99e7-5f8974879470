package com.hvisions.wms.dto.inwarehouseorder;

/**
 * <p>Title: OrderQuery</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString
public class OrderQuery extends PageInfo {
    /**
    *   入库单号
    */
    @ApiModelProperty(value = "入库单号")
    private String receiptNumber;
    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    private String purchaseReceiptNumber;
    /**
     * 收货日期开始
     */
    @ApiModelProperty(value = "收货日期开始")
    private LocalDateTime startTime;
    /**
     * 收货时间结束
     */
    @ApiModelProperty(value = "收货时间结束")
    private LocalDateTime endTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer state;

    /**
     * 入库单类型
     */
    @ApiModelProperty(value = "入库单类型", allowableValues = "1,2", notes = "1:采购入库，2：手动入库")
    private Integer type;
}









