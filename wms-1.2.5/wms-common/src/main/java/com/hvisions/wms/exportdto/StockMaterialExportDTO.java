package com.hvisions.wms.exportdto;

import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.wms.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <P> 库存信息导出   <P>
 *
 * <AUTHOR>
 * @date 2025/1/8
 */
@Data
public class StockMaterialExportDTO  extends SysBaseDTO {


    /**
     * 物料id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "物料id")
    private Integer materialId;


    /**
     * 库位id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "库位id")
    private Integer locationId;

    /**
     * 料框编号
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "料框编号")
    private String frameCode;

    /**
     * 物料编码
     */
    @ExcelAnnotation(width = 18)
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ExcelAnnotation(width = 18)
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    /**
     * 物料批次号
     */
    @ExcelAnnotation(width = 18)
    @ApiModelProperty(value = "物料批次号")
    private String materialBatchNum;

    /**
     * 原物料批次号
     */
    @ExcelAnnotation(width = 18)
    @ApiModelProperty(value = "原物料批次号")
    private String preMaterialBatchNum;

    /**
     * 库位编码
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "库位编码")
    private String locationCode;

    @ApiModelProperty(value = "库区编码")
    private String locationAreaCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @ExcelAnnotation(width = 15)
    private String supplierName;

    /**
     * 货位
     */
    @ApiModelProperty(value = "储位")
    private String locationDescription;

    /**
     * 数量
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 使用数量
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "已占用数量")
    private BigDecimal usedCount;


    /**
     * 单位名称
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 库区状态
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "库区状态")
    private String locationState;

    /**
     * 是否冻结
     */
    @ApiModelProperty(value = "是否冻结")
    private Boolean frozen;

    /**
     * 料点id（库位id）
     */
    @ApiModelProperty(value = "库位id")
    private  String  materialPointId;

    /**
     * 料点编码（库位编码）
     */
    @ApiModelProperty(value = "库位编码")
    private String  materialPointCode;

    /**
     * 料点名称（库位名称）
     */
    @ApiModelProperty(value = "库位名称")
    private String  materialPointName;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String  shipNo;
    /**
     * 流向
     */
    @ApiModelProperty(value = "流向")
    private String blockCode;
}
