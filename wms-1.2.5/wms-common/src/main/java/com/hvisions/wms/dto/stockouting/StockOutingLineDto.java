package com.hvisions.wms.dto.stockouting;

import com.hvisions.wms.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: ManualStockOutingDetailDto</p >
 * <p>Description: 出库单行表</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/3</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockOutingLineDto extends SysBaseDTO {

    @ApiModelProperty(value = "出库单头表id")
    @NotNull(message = "出库单头表id不能为空")
    private Integer headerId;

    @ApiModelProperty(value = "物料id")
    @NotNull(message = "物料id不能为空")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码", readOnly = true)
    private String materialCode;

    @ApiModelProperty(value = "物料名称", readOnly = true)
    private String materialName;

    @ApiModelProperty(value = "出库数量", readOnly = true)
    private BigDecimal outNum;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planNum;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "出库单批次信息", readOnly = true)
    private List<StockOutingBatchDto> stockOutingBatchDtos;
}
