package com.hvisions.wms.dto.inwarehouseorder;

/**
 * <p>Title: InWarehouseOrderLineDTO</p>
 * <p>Description: 入库单行表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.wms.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@ToString
public class InWarehouseOrderLineDTO extends SysBaseDTO {

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", readOnly = true)
    private String materialCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称", readOnly = true)
    private String materialName;

    /**
     * 采购数量
     */
    @ApiModelProperty(value = "采购数量", readOnly = true)
    private BigDecimal purchaseNum;

    /**
     * 入库数量
     */
    @ApiModelProperty(value = "入库数量", readOnly = true)
    public BigDecimal getInNuber() {
        if (details == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = BigDecimal.ZERO;
        for (InWarehouseOrderDetailDTO detail : details) {
            result = result.add(detail.getNumber());
        }
        return result;
    }

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", readOnly = true)
    public String getState() {
        if (getInNuber().compareTo(purchaseNum) >= 0) {
            return "完成";
        } else {
            return "进行中";
        }
    }

    /**
     * 入库详情
     */
    @ApiModelProperty(value = "入库详情")
    private List<InWarehouseOrderDetailDTO> details;

}









