package com.hvisions.wms.dto.stockouting;

import com.hvisions.wms.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: ManualOuting</p >
 * <p>Description: 出库单头表</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/3</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockOutingHeaderDto extends SysBaseDTO {
    @ApiModelProperty(value = "出库单号")
    private String owNumber;

    @ApiModelProperty(value = "出库状态", readOnly = true)
    private Integer status;

    @ApiModelProperty(value = "操作人", readOnly = true)
    private String operatorName;

    @ApiModelProperty(value ="关联单号")
    private String associateNumber;

    @ApiModelProperty(value = "出库时间",readOnly = true)
    private Date outStockTime;

    @ApiModelProperty(value ="出库单详情",readOnly = true)
    private List<StockOutingLineDto> stockOutingLineDtos;

}
