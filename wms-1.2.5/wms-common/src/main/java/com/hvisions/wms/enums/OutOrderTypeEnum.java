package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;
import lombok.Getter;

/**
 * <p>Title: OutOrderTypeEnum</p >
 * <p>Description: 出库单枚举</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/11</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@Getter
public enum OutOrderTypeEnum implements IKeyValueObject {
    //工单状态
    CREATE(1,"新建"),
    PIKING(2,"捡料中"),
    FINISH(3,"完成出库"),
    APPROVE_SUCCESS(4,"审批通过"),
    RECEIPT(5,"收料完成"),
    NOT_FINISH(6,"未完成"),
    NOT_APPROVE(7,"未审批"),
    APPROVE_FAIL(8,"审批不通过"),
    ;

    OutOrderTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;
    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
