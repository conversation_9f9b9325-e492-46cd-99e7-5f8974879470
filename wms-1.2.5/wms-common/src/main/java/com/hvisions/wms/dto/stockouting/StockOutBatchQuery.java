package com.hvisions.wms.dto.stockouting;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: StockOutBatchQuery</p >
 * <p>Description: 出库批次详情查询</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/5/11</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StockOutBatchQuery extends PageInfo {

    @ApiModelProperty(value = "行id")
    private Integer lineId;
}
