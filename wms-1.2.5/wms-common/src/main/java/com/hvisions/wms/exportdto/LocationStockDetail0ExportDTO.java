package com.hvisions.wms.exportdto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2025/1/9
 */
@Data
public class LocationStockDetail0ExportDTO {

    /**
     * 库区编码
     */
    @ApiModelProperty(value = "库区编码")
    private String locationAreaCode;

    /**
     * 库区名称
     */
    @ApiModelProperty(value = "库区名称")
    private String locationDescription;

    /**
     * 库位编码
     */
    @ApiModelProperty(value = "库位编码")
    private String  materialPointCode;

    /**
     * 库位名称
     */
    @ApiModelProperty(value = "库位名称")
    private String  materialPointName;

    /**
     * 物料批次号
     */
    @ExcelAnnotation(width = 18)
    @ApiModelProperty(value = "物料批次号")
    private String materialBatchNum;

    /**
     * 物料编码
     */
    @ExcelAnnotation(width = 18)
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ExcelAnnotation(width = 18)
    @ApiModelProperty(value = "物料名称")
    private String materialName;


    /**
     * 单位名称
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "单位名称")
    private String unitName;

    /**
     * 料框编号(托盘)
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "料框编号(托盘)")
    private String frameCode;


    /**
     * 数量
     */
    @ExcelAnnotation(width = 15)
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 是否冻结
     */
    @ApiModelProperty(value = "是否冻结")
    private Boolean frozen;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    protected Date createTime;







}
