package com.hvisions.wms.dto.inwarehouseorder;

/**
 * <p>Title: InWarehouseOrderDetailDTO</p>
 * <p>Description: 入库单详情表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.wms.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
public class InWarehouseOrderDetailDTO extends SysBaseDTO implements Comparable<InWarehouseOrderDetailDTO> {
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNum;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal number;
    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称", readOnly = true)
    private String locationName;
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Integer locationId;

    /**
     * 是否是本订单入库的物料
     */
    @ApiModelProperty(value = "是否是本订单入库的物料")
    private Boolean thisOrder;

    /**
     * 对采购入库排序,本单号排前面,非本单号排后面
     *
     * @param o the object to be compared.
     * @return 1:大于 -1:小于
     */
    @Override
    public int compareTo(InWarehouseOrderDetailDTO o) {
        return o.getThisOrder() ? 1 : -1;
    }
}









