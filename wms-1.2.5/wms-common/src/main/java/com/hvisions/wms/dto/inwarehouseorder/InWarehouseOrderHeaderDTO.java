package com.hvisions.wms.dto.inwarehouseorder;

import com.hvisions.wms.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>Title: InWarehouseOrderHeaderDTO</p>
 * <p>Description: 入库单头表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Getter
@Setter
@ToString
public class InWarehouseOrderHeaderDTO extends SysBaseDTO {
    /**
     * 单号
     */
    @ApiModelProperty(value = "单号")
    private String receiptNumber;

    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称", readOnly = true)
    private String supplierName;
    /**
     * 料框编号
     */
    private String frameCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", allowableValues = "1,2", notes = "1:执行中，2：完成")
    private Integer state;

    /**
     * 采购单号
     */
    @ApiModelProperty(value = "采购单号")
    private String purchaseReceiptNumber;

    /**
     * 实际收货时间
     */
    @ApiModelProperty(value = "实际收货时间", readOnly = true)
    private LocalDateTime actualInTime;

    /**
     * 计划收货时间
     */
    @ApiModelProperty(value = "计划收货时间")
    private LocalDateTime planInTime;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名", readOnly = true)
    private String operator;

    /**
    *   行表信息
    */
    @ApiModelProperty(value = "行表信息")
    private List<InWarehouseOrderLineDTO> lines;
}









