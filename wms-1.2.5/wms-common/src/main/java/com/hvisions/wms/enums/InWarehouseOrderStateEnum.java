package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: InWarehouseOrderStateEnum</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2021/9/3</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum InWarehouseOrderStateEnum implements IKeyValueObject {

    //参数用途
    CREATE(1, "创建"),
    FINISH(2, "完成"),
    ;

    InWarehouseOrderStateEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    private int code;
    private String name;


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }

}

    
    
    
    
    
    
    
    
