package com.hvisions.wms.dto.agvScheduling;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel("添加/修改调度任务信息")
public class HvWmAgvSchedulingCancelDTO {

    /**
     * 调度任务编号
     */
    @ApiModelProperty(value = "调度任务编号")
    private String taskNo;


}
