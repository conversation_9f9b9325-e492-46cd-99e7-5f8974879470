package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.BaseErrorCode;
import lombok.Getter;

/**
 * <p>Title: ResultEnum</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/9/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum WmsExceptionEnum implements BaseErrorCode {
    //异常枚举
    TRANSFER_BOX_NOT_EMPTY(8000),
    COUNT_OVER_LIMIT(930001),
    BOX_NOT_EXIST(8001),
    QUANTITY_NOT_ENOUGH(8002),
    ERROR_QUANTITY(8003),
    <PERSON>OC<PERSON><PERSON>_FROZEN(8005),
    IS_FINISH(8006),
    NOT_FIND_QUALITY(8007),
    NOT_FIND_SHELF(8008),
    NOT_OPERATION(8009),
    NOT_OPERATION_DELETE(8020),
    LOCATION_AND_NUM_HAVE_ONE(8010),
    QUANTITY_NOT_CREATE(8011),
    ALREADY_SHELF_NOT_CREATE(8012),
    DELETE_ERROR(8014),
    UPDATE_ERROR(8015),
    SHELF_DELETE_ERROR(8016),
    MATERIAL_LOCATION_NOT_NULL(8017),
    IS_NOT_FINISH(80017),
    LINE_NOT_ALL_FINISH(8018),
    IS_NOT_DELETE(80021),
    IS_NOT_UPDATE(80022),
    NOT_CHOOSE_MATERIAL(8023),
    ACTUAL_COUNT_NOT_ZERO(8024),
    OPERATION_NOT_CREATE(80025),
    STATE_NOT_UPDATE(80027),
    DELIVER_FAIL(80028),
    FIND_TAKING_ERROR(8025),
    NOT_FINISH_QUANTITY(8026),
    CHOOSE_MATERIAL(8027),
    RECEIPT_IS_FINISH(8028),
    NOT_FINISH_SHELF(8029),
    RECEIPT_IS_FINISH_NOT_IN_STOCK(8030),
    OPERATION_FAIL(80031),
    PICK_FAIL(80032),
    APPROVE_NOT_FINISH(80033),
    IS_NOT_MATERIAL(80035),
    PICK_FINISH(80036),
    PICKING(80037),
    PICK_NOT_START(80038),
    STOCK_FINISH(80039),
    FINISH_STOCK_TAKING(80040),
    ADJUST_ORDER_NOT_EXIST(80041),
    QUANTITY_IS_FINISH(80043),
    STOCK_ORDER_NOT_START(80044),
    STOCK_NOT_FINISH(80046),
    SHELF_IN(80047),
    SHELF_QUANTITY_NOT(80048),
    STOCK_DELETE_ERROR(80049),
    QUANTITY_NOT_ZERO(80051),
    MATERIAL_SHElF(80052),
    RECEIPT_FINISH(80053),
    QUANTITY_FINISH(80054),
    MATERIAL_IS_EXIST(80055),
    OUT_ORDER_DELETE_FAIL(80056),
    DELIVER_LINE_NOT_EXIT(80057),
    DELIVER_NOT_OPERATION(80058),
    ADJUST_LIVE_NOT_EXIST(80059),
    ADJUST_NOT_OPERATION(80060),
    QUANTITY_NOT_ILLEGAL(80061),
    OUT_ODER_NOT_EXIT(80067),
    APPROVE_FAIL(80063),
    CREATE_NOT_APPROVE(80064),
    ADD_NOT_APPROVE(80065),
    STOCK_FAIL(80066),
    NOT_SHELF_MATERIAL(80062),
    DELETE_NOT_APPROVE(80067),
    RECEIPT_IS_NOT_FIND(80068),
    NOT_HAVE_RULE(80069),
    SET_MIN_UNIT(80070),
    DIVISION_NOT_ZERO(80071),
    OUT_LINE_NOT_EXIT(80072),
    DELETE_ADJUST_FAIL(80074),
    CONFIRM_ADJUST_FAIL(80075),
    UPDATE_ADJUST_FAIL(80076),
    FROM_STOCK_NOT_EXIST(80077),
    DELIVER_FINISHED(80078),
    OCCUPY_EXISTS(80079),
    STOCK_EMPTY(80080),
    OCCUPY_NOT_FINISH(80081),
    OUT_ORDER_NOT_EXISTS(80082),
    OUT_ORDER_NOT_IN_CREATE_STATE(80083),
    NOT_FIND_MATERIAL(80084),
    DELIVER_IN_PICKING(80085),
    EMPTY_ORDER(80086),
    OUT_ORDER_IS_APPROVED(80087),
    //出库
    STOCK_OUT_ISFINISHED(80088),
    STOCK_OUT_DETAIL_IS_EMPTY(80090),
    STOCK_OUT_REPEATED(80091),
    STOCK_OUT_MATERIAL_EXISTS(80095),
    //盘点
    STOCK_PULL_ISEMPTY(80092),
    HAS_PULL_BEFORE(80093),
    CHECKING(80095),
    ;

    private Integer code;

    WmsExceptionEnum(int code) {
        this.code = code;
    }


    @Override
    public String getMessage() {
        return this.toString();
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
