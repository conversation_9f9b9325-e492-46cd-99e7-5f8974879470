package com.hvisions.wms.dto.stockouting;

import com.hvisions.wms.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>Title: StockOutingBatchDto</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/3</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StockOutingBatchDto extends SysBaseDTO {

    @ApiModelProperty(value = "批次号")
    private String batchNumber;

    @ApiModelProperty(value ="数量")
    private BigDecimal num;

    @ApiModelProperty(value ="库位id")
    private Integer locationId;

    @ApiModelProperty(value = "库位名称",readOnly = true)
    private String locationName;

    @ApiModelProperty(value ="物料id")
    private Integer materialId;

    @ApiModelProperty(value ="物料编码")
    private String materialCode;

    @ApiModelProperty(value = "出库单行表id")
    private Integer outingLineId;
}
