package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: ReceiptStateEnum</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/1</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public enum ReceiptStateEnum implements IKeyValueObject {
    NEW(0, "新建"),
    RECEIVING(1, "质检中"),
    RECEIVING_FINISH(2, "质检完成"),
    SHELF(3, "上架中"),
    FINISH(4, "完成");

    ReceiptStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override

    public String getName() {
        return this.name;
    }
}
