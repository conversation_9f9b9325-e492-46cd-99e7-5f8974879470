package com.hvisions.wms.dto.stocktaking;

import com.hvisions.wms.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>Title: StockTakingOrderDTO</p>
 * <p>Description: 盘点单</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
@Setter
@ToString
public class StockTakingOrderDTO extends SysBaseDTO {
    /**
     * 盘点单号
     */
    @ApiModelProperty(value = "盘点单号")
    @NotNull(message = "盘点单号不能为空")
    @Length(max = 200, message = "单号不能超过200个字符")
    private String orderCode;

    /**
     * 盘点单类型
     */
    @ApiModelProperty(value = "盘点单类型", allowableValues = "1,2", notes = "1:根据库位盘点，2：根据物料id盘点")
    private Integer type;
    /**
     * 盘点库位
     */
    @ApiModelProperty(value = "盘点储位")
    private Integer locationId;
    /**
     * 库位名称
     */
    @ApiModelProperty(value = "储位名称", readOnly = true)
    private String locationName;
    /**
     * 盘点物料id
     */
    @ApiModelProperty(value = "盘点物料id")
    private Integer materialId;
    /**
     * 盘点物料名称
     */
    @ApiModelProperty(value = "盘点物料名称", readOnly = true)
    private String materialName;
    /**
     * 盘点完成时间
     */
    @ApiModelProperty(value = "盘点完成时间")
    private Date completeTime;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 盘点单状态
     */
    @ApiModelProperty(value = "盘点单状态", readOnly = true, allowableValues = "新建，盘点中，盘点完成")
    private String state;

}









