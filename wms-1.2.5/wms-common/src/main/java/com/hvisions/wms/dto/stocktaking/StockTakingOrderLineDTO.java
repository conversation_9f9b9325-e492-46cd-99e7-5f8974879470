package com.hvisions.wms.dto.stocktaking;

import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.wms.dto.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: StockTakingOrderLineDTO</p>
 * <p>Description: 盘点单行表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
@Setter
@ToString
public class StockTakingOrderLineDTO extends SysBaseDTO {
    //注释名称由测试提出bug指明与 界面显示一致进行修改
    /**
     * 盘点单id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "盘点单id")
    private Integer orderId;
    /**
     * 物料id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "物料id", readOnly = true)
    private Integer materialId;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", readOnly = true)
    private String materialCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "盘点物料名称", readOnly = true)
    private String materialName;
    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位", readOnly = true)
    private String unit;
    /**
     * 库位id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "储位id", readOnly = true)
    private Integer locationId;
    /**
     * 库位名称
     */
    @ApiModelProperty(value = "库位", readOnly = true)
    private String locationName;
    /**
     * 原有数量
     */
    @ApiModelProperty(value = "应有库存", readOnly = true)
    private BigDecimal originQuantity;
    /**
     * 真实数量
     */
    @ApiModelProperty(value = "实际库存")
    private BigDecimal realQuantity;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", readOnly = true, allowableValues = "新建，盘点中，已盘点")
    private String state;

    /**
     * 排序
     */
    @ExcelAnnotation(ignore = true)
    private List<Integer> sortString;

    /**
     * 物料批次号
     */
    @ExcelAnnotation(width = 18)
    @ApiModelProperty(value = "物料批次")
    private String materialBatchNum;

    /**
     * 差值
     */
    @ApiModelProperty(value = "差值", readOnly = true)
    private BigDecimal getDifferences() {
        return originQuantity.subtract(realQuantity);
    }


}









