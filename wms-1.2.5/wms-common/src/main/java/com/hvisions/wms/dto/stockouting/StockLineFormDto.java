package com.hvisions.wms.dto.stockouting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <p>Title: StockLineFormDto</p >
 * <p>Description: 创建出库清单</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/4/19</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */

@Data
public class StockLineFormDto {

    @ApiModelProperty(value = "出库单头表id")
    private Integer headerId;

    @ApiModelProperty(value = "物料id")
    @NotNull(message = "物料id不能为空")
    private Integer materialId;

    @ApiModelProperty(value = "计划数量")
    private BigDecimal planNum;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "出库单号")
    private String owNumber;
}
