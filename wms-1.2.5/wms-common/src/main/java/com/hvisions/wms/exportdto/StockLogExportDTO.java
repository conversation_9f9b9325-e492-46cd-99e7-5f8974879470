package com.hvisions.wms.exportdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2025/1/13
 */
@Data
public class StockLogExportDTO {

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;


    /**
     * 物料批次号
     */
    @ApiModelProperty(value = "物料批次号")
    private String materialBatchNum;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unitName;

    /**
     * 库区编码
     */
    @ApiModelProperty(value = "库区编码")
    private String locationCode;

    /**
     * 库区名称
     */
    @ApiModelProperty(value = "库区名称")
    private String locationName;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String operation;

    /**
     * 业务号
     */
    @ApiModelProperty(value = "业务号")
    private String orderCode;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    protected Date createTime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;


    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String userName;

}
