package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: ReceiptMaterialType</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public enum ReceiptMaterialTypeEnum implements IKeyValueObject {

    NO_RECEIPT(0, "未收货"),
    QUALITY(1, "质检中"),
    QUALIFIED(2, "合格"),
    NO_QUALIFIED(3, "不合格"),
    RECEIPT(4, "确认收货"),
    GIVE_UP_MATERIAL(5, "放弃收货"),
    ALREADY_SHELF(6, "上架中"),
    FINISH_SHELF(7, "上架完成");

    ReceiptMaterialTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override

    public String getName() {
        return this.name;
    }
}