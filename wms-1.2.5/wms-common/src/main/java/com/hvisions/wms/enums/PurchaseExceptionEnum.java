package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.BaseErrorCode;
import lombok.Getter;

/**
 * <p>Title: PurchaseExceptionEnum</p >
 * <p>Description: 采购异常枚举</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
public enum PurchaseExceptionEnum implements BaseErrorCode {

    //参数用途
    //没有此采购单
    THIS_PURCHASE_ORDER_IS_NOT_AVAILABLE(15001),

    //已经生效禁止修改删除
    NO_MODIFICATION_OR_DELETION_IS_ALLOWED(15002),

    ;



    private int code;
    PurchaseExceptionEnum(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return this.toString();
    }

    @Override
    public Integer getCode() {
        return code;
    }

}