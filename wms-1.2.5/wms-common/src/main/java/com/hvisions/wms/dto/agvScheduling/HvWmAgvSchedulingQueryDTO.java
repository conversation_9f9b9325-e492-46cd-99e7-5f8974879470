package com.hvisions.wms.dto.agvScheduling;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel("调度任务查询参数")
public class HvWmAgvSchedulingQueryDTO extends PageInfo {

    @ApiModelProperty(value = "调度任务编号")
    private String taskNo;

    @ApiModelProperty(value = "请求任务号")
    private String requestTaskNo;

    @ApiModelProperty(value = "任务类型 0-空框请求；1-满框调度；2-其他")
    private Integer taskType;

    @ApiModelProperty(value = "AGV编号")
    private String agvNo;

    @ApiModelProperty(value = "AGV类型名称")
    private String agvTypeName;

    @ApiModelProperty(value = "料框/托盘编号")
    private String palletNo;

    @ApiModelProperty(value = "料框/托盘类型名称")
    private String palletTypeName;

    @ApiModelProperty(value = "调度状态 0-带开始；1-待走出储位；2-运输中；3-待入储位；4-完成；5-取消")
    private Integer schedulingState;

    @ApiModelProperty(value = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime plannedStartTime;

    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime plannedEndTime;


}
