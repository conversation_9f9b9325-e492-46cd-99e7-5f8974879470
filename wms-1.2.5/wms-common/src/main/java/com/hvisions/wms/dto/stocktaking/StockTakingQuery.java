package com.hvisions.wms.dto.stocktaking;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <p>Title: StockTakingQuery</p>
 * <p>Description: 盘点单查询对象</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
@Setter
@ToString
public class StockTakingQuery extends PageInfo {
    /**
     * 盘点单号
     */
    @ApiModelProperty(value = "盘点单号")
    private String orderCode;
    /**
     * 盘点单状态
     */
    @ApiModelProperty(value = "盘点单状态")
    private String state;
    /**
     * 创建起始时间
     */
    @ApiModelProperty(value = "创建起始时间")
    private Date createTimeStart;
    /**
     * 创建结束时间
     */
    @ApiModelProperty(value = "创建结束时间")
    private Date createTimeEnd;
    /**
     * 完成起始时间
     */
    @ApiModelProperty(value = "完成起始时间")
    private Date finishTimeStart;
    /**
     * 完成结束时间
     */
    @ApiModelProperty(value = "完成结束时间")
    private Date finishTimeEnd;
    /**
     * 储位id
     */
    @ApiModelProperty(value = "储位id")
    private Integer locationId;
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;
}









