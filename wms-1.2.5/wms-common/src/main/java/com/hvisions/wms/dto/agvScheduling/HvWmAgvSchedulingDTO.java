package com.hvisions.wms.dto.agvScheduling;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@ApiModel("添加/修改调度任务信息")
public class HvWmAgvSchedulingDTO {
    /**
     * 主键
     */
    @ExcelAnnotation(ignore = true)
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 调度任务编号
     */
    @ApiModelProperty(value = "调度任务编号")
    private String taskNo;

    /**
     * 任务类型 0-空框请求；1-满框调度；2-其他
     */
    @ApiModelProperty(value = "任务类型 0-空框请求；1-满框调度；2-其他")
    private Integer taskType;

    /**
     * 请求任务号
     */
    @ApiModelProperty(value = "请求任务号")
    private String requestTaskNo;

    /**
     * AGV编号
     */
    @ApiModelProperty(value = "AGV编号")
    private String agvNo;

    /**
     * AGV名称
     */
    @ApiModelProperty(value = "AGV名称")
    private String agvName;

    /**
     * AGV类型名称
     */
    @ApiModelProperty(value = "AGV类型名称")
    private String agvTypeName;

    /**
     * AGV类型编码
     */
    @ApiModelProperty(value = "AGV类型编码")
    private String agvTypeNo;

    /**
     * 出发点(出发料位号)
     */
    @ApiModelProperty(value = "出发点(出发料位号)")
    private String startPoint;

    /**
     * 目标点(终点料位号)
     */
    @ApiModelProperty(value = "目标点(终点料位号)")
    private String endPoint;

    /**
     * 优先级 (默认为1 数据越大优先级越高)
     */
    @ApiModelProperty(value = "优先级 (默认为1 数据越大优先级越高)")
    private String priority;

    /**
     * 调度状态 0-待开始；1-待走出储位；2-运输中；3-待入储位；4-完成；5-取消
     */
    @ApiModelProperty(value = "调度状态 0-待开始；1-待走出储位；2-运输中；3-待入储位；4-完成；5-取消")
    private Integer schedulingState;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String orderNo;

    /**
     * 托盘编号
     */
    @ApiModelProperty(value = "托盘编号")
    private String palletNo;

    /**
     * 托盘名称
     */
    @ApiModelProperty(value = "托盘名称")
    private String palletName;

    /**
     * 托盘类型编码
     */
    @ApiModelProperty(value = "托盘类型编码")
    private String palletTypeNo;

    /**
     * 托盘类型名称
     */
    @ApiModelProperty(value = "托盘类型名称")
    private String palletTypeName;


    /**
     * 是否手动 0-自动；1-手动
     */
    @ApiModelProperty(value = "是否手动 0-自动；1-手动")
    private Integer manual;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime plannedEndTime;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime actualStartTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间")
@JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime actualEndTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creatorName;

    /**
     * 多工厂字段
     */
    @ExcelAnnotation(ignore = true)
    private String siteNum;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updaterName;

}
