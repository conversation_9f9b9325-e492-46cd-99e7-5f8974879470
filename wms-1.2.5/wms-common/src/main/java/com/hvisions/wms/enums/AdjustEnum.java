package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: AdjustEnum</p >
 * <p>Description: 库存调整枚举</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/22</p >
 *
 * <AUTHOR> l<PERSON><PERSON>
 * @version :1.0.0
 */
public enum AdjustEnum implements IKeyValueObject {

    CREATE(1, "新建"),
    FINISH(2, "完成"),
    NOT_FINISH(3, "未完成"),
    ADJUST_ORDER(4, "库存调整"),
    STOCK_CHECK_PROFIT(5, "盘点库存-盘盈"),
    STOCK_CHECK_LOSS(6, "盘点库存-盘亏"),
    STOCK_OUT(7, "出库"),
    ;

    AdjustEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getName() {
        return name;
    }
}
