package com.hvisions.wms.enums;

import com.hvisions.common.interfaces.IKeyValueObject;

/**
 * <p>Title: ClassProperty</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
public enum ClassPropertyDataTypeEnum {

    //数据类型
    STRING(1),
    LONG(2),
    FLOAT(3);
    int code;

    ClassPropertyDataTypeEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static ClassPropertyDataTypeEnum getByCode(int code) {
        for (ClassPropertyDataTypeEnum value : values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return ClassPropertyDataTypeEnum.STRING;
    }
}