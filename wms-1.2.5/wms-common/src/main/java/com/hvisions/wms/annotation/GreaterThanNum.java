package com.hvisions.wms.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <p>Title: MoreThanNum</p >
 * <p>Description: 自定义数字大于0注解</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/10/13</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER, ElementType.TYPE_USE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = GreaterThanNumValidator.class)
@Documented
public @interface GreaterThanNum {
    String message() default "";

    double value() default 0.0;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
