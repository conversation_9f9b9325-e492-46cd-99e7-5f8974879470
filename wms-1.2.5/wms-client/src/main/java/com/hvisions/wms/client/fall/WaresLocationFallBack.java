package com.hvisions.wms.client.fall;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.WaresLocationClient;
import com.hvisions.wms.dto.location.WareLocationInfoDTO;
import com.hvisions.wms.dto.location.WaresLocationDTO;
import com.hvisions.wms.dto.location.WaresLocationQueryDTO;
import com.hvisions.wms.dto.location.WaresLocationRuleDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-26 9:03
 */
@Component
public class WaresLocationFallBack extends BaseFallbackFactory<WaresLocationClient> {
    @Override
    public WaresLocationClient getFallBack(ResultVO vo) {
        return new WaresLocationClient() {
            @Override
            public ResultVO<WaresLocationDTO> createWaresLocation(WaresLocationDTO waresLocationDTO) {
                return vo;
            }

            @Override
            public ResultVO<WaresLocationDTO> updateWaresLocation(WaresLocationDTO waresLocationDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<WaresLocationDTO>> getLocationByQuery(WaresLocationQueryDTO waresLocationQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<WaresLocationDTO>> findAllByQuery(WaresLocationQueryDTO waresLocationQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteWareLocation(int id) {
                return vo;
            }

            @Override
            public ResultVO createLocationRule(WaresLocationRuleDTO waresLocationRuleDTO) {
                return vo;
            }

            @Override
            public ResultVO updateLocationRule(WaresLocationRuleDTO waresLocationRuleDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<WaresLocationRuleDTO>> getRuleByLocationId(int locationId) {
                return vo;
            }

            @Override
            public ResultVO deleteLocationRule(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<List<Integer>> getStockLocation(Integer locationId) {
                return vo;
            }

            @Override
            public ResultVO<List<WaresLocationDTO>> getAllNotInZero() {
                return vo;
            }

            @Override
            public ResultVO<Integer> getLocationId(String locationCode) {
                return vo;
            }

            @Override
            public ResultVO<WaresLocationDTO> getByCode(String code) {
                return vo;
            }

            @Override
            public ResultVO<WareLocationInfoDTO> getWareLocationInfoByLocationCode(String locationCode) {return vo;}

            @Override
            public ResultVO getById(Integer Id) {
                return vo;
            }
        };
    }
}
