package com.hvisions.wms.client.fall;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;

import com.hvisions.wms.client.HvWmRcsReportedTaskClient;
import com.hvisions.wms.dto.rcs.HvWmRcsReportedTaskRecord;

public class HvWmRcsReportedTaskFallback extends BaseFallbackFactory<HvWmRcsReportedTaskClient> {
    @Override
    public HvWmRcsReportedTaskClient getFallBack(ResultVO vo) {
        return new HvWmRcsReportedTaskClient() {


            @Override
            public ResultVO searchById(String id) {
                return vo;
            }

            @Override
            public void addRecord(HvWmRcsReportedTaskRecord hvWmRcsReportedTaskRecord) {

            }

            @Override
            public void updateRecord(HvWmRcsReportedTaskRecord hvWmRcsReportedTaskRecord) {

            }

            @Override
            public void delRecord(HvWmRcsReportedTaskRecord hvWmRcsReportedTaskRecord) {

            }
        };
    }
}
