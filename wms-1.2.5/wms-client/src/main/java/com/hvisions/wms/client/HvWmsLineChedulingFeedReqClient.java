package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.LineSchedulingReceiveReqDTO;
import com.hvisions.wms.client.fall.HvWmsLineChedulingFeedReqFallBack;
import com.hvisions.wms.dto.rcs.HvWmsLineSchedulingFeedReq;
import com.hvisions.wms.dto.rcs.HvWmsLineSchedulingReceiveReqDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "wms",path = "/lineChedulingFeedReq",fallbackFactory = HvWmsLineChedulingFeedReqFallBack.class)
public interface HvWmsLineChedulingFeedReqClient {

    @PostMapping("/addLineChedulingFeedReq")
    @ApiOperation(value = "添加产线调度请求")
    ResultVO addLineChedulingFeedReq(@RequestBody HvWmsLineSchedulingFeedReq hvWmsLineSchedulingFeedReq);

    @PostMapping("/updateLineChedulingFeedReq")
    @ApiOperation(value = "更新产线调度请求")
    ResultVO updateLineChedulingFeedReq(@RequestBody HvWmsLineSchedulingFeedReq hvWmsLineSchedulingFeedReq);

    @PostMapping("/deleteLineChedulingFeedReq")
    @ApiOperation(value = "删除产线调度请求")
    ResultVO deleteLineChedulingFeedReqByRequestCode(@RequestParam("requestCode") String requestCode);

    @PostMapping("/getLineChedulingFeedReqByRequestCode")
    @ApiOperation(value = "根据请求编号获取产线调度请求")
    ResultVO getLineChedulingFeedReqByRequestCode(@RequestParam("requestCode") String requestCode) ;

    @PostMapping("/addLineSchedulingReceiveReq")
    @ApiOperation(value = "添加产线调度请求-新")
    ResultVO addLineSchedulingReceiveReq(@RequestBody LineSchedulingReceiveReqDTO lineSchedulingReceiveReqDTO);

    @PostMapping("/updateLineSchedulingReceiveReq")
    @ApiOperation(value = "更新产线调度请求-新")
    ResultVO updateLineSchedulingReceiveReq(@RequestBody LineSchedulingReceiveReqDTO lineSchedulingReceiveReqDTO);

    @PostMapping("/getLineSchedulingReceiveReqByRequestCode")
    @ApiOperation(value = "根据请求编号获取产线调度请求-新")
    ResultVO<LineSchedulingReceiveReqDTO> getLineSchedulingReceiveReqByRequestCode(@RequestParam("RequestCode") String RequestCode);

    @PostMapping("/deleteLineSchedulingReceiveReqByRequestCode")
    @ApiOperation(value = "删除产线调度请求-新")
    ResultVO deleteLineSchedulingReceiveReqByRequestCode(@RequestParam("RequestCode") String RequestCode );
    @PostMapping("/generateSchedulingTasks")
    @ApiOperation(value = "根据线体请求生成调度任务")
    ResultVO<ResultVO> generateSchedulingTasks(@RequestBody HvWmsLineSchedulingReceiveReqDTO lineSchedulingReceiveReqDTO);
}
