package com.hvisions.wms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.deliver.DeliverOrderDTO;
import com.hvisions.wms.dto.deliver.DeliverOrderLineDTO;
import com.hvisions.wms.dto.deliver.DeliverOrderQuery;
import com.hvisions.wms.dto.deliver.MergeDeliverOrderDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: DeliverOrderController</p>
 * <p>Description: 移库单控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/8</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "wms", path = "/deliverOrder")
public interface DeliverOrderClient {

    /**
     * 创建修改移库单(普通），只能选择“普通移库，或者其他类型”，具体的明细要手动录入
     *
     * @param deliverOrderDTO 移库单信息
     * @return 移库单id
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建移库单")
    ResultVO<Integer> create(@RequestBody DeliverOrderDTO deliverOrderDTO);

    /**
     * 查询移库单单分页数据信息
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/getPage")
    @ApiOperation(value = "查询移库单单分页数据信息")
    ResultVO<HvPage<DeliverOrderDTO>> getPage(@RequestBody DeliverOrderQuery query);

    /**
     * 创建库存合并移库单
     *
     * @param mergeDeliverOrderDTO 库存合并移库单信息
     * @return 移库单id
     */
    @PostMapping("/createCombineOrder")
    @ApiOperation(value = "创建库存合并移库单")
    ResultVO<Integer> createCombineOrder(@RequestBody MergeDeliverOrderDTO mergeDeliverOrderDTO);

    /**
     * 删除移库单
     *
     * @param id 移库单id
     * @return 执行结果
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除移库单")
    ResultVO delete(@PathVariable int id);

    /**
     * 获取移库单明细
     *
     * @param id 移库单id
     * @return 移库单明细列表
     */
    @GetMapping("/getLine/{id}")
    @ApiOperation(value = "获取移库单明细")
    ResultVO<List<DeliverOrderLineDTO>> getLine(@PathVariable int id);

    /**
     * 创建或者修改明细信息
     *
     * @param dto 明细信息
     * @return 明细id
     */
    @PostMapping("/createLine")
    @ApiOperation(value = "创建或者修改明细信息")
    ResultVO<Integer> createLine(@RequestBody DeliverOrderLineDTO dto);

    /**
     * 删除明细信息
     *
     * @param id 明细id
     * @return 执行结果
     */
    @DeleteMapping("/deleteLine/{id}")
    @ApiOperation(value = "删除明细")
    ResultVO deleteLine(@PathVariable int id);

    /**
     * 锁定库存，开始移库操作
     *
     * @param id 移库单id
     */
    @PutMapping("/lockStock/{id}")
    @ApiOperation(value = "锁定库存，开始移库操作")
    ResultVO lockStock(@PathVariable int id);

    /**
     * 标记明细已经完成移库操作
     *
     * @param id 明细id
     * @return 执行结果
     */
    @PutMapping("/finishLine/{id}")
    @ApiOperation(value = "标记明细已经完成移库操作")
    ResultVO finishLine(@PathVariable int id);

    /**
     * 完成移库单,提交数据，更新库存信息
     *
     * @param id 移库单id
     * @return 执行结果
     */
    @PutMapping("/finishOrder/{id}")
    @ApiOperation(value = "完成移库单操作")
    ResultVO finishOrder(@PathVariable int id);

}









