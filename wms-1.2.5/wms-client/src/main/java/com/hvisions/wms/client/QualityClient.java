package com.hvisions.wms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.QualityFallBack;
import com.hvisions.wms.dto.quality.QualityControlDTO;
import com.hvisions.wms.dto.quality.QualityDTO;
import com.hvisions.wms.dto.quality.QualityQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: QualityController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/3</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(value = "wms", path = "quality", fallbackFactory = QualityFallBack.class)
public interface QualityClient {

    /**
     * 创建请验单
     *
     * @param receiptId 收货单Id
     * @return 执行结果
     */
    @PostMapping(value = "/createQuality")
    @ApiOperation(value = "创建请验单")
    ResultVO createQuality(@RequestParam Integer receiptId, @RequestBody List<Integer> receiptMaterialIds);

    /**
     * 质检
     *
     * @param qualityControlDTOS 质检信息
     * @return 执行结果
     */
    @PutMapping(value = "/qualityControl")
    @ApiOperation(value = "质检")
    ResultVO qualityControl(@RequestBody List<QualityControlDTO> qualityControlDTOS);

    /**
     * 质检完成
     *
     * @param qualityId 请验单Id
     * @param inspector 执行人id
     * @return 执行结果
     */
    @PutMapping(value = "/finishQuality/{qualityId}/{inspector}")
    @ApiOperation(value = "提交质检结果(质检完成)")
    ResultVO finishQuality(@PathVariable Integer qualityId, @PathVariable Integer inspector);


    /**
     * 根据请验单查询项目
     *
     * @param qualityId 请验单ID
     * @return 检验货物信息
     */
    @GetMapping(value = "/getAllByQualityId/{qualityId}")
    @ApiOperation(value = "根据请验单查询项目")
    ResultVO<List<QualityControlDTO>> getAllByQualityId(@PathVariable Integer qualityId);

    /**
     * 查询请验单
     *
     * @param qualityQueryDTO 查询条件
     * @return 请验单
     */
    @PostMapping(value = "/getAllByQuery")
    @ApiOperation(value = "查询请验单")
    ResultVO<HvPage<QualityDTO>> getAllByQuery(@RequestBody QualityQueryDTO qualityQueryDTO);

}