package com.hvisions.wms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.OutOrderFallBack;
import com.hvisions.wms.dto.outstock.BaseOutStockDTO;
import com.hvisions.wms.dto.outstock.OutOrderAndLineDTO;
import com.hvisions.wms.dto.outstock.OutStockDTO;
import com.hvisions.wms.dto.outstock.OutStockQueryDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title: HistoryService</p >
 * <p>Description:出库单控制器 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@FeignClient(name = "wms", path = "/outStock", fallbackFactory = OutOrderFallBack.class)
public interface OutOrderClient {

    /**
     * 新建出库单
     *
     * @param baseOutStockDto 出库单新增对象
     * @return 主键
     */
    @PostMapping(value = "createOutStock")
    @ApiOperation(value = "新建出库单")
    ResultVO<Integer> createOutStock(@RequestBody BaseOutStockDTO baseOutStockDto);

    /**
     * 出库单和出库单详情新增
     *
     * @param outOrderAndLineDto 出库单和出库单详情新增对象
     * @return 出库单id
     */
    @PostMapping(value = "createOutStockAndMaterial")
    @ApiOperation(value = "出库单和出库单详情新增")
    ResultVO<Integer> createOutStockAndMaterial(@RequestBody OutOrderAndLineDTO outOrderAndLineDto);

    /**
     * 修改
     *
     * @param baseOutStockDto 出库单修改对象
     * @return 主键
     */
    @PutMapping(value = "updateOutStock")
    @ApiOperation(value = "修改出库单")
    ResultVO<Integer> updateOutStock(@RequestBody BaseOutStockDTO baseOutStockDto);

    /**
     * 查询出库单
     *
     * @param outStockQueryDTO 出库单查询对象
     * @return 出库单
     */
    @PostMapping(value = "getOutStock")
    @ApiOperation(value = "查询出库单")
    ResultVO<HvPage<OutStockDTO>> getOutStock(@RequestBody OutStockQueryDTO outStockQueryDTO);

    /**
     * 自动查找库位
     *
     * @param id           主键
     * @param stockWarning 是否报错
     * @return OutMaterialDTO
     */
    @PutMapping(value = "getOutStock")
    @ApiOperation(value = "自动查找库位")
    ResultVO autoFindStock(@RequestParam int id, @RequestParam int stockWarning);


    /**
     * 根据id查询
     *
     * @param id 主键
     * @return 出库单
     */
    @GetMapping(value = "findById/{id}")
    @ApiOperation(value = "根据id查询")
    ResultVO<OutStockDTO> findById(@PathVariable int id);


    /**
     * 根据出库单号查询
     *
     * @param code 出库单号
     * @return 出库单
     */
    @GetMapping(value = "findByCode/{code}")
    @ApiOperation(value = "根据出库单号查询")
    ResultVO<OutStockDTO> findByCode(@PathVariable String code);

    /**
     * 根据出库单号删除
     *
     * @param code 出库单号
     * @return 执行结果
     */
    @DeleteMapping(value = "deleteByCode/{code}")
    @ApiOperation(value = "根据出库单号删除")
    ResultVO deleteByCode(@PathVariable String code);

    /**
     * 根据id删除
     *
     * @param id 主键
     * @return 执行结果
     */
    @DeleteMapping(value = "deleteById/{id}")
    @ApiOperation(value = "根据id删除")
    ResultVO deleteById(@PathVariable Integer id);

    /**
     * 完成出库
     *
     * @param id 出料单id
     * @return 执行结果
     */
    @GetMapping(value = "finishStock/{id}")
    @ApiOperation(value = "完成出库")
    ResultVO finishStock(@PathVariable Integer id);

    /**
     * 出库完成后修改出库状态
     *
     * @param id    主键
     * @param state 状态
     */
    @PutMapping(value = "updateState")
    @ApiOperation(value = "修改出库状态")
    ResultVO updateState(@RequestParam Integer id,
                         @ApiParam(value = "类型(1:创建; 2:捡料中; 3:完成出库;4:审批完成;5:收料完成)") @RequestParam String state);


    /**
     * 修改审批状态
     *
     * @param id    主键
     * @param state 状态
     */
    @PutMapping(value = "approveOutOrder")
    @ApiOperation(value = "修改审批状态")
    ResultVO approveOutOrder(@RequestParam Integer id,
                             @ApiParam(value = "类型(审批通过;审批不通过;提交审批)") @RequestParam String state);
}
