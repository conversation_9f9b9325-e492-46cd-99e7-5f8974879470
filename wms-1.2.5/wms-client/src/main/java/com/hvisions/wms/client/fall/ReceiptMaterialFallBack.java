package com.hvisions.wms.client.fall;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.ReceiptMaterialClient;
import com.hvisions.wms.dto.receipt.ReceiptMaterialDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: ReceiptMaterialFallBack</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/22</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
public class ReceiptMaterialFallBack extends BaseFallbackFactory<ReceiptMaterialClient> {
    @Override
    public ReceiptMaterialClient getFallBack(ResultVO vo) {
        return new ReceiptMaterialClient() {
            @Override
            public ResultVO<Integer> createReceiptMaterial(ReceiptMaterialDTO receiptMaterialDTO) {
                return vo;
            }

            /**
             * 新增原材料收货信息
             *
             * @param receiptMaterialDTOS 收货单物料信息列表
             */
            @Override
            public ResultVO createReceiptMaterialList(List<ReceiptMaterialDTO> receiptMaterialDTOS) {
                return vo;
            }

            @Override
            public ResultVO updateReceiptMaterial(ReceiptMaterialDTO receiptMaterialDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<List<ReceiptMaterialDTO>> getAllByReceiptId(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<Map<Integer, String>> getReceiptMaterialType() {
                return vo;
            }

            @Override
            public ResultVO goShelf(Integer receiptId) {
                return vo;
            }
        };
    }
}