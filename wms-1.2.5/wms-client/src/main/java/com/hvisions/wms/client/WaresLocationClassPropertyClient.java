package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.location.WaresLocationClassPropertyDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: WaresLocationClassPropertyController</p >
 * <p>Description:仓储位置属性控制器 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/21</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(value = "wms",path = "/wares_class_property")
public interface WaresLocationClassPropertyClient {




    /**
     * 创建属性
     *
     * @param propertyDTO 属性
     * @return 主键
     */
    @PostMapping(value = "/create")
    @ApiOperation(value = "创建属性")
    ResultVO<Integer> create(@RequestBody WaresLocationClassPropertyDTO propertyDTO) ;

    /**
     * 更新属性
     *
     * @param propertyDTO 属性
     * @return 执行结果
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "更新属性")
    ResultVO update(@RequestBody WaresLocationClassPropertyDTO propertyDTO);

    /**
     * 删除属性
     *
     * @param id 主键
     * @return 执行结果
     */
    @DeleteMapping(value = "/deleteById/{id}")
    @ApiOperation(value = "删除属性")
    ResultVO deleteById(@PathVariable Integer id);

    /**
     * 获取属性
     *
     * @param id 仓储位置类型id
     * @return 仓储位置属性类型属性列表
     */
    @GetMapping(value = "/findByLocationClassId/{id}")
    @ApiOperation(value = "获取属性")
    ResultVO<List<WaresLocationClassPropertyDTO>> findByLocationClassId(@PathVariable Integer id);
}