package com.hvisions.wms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.transfer.TransferBoxDTO;
import com.hvisions.wms.dto.transfer.TransferBoxDetailDTO;
import com.hvisions.wms.dto.transfer.TransferBoxQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>Title: TransferBoxController</p>
 * <p>Description: 中转箱控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/8/26</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(value = "wms",path = "/transferBox")
public interface TransferBoxClient {
    /**
     * 新增中转箱
     *
     * @param transferBoxDTO 中转箱信息
     * @return 主键
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增")
    ResultVO<Integer> create(@RequestBody @Valid TransferBoxDTO transferBoxDTO);

    /**
     * 修改中转箱信息
     *
     * @param transferBoxDTO 中转箱信息
     * @return 主键
     */
    @PutMapping("/update")
    @ApiOperation(value = "修改")
    ResultVO<Integer> update(@RequestBody @Valid TransferBoxDTO transferBoxDTO) ;

    /**
     * 根据id获取中转箱信息
     *
     * @param id 主键
     * @return 中转箱信息
     */
    @GetMapping("/findById/{id}")
    @ApiOperation(value = "获取中转箱信息")
    ResultVO<TransferBoxDTO> findById(@PathVariable Integer id);

    /**
     * 根据编号获取中转箱信息
     *
     * @param code 中转箱编号
     * @return 中转箱信息
     */
    @GetMapping("/findByCode/{code}")
    @ApiOperation(value = "根据编号获取中转箱信息")
    ResultVO<TransferBoxDTO> findByCode(@PathVariable String code);

    /**
     * 删除中转箱信息
     *
     * @param id 主键
     * @return 执行结果
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除中转箱信息")
    ResultVO delete(@PathVariable int id);

    /**
     * 获取中转箱分页数据
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/getPage")
    @ApiOperation(value = "获取中转箱分页数据")
    ResultVO<HvPage<TransferBoxDTO>> getPage(@RequestBody TransferBoxQuery query);


    /**
     * 添加中转箱物料
     *
     * @param transferBoxDetailDTO 中转箱物料
     * @return 中转物料id
     */
    @PostMapping("/addMaterial")
    @ApiOperation(value = "添加中转箱物料")
    ResultVO<Integer> addMaterial(@RequestBody @Valid TransferBoxDetailDTO transferBoxDetailDTO);
    /**
     * 移除中转箱物料
     *
     * @param id 物料信息主键
     * @return 执行结果
     */
    @DeleteMapping("/removeMaterial/{id}")
    @ApiOperation(value = "移除中转箱物料")
    ResultVO removeMaterial(@PathVariable int id);

    /**
     * 清空中转箱数据
     *
     * @param boxId 中转箱id
     * @return 执行结果
     */
    @DeleteMapping("/clearBox/{boxId}")
    @ApiOperation(value = "清除中转箱所有物料")
    ResultVO clearBox(@PathVariable int boxId);
}









