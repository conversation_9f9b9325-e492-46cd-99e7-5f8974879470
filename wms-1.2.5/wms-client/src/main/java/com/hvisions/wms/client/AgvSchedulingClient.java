package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.AgvSchedulingClientFallBack;
import com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingDTO;
import com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingQueryDTO;
import com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingShowDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title: AgvSchedulingClient</p>
 * <p>Description: AGV调度任务远程调用接口</p>
 * <p>Company: www.h-visions.com</p>
 */
@FeignClient(name = "wms", path = "/hvWmAgvScheduling", fallbackFactory = AgvSchedulingClientFallBack.class)
public interface AgvSchedulingClient {

    @PostMapping("/getPage")
    @ApiOperation("分页查询AGV调度任务")
    ResultVO<Page<HvWmAgvSchedulingShowDTO>> getPage(@RequestBody HvWmAgvSchedulingQueryDTO queryDTO);

    @GetMapping("/getSchedulingById/{id}")
    @ApiOperation("根据ID查询AGV调度任务")
    ResultVO<HvWmAgvSchedulingShowDTO> getSchedulingById(@PathVariable Integer id);

    @GetMapping("/getSchedulingByTaskNo/{taskNo}")
    @ApiOperation("根据TaskNo查询AGV调度任务")
    ResultVO<HvWmAgvSchedulingShowDTO> getSchedulingByTaskNo(@PathVariable String taskNo);

    @PostMapping("/addScheduling")
    @ApiOperation("添加AGV调度任务")
    ResultVO addScheduling(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO);

    @DeleteMapping("/delScheduling/{id}")
    @ApiOperation("删除AGV调度任务")
    ResultVO<Void> delScheduling(@PathVariable Integer id);

    @PutMapping("/updateScheduling")
    @ApiOperation("更新AGV调度任务")
    ResultVO updateScheduling(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO);

    @ApiOperation("AGV任务下发")
    @PostMapping("/agvTaskDispatch")
    ResultVO agvTaskDispatch(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO);

    @ApiOperation("AGV任务新增并且下发")
    @PostMapping("/agvTaskAddAndDispatch")
    ResultVO agvTaskAddAndDispatch(@RequestBody HvWmAgvSchedulingDTO agvSchedulingDTO);

}