package com.hvisions.wms.client.fall;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.AgvSchedulingClient;
import com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingDTO;
import com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingQueryDTO;
import com.hvisions.wms.dto.agvScheduling.HvWmAgvSchedulingShowDTO;
import org.springframework.stereotype.Component;
import org.springframework.data.domain.Page;

/**
 * <p>Title: AgvSchedulingClientFallBack</p>
 * <p>Description: AGV调度任务服务降级处理</p>
 * <p>Company: www.h-visions.com</p>
 */
@Component
public class AgvSchedulingClientFallBack extends BaseFallbackFactory<AgvSchedulingClient> {

    @Override
    public AgvSchedulingClient getFallBack(ResultVO vo) {
        return new AgvSchedulingClient() {

            @Override
            public ResultVO<Page<HvWmAgvSchedulingShowDTO>> getPage(HvWmAgvSchedulingQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvWmAgvSchedulingShowDTO> getSchedulingById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<HvWmAgvSchedulingShowDTO> getSchedulingByTaskNo(String taskNo) {
                return vo;
            }

            @Override
            public ResultVO addScheduling(HvWmAgvSchedulingDTO agvSchedulingDTO) {
                return vo;
            }

            @Override
            public ResultVO<Void> delScheduling(Integer id) {
                return vo;
            }

            @Override
            public ResultVO updateScheduling(HvWmAgvSchedulingDTO agvSchedulingDTO) {
                return vo;
            }

            @Override
            public ResultVO agvTaskDispatch(HvWmAgvSchedulingDTO agvSchedulingDTO) {
                return vo;
            }

            @Override
            public ResultVO agvTaskAddAndDispatch(HvWmAgvSchedulingDTO agvSchedulingDTO) {
                return vo;
            }

        };
    }
}