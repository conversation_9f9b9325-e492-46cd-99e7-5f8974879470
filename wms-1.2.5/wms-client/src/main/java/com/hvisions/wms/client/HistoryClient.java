package com.hvisions.wms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.StockInfoDTO;
import com.hvisions.wms.client.fall.HistoryFallBack;
import com.hvisions.wms.dto.history.HistoryDTO;
import com.hvisions.wms.dto.history.QueryHistory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>Title: HistoryController</p >
 * <p>Description: 库存历史单控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(name = "wms",path = "/history",fallbackFactory = HistoryFallBack.class)
public interface HistoryClient {

    /**
     * 获取历史记录
     *
     * @return 历史记录信息
     */
    @PostMapping(value = "/getHistoryByQuery")
    @ApiOperation(value = "获取历史记录")
    ResultVO<HvPage<HistoryDTO>> getHistoryByQuery(@RequestBody QueryHistory queryHistory);


    /**
     * 库存同步异常信息存入redis
     * @return 历史记录
     */
    @PostMapping(value = "/stockSendFailedSaveRedis")
    @ApiOperation(value = "手动库存同步")
    ResultVO stockSendFailedSaveRedis(@RequestBody StockInfoDTO stockInfoDTO);

}