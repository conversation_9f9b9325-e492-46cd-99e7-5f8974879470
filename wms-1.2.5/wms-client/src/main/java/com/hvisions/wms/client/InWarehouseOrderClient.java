package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.InWarehouseOrderFallBack;
import com.hvisions.wms.dto.inwarehouseorder.InStoreDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;


@FeignClient(value = "wms", path = "/inWarehouseOrder",fallbackFactory = InWarehouseOrderFallBack.class)
public interface InWarehouseOrderClient {
    @PostMapping("/addStoreManual")
    @ApiOperation("手动添加入库信息")
    ResultVO<Integer> addStoreManual(@RequestBody @Valid InStoreDTO dto);
    @PutMapping("/inStore/{id}")
    @ApiOperation("完成入库")
    ResultVO inStore(@PathVariable Integer id);


}
