package com.hvisions.wms.client.fall;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.InWarehouseOrderClient;
import com.hvisions.wms.dto.inwarehouseorder.InStoreDTO;
import org.springframework.stereotype.Component;

/**
 * <p>Title: OutOrderFallBack</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/23</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
public class InWarehouseOrderFallBack extends BaseFallbackFactory<InWarehouseOrderClient> {
    @Override
    public InWarehouseOrderClient getFallBack(ResultVO vo) {
        return new InWarehouseOrderClient() {
            @Override
            public ResultVO<Integer> addStoreManual(InStoreDTO dto){
                return vo;
            }

            @Override
            public  ResultVO inStore(Integer id){
                return vo;
            }
        };
    }
}
