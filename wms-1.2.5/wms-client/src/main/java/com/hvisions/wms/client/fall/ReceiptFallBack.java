package com.hvisions.wms.client.fall;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.ReceiptClient;
import com.hvisions.wms.dto.receipt.BaseReceiptDTO;
import com.hvisions.wms.dto.receipt.ReceiptDTO;
import com.hvisions.wms.dto.receipt.ReceiptQueryDTO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <p>Title: ReceiptFallBackFactory</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/22</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
public class ReceiptFallBack extends BaseFallbackFactory<ReceiptClient> {
    @Override
    public ReceiptClient getFallBack(ResultVO vo) {
        return new ReceiptClient() {
            @Override
            public ResultVO<Integer> createReceipt(BaseReceiptDTO baseReceiptDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateReceipt(BaseReceiptDTO baseReceiptDTO) {
                return vo;
            }

            @Override
            public ResultVO<Page<ReceiptDTO>> getReceiptByQuery(ReceiptQueryDTO receiptQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteReceiptById(int id) {
                return vo;
            }

            @Override
            public ResultVO<Map<Integer, String>> getReceiptState() {
                return vo;
            }

            @Override
            public ResultVO<Map<Integer, String>> getReceiptType() {
                return vo;
            }
        };
    }
}