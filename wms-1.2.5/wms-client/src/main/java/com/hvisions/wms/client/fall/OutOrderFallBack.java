package com.hvisions.wms.client.fall;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.OutOrderLineClient;
import com.hvisions.wms.dto.outstock.OutLineDTO;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: OutOrderFallBack</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/23</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
public class OutOrderFallBack extends BaseFallbackFactory<OutOrderLineClient> {
    @Override
    public OutOrderLineClient getFallBack(ResultVO vo) {
        return new OutOrderLineClient() {
            @Override
            public ResultVO<Integer> createReceiptMaterial(OutLineDTO outLineDTO) {
                return vo;
            }

            @Override
            public ResultVO createOutLine(List<OutLineDTO> outLineDTOS) {
                return vo;
            }

            @Override
            public ResultVO autoCreateLine(Integer stockId, Integer orderId, BigDecimal quality) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateReceiptMaterial(OutLineDTO outLineDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteById(int id) {
                return vo;
            }

            @Override
            public ResultVO<List<OutLineDTO>> findStock(int id) {
                return vo;
            }

            @Override
            public ResultVO setStockState(int id) {
                return vo;
            }

            @Override
            public ResultVO pickMaterial(int id) {
                return vo;
            }

            @Override
            public ResultVO resetStock(int id) {
                return vo;
            }

            @Override
            public ResultVO deleteMaterialById(int id) {
                return vo;
            }
        };
    }
}