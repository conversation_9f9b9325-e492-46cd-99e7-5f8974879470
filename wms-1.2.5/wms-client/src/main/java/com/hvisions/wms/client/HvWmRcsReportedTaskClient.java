package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;

import com.hvisions.wms.client.fall.HvWmRcsReportedTaskFallback;
import com.hvisions.wms.dto.rcs.HvWmRcsReportedTaskRecord;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "wms",path = "/rcsReport",fallbackFactory = HvWmRcsReportedTaskFallback.class)
public interface HvWmRcsReportedTaskClient {

    @PostMapping("/rcs/findRecord")
    ResultVO searchById(@RequestParam("id") String id);

    @PostMapping("/rcs/addRecord")
    void addRecord(@RequestBody HvWmRcsReportedTaskRecord hvWmRcsReportedTaskRecord);

    @PostMapping("/rcs/updateRecord")
    void updateRecord(@RequestBody HvWmRcsReportedTaskRecord hvWmRcsReportedTaskRecord);

    @PostMapping("/rcs/delRecord")
    void delRecord(@RequestBody HvWmRcsReportedTaskRecord hvWmRcsReportedTaskRecord);

}