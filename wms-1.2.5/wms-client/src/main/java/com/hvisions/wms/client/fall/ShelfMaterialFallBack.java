package com.hvisions.wms.client.fall;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.ShelfMaterialClient;
import com.hvisions.wms.dto.receipt.ShelfMaterialDTO;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: ShelfMaterialFallBack</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/22</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
public class ShelfMaterialFallBack extends BaseFallbackFactory<ShelfMaterialClient> {
    @Override
    public ShelfMaterialClient getFallBack(ResultVO vo) {
        return new ShelfMaterialClient() {
            @Override
            public ResultVO setShelfState(int id, int state) {
                return vo;
            }

            @Override
            public ResultVO<Boolean> validStock(String materialBatchNum, BigDecimal receiptCount) {
                return vo;
            }

            @Override
            public ResultVO putShelf(ShelfMaterialDTO shelfMaterialDTOS) {
                return vo;
            }

            @Override
            public ResultVO putShelf(List<ShelfMaterialDTO> shelfMaterialDTOS) {
                return vo;
            }

            @Override
            public ResultVO allPut(Integer locationId, Integer receiptId) {
                return vo;
            }

            @Override
            public ResultVO finishShelf(Integer shelfId) {
                return vo;
            }

            @Override
            public ResultVO finishShelfAndInStock(Integer receiptId) {
                return vo;
            }

            @Override
            public ResultVO deleteById(Integer shelfId) {
                return vo;
            }

            @Override
            public ResultVO<List<ShelfMaterialDTO>> getAllByReceiptId(int receiptId) {
                return vo;
            }

            @Override
            public ResultVO resetShelf(Integer receiptId) {
                return vo;
            }
        };
    }
}