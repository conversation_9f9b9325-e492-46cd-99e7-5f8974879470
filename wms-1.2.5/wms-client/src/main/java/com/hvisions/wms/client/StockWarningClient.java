package com.hvisions.wms.client;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.stockWarning.BaseStockWarning;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;

/**
 * <p>Title: StockWarningController</p >
 * <p>Description: 储位库存报警</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date:  2020/9/7</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */

@FeignClient(value = "wms",path = "/stockWarning")
public interface StockWarningClient {

    /**
     * 获取库位库存警告详细信息
     * @return BaseStockWarning
     */
    @GetMapping(value = "/getStockWarning")
    @ApiOperation(value = "获取库位库存警告详细信息")
    ResultVO<List<BaseStockWarning>> getStockWarning();


    /**
     * 导出报警库存信息
     *
     * @return 供应商信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportLinker")
    @ApiOperation(value = "导出报警库存信息")
    ResultVO<ExcelExportDto> exportLinker() throws IOException, IllegalAccessException;
}
