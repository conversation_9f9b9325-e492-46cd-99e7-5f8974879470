package com.hvisions.wms.client.fall;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.QualityClient;
import com.hvisions.wms.dto.quality.QualityControlDTO;
import com.hvisions.wms.dto.quality.QualityDTO;
import com.hvisions.wms.dto.quality.QualityQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: QualityFallBack</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/22</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
public class QualityFallBack extends BaseFallbackFactory<QualityClient> {
    @Override
    public QualityClient getFallBack(ResultVO vo) {
        return new QualityClient() {
            @Override
            public ResultVO createQuality(Integer receiptId, List<Integer> receiptMaterialIds) {
                return vo;
            }

            @Override
            public ResultVO qualityControl(List<QualityControlDTO> qualityControlDTOS) {
                return vo;
            }

            @Override
            public ResultVO finishQuality(Integer qualityId, Integer inspector) {
                return vo;
            }

            @Override
            public ResultVO<List<QualityControlDTO>> getAllByQualityId(Integer qualityId) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<QualityDTO>> getAllByQuery(QualityQueryDTO qualityQueryDTO) {
                return vo;
            }
        };
    }
}