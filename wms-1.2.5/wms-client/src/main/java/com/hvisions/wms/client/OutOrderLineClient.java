package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.outstock.OutLineDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: HistoryService</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> liuwei
 * @version :1.0.0
 */
@FeignClient(name = "wms",path = "/outMaterial")
public interface OutOrderLineClient {


    /**
     * 新增出库单原料
     * @param outLineDTO 出料单原材料新增对象
     * @return id
     */
    @PostMapping(value = "createReceiptMaterial")
    @ApiOperation(value = "新增出库单原料")
    ResultVO<Integer> createReceiptMaterial(@RequestBody OutLineDTO outLineDTO);

    /**
     * 批量新增出库单原料
     * @param outLineDTOS 出料单原材料新增对象
     * @return 执行结果
     */
    @PostMapping(value = "createOutLine")
    @ApiOperation(value = "批量新增出库单原料")
    ResultVO createOutLine(@RequestBody List<OutLineDTO> outLineDTOS);

    /**
     * 根据库存id、出库单id、出库单数量生成明细表
     * @param stockId 库位id
     * @param orderId 出库单id
     * @param quality 出库数量
     * @return 执行结果
     */
    @PutMapping(value = "autoCreateLine")
    @ApiOperation(value = "根据库存id、出库单id、出库单数量生成明细表")
    ResultVO autoCreateLine(@RequestParam Integer stockId,@RequestParam Integer orderId,
                        @RequestParam BigDecimal quality);
    /**
     * 修改出库单原料
     * @param outLineDTO 出料单原材料修改对象
     * @return id
     */
    @PutMapping(value = "updateReceiptMaterial")
    @ApiOperation(value = "修改出库单原料")
    ResultVO<Integer> updateReceiptMaterial(@RequestBody OutLineDTO outLineDTO);
    /**
     * 删除出库单原料
     * @param id 主键
     * @return 执行结果
     */
    @DeleteMapping(value = "deleteById/{id}")
    @ApiOperation(value = "删除出库单原料")
    ResultVO deleteById(@PathVariable int id);

    /**
     * 根据主键出库单id查找物料信息
     * @param id 出库单id
     * @return 出料单原材料新增
     */
    @GetMapping(value = "findStock/{id}")
    @ApiOperation(value = "根据主键出库单id查找物料信息")
    ResultVO<List<OutLineDTO>> findStock(@PathVariable int id);

    /**
     * 根据主键修改物料状态
     * @param id 出料单主键
     * @return 执行结果
     */
    @GetMapping(value = "setStockState")
    @ApiOperation(value = "物料完成出库")
    ResultVO setStockState(int id);

    /**
     * 捡料
     * @param id 出料单主键
     * @return 执行结果
     */
    @GetMapping(value = "pickMaterial/{id}")
    @ApiOperation(value = "捡料")
    ResultVO pickMaterial(@PathVariable int id);

    /**
     * 重置储位
     * @param id 储位id
     * @return 执行结果
     */
    @GetMapping(value = "resetStock/{id}")
    @ApiOperation(value = "重置储位")
    ResultVO resetStock(@PathVariable int id);

    /**
     * 根据主键删除物料详情
     * @param id 物料详情id
     * @return 执行结果
     */
    @DeleteMapping(value = "deleteMaterialById/{id}")
    @ApiOperation(value = "根据主键删除物料详情")
    ResultVO deleteMaterialById(@PathVariable int id);
}
