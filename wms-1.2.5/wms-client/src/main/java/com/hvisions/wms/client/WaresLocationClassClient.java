package com.hvisions.wms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.location.WaresClassQuery;
import com.hvisions.wms.dto.location.WaresLocationClassDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title: WaresLocationClassController</p >
 * <p>Description:仓储位置属性类型 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/21</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(value = "wms",path = "/wares_class")
public interface WaresLocationClassClient {


    /**
     * 创建仓储位置属性类型
     *
     * @param classDTO 属性类型
     * @return 主键
     */
    @PostMapping(value = "/create")
    @ApiOperation(value = "创建物料属性类型")
    ResultVO<Integer> create(@Valid @RequestBody WaresLocationClassDTO classDTO);

    /**
     * 修改仓储位置属性类型
     *
     * @param classDTO 属性类型
     * @return 执行结果
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "修改物料属性类型")
    ResultVO update(@Valid @RequestBody WaresLocationClassDTO classDTO);

    /**
     * 获取仓储位置属性类型
     *
     * @param id 属性类型主键
     * @return 属性类型
     */
    @GetMapping(value = "/findById/{id}")
    @ApiOperation(value = "根据ID获取仓储位置属性类型")
    ResultVO<WaresLocationClassDTO> findById(@PathVariable Integer id);

    /**
     * 获取仓储位置属性类型
     *
     * @param code 属性类型编码
     * @return 属性类型
     */
    @GetMapping(value = "/findByCode/{code}")
    @ApiOperation(value = "根据Code获取仓储位置属性类型")
    ResultVO<WaresLocationClassDTO> findByCode(@PathVariable String code);

    /**
     * 获取仓储位置属性类型分页数据
     *
     * @param query 属性类型编码
     * @return 属性类型分页数据
     */
    @PostMapping(value = "/findPage")
    @ApiOperation(value = "获取仓储位置属性类型分页数据")
    ResultVO<HvPage<WaresLocationClassDTO>> findPage(@RequestBody WaresClassQuery query);

    /**
     * 删除仓储位置属性类型
     *
     * @param id 属性类型
     * @return 执行结果
     */
    @DeleteMapping(value = "/deleteById/{id}")
    @ApiOperation(value = "删除仓储位置属性类型")
    ResultVO deleteById(@PathVariable Integer id);

    /**
     * 根据仓储位置id查询仓储位置属性列表
     *
     * @param id 仓储位置di
     * @return 仓储位置属性类型列表
     */
    @GetMapping(value = "/deleteById/{id}")
    @ApiOperation(value = "根据仓储位置id查询仓储位置属性列表")
    ResultVO<List<WaresLocationClassDTO>> findByLocationId(@PathVariable Integer id);

    /**
     * 向仓储位置添加仓储位置属性类型
     *
     * @param locationId 仓储位置di
     * @param classId    属性类型id
     * @return 执行结果
     */
    @PostMapping(value = "/addClassToLocation/{locationId}/{classId}")
    @ApiOperation(value = "向仓储位置添加仓储位置属性类型")
    ResultVO addClassToLocation(@PathVariable Integer locationId, @PathVariable Integer classId);

    /**
     * 删除仓储位置的仓储位置属性类型
     *
     * @param locationId 仓储位置di
     * @param classId    属性类型id
     * @return 执行结果
     */
    @DeleteMapping(value = "/removeClassToLocation/{locationId}/{classId}")
    @ApiOperation(value = "删除仓储位置的仓储位置属性类型")
    ResultVO removeClassToLocation(@PathVariable Integer locationId, @PathVariable Integer classId);
}