package com.hvisions.wms.client;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.stocktaking.StockTakingOrderDTO;
import com.hvisions.wms.dto.stocktaking.StockTakingOrderLineDTO;
import com.hvisions.wms.dto.stocktaking.StockTakingQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: StockTakingOrderController</p>
 * <p>Description: 盘点控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@FeignClient(value = "wms",path = "/stockTaskingOrder")
public interface StockTakingOrderClient {

    /**
     * 创建盘点单
     *
     * @param orderDTO 盘点单信息
     * @return 盘点单id
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建盘点单")
    ResultVO<Integer> create(@RequestBody StockTakingOrderDTO orderDTO);


    /**
     * 删除盘点单
     *
     * @param id 盘点单id
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除盘点单")
    ResultVO delete(@PathVariable int id);

    /**
     * 查找盘点单分页数据
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/findPage")
    @ApiOperation(value = "查找盘点单分页数据")
    ResultVO<HvPage<StockTakingOrderDTO>> findPage(@RequestBody StockTakingQuery query);


    /**
     * 查询明细
     *
     * @param id 盘点单id
     * @return 明细列表
     */
    @GetMapping("/findLine/{id}")
    @ApiOperation(value = "查询明细")
    ResultVO<List<StockTakingOrderLineDTO>> findLine(@PathVariable Integer id);


    /**
     * 排列盘点明细表
     *
     * @param id 盘点单id
     */
    @PostMapping("/sortOrderLine")
    @ApiOperation(value = "排列盘点明细表")
    ResultVO sortOrderLine(@RequestParam Integer id);


    /**
     * 开始盘点
     *
     * @param id 盘点单号
     */
    @PutMapping("/startTaking")
    @ApiOperation(value = "开始盘点")
    ResultVO startTaking(@RequestParam Integer id);

    /**
     * 设置明细检查无误(快捷操作)
     *
     * @param lineId 明细id
     */
    @PutMapping("/checkLine")
    @ApiOperation(value = "设置明细检查无误")
    ResultVO checkLine(@RequestParam Integer lineId);

    /**
     * 设置明细检查
     *
     * @param lineId   明细id
     * @param quantity 实际数量
     */
    @PutMapping("/updateLine")
    @ApiOperation(value = "设置明细检查无误")
    ResultVO updateLine(@RequestParam Integer lineId, @RequestParam BigDecimal quantity);

    /**
     * 结束盘点
     *
     * @param id 盘点单id
     */
    @PostMapping("/finishTaking")
    @ApiOperation(value = "结束盘点")
    ResultVO finishTaking(@RequestParam Integer id);


    /**
     * 导出盘点明细表
     *
     * @param id 盘点单Id
     * @return 盘点明细表
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportOrderLine/{id}")
    @ApiOperation(value = "导出盘点明细表")
    ResultVO<ResponseEntity<byte[]>> export(@PathVariable Integer id) throws IOException, IllegalAccessException;


    /**
     * 导出盘点明细表
     *
     * @param id 盘点单Id
     * @return 盘点明细表
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportStockLine/{id}")
    @ApiOperation(value = "导出盘点明细表")
    ResultVO<ExcelExportDto> exportStockLine(@PathVariable Integer id) throws IOException, IllegalAccessException;
}









