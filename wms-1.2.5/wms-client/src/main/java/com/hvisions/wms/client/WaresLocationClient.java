package com.hvisions.wms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.WaresLocationFallBack;
import com.hvisions.wms.dto.location.WareLocationInfoDTO;
import com.hvisions.wms.dto.location.WaresLocationDTO;
import com.hvisions.wms.dto.location.WaresLocationQueryDTO;
import com.hvisions.wms.dto.location.WaresLocationRuleDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: WareHouseLocationController</p >
 * <p>Description: 库存位置控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(value = "wms", path = "/warehouse", fallbackFactory = WaresLocationFallBack.class)
public interface WaresLocationClient {

    /**
     * 创建仓储位置
     *
     * @param waresLocationDTO 仓储位置信息
     * @return 执行结果
     */
    @PostMapping(value = "/createWaresLocation")
    @ApiOperation(value = "创建仓储位置")
    ResultVO<WaresLocationDTO> createWaresLocation(@RequestBody WaresLocationDTO waresLocationDTO);

    /**
     * 更新仓储位置
     *
     * @param waresLocationDTO 仓储位置信息
     * @return 执行结果
     */
    @PutMapping(value = "/updateWaresLocation")
    @ApiOperation(value = "更新仓储位置")
    ResultVO<WaresLocationDTO> updateWaresLocation(@RequestBody WaresLocationDTO waresLocationDTO);

    /**
     * 分页查询库存位置
     *
     * @param waresLocationQueryDTO 查询条件
     * @return 库存位置信息
     */
    @PostMapping(value = "/getLocationByQuery")
    @ApiOperation(value = "分页查询库存位置")
    ResultVO<HvPage<WaresLocationDTO>> getLocationByQuery(@RequestBody WaresLocationQueryDTO waresLocationQueryDTO);


    /**
     * 查询所有库存
     *
     * @param waresLocationQueryDTO 查询条件
     * @return 库存信息
     */
    @PostMapping(value = "/findAllByQuery")
    @ApiOperation(value = "查询所有库存")
    ResultVO<List<WaresLocationDTO>> findAllByQuery(@RequestBody WaresLocationQueryDTO waresLocationQueryDTO);

    /**
     * 删除仓储位置
     *
     * @param id 仓储位置信息ID
     * @return 执行结果
     */
    @DeleteMapping(value = "/deleteWareLocation/{id}")
    @ApiOperation(value = "删除仓储位置 同时删除库位储存规则")
    ResultVO deleteWareLocation(@PathVariable("id") int id);


    /**
     * 新增库位储存物料
     *
     * @param waresLocationRuleDTO 新增信息
     * @return 执行结果
     */
    @PostMapping(value = "/createLocationRule")
    @ApiOperation(value = "新增库位储存物料")
    ResultVO createLocationRule(@RequestBody WaresLocationRuleDTO waresLocationRuleDTO);

    /**
     * 更新库位储存物料
     *
     * @param waresLocationRuleDTO 库位储存信息信息
     * @return 执行结果
     */
    @PutMapping(value = "/updateLocationRule")
    @ApiOperation(value = "更新库位储存物料")
    ResultVO updateLocationRule(@RequestBody WaresLocationRuleDTO waresLocationRuleDTO);


    /**
     * 根据库存id查询库存可储存物料
     *
     * @param locationId 库存id
     * @return 库存可储存物料列表
     */
    @ApiOperation(value = "根据库存id查询库存可储存物料")
    @GetMapping(value = "/getRuleByLocationId/{locationId}")
    ResultVO<List<WaresLocationRuleDTO>> getRuleByLocationId(@PathVariable("locationId") int locationId);

    /**
     * 删除仓储位置可存储物料
     *
     * @param id id
     * @return 执行结果
     */
    @DeleteMapping(value = "/deleteLocationRule/{id}")
    @ApiOperation(value = "删除仓储位置可存储物料")
    ResultVO deleteLocationRule(@PathVariable("id") Integer id);

    /**
     * 递归查询仓储位置
     *
     * @param locationId 储位id
     * @return HvWmsWaresLocation
     */
    @ApiOperation(value = "递归查询仓储位置")
    @GetMapping(value = "/getStockLocation/{locationId}")
    ResultVO<List<Integer>> getStockLocation(@PathVariable("locationId") Integer locationId);

    /**
     * 查询所有父节点不为0的库存
     *
     * @return
     */
    @ApiOperation(value = "查询所有父节点不为0的库存")
    @GetMapping(value = "/getAllNotInZero")
    ResultVO<List<WaresLocationDTO>> getAllNotInZero();


    @ApiOperation(value = "查询仓储位置Id")
    @GetMapping(value = "/getLocationId/{locationCode}")
    ResultVO<Integer> getLocationId(@PathVariable("locationCode") String locationCode);


    @ApiOperation(value = "查询仓储信息")
    @GetMapping(value = "/getByCode/{code}")
    ResultVO<WaresLocationDTO> getByCode(@PathVariable String code);


    @ApiOperation(value = "根据库位编码查询库位库区信息")
    @GetMapping(value = "/getWareLocationInfoByLocationCode/{locationCode}")
    ResultVO<WareLocationInfoDTO> getWareLocationInfoByLocationCode(@PathVariable("locationCode") String locationCode);

    @ApiOperation(value = "根据仓库id查询仓储信息")
    @GetMapping(value = "/getById/{Id}")
    ResultVO<WaresLocationDTO> getById(@PathVariable Integer Id);

}