package com.hvisions.wms.client.fall;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.WaresLocationMaterialPointClient;
import com.hvisions.wms.dto.location.WaresLocationMaterialPointDTO;
import com.hvisions.wms.dto.location.WaresLocationMaterialPointSynchronizationDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * date 2024-06-25 17:11
 */
@Component
public class WaresLocationMaterialPointFallBack extends BaseFallbackFactory<WaresLocationMaterialPointClient> {

    @Override
    public WaresLocationMaterialPointClient getFallBack(ResultVO vo) {
        return new WaresLocationMaterialPointClient() {
            @Override
            public ResultVO<List<WaresLocationMaterialPointDTO>> getAll() {
                return vo;
            }

            @Override
            public ResultVO<WaresLocationMaterialPointDTO> getMaterialPointByLocationIdAndMaterialPointCode(WaresLocationMaterialPointDTO condition) {
                return vo;
            }

            /**
             * 添加
             *
             * @param waresLocationMaterialPoint
             */
            @Override
            public ResultVO add(WaresLocationMaterialPointDTO waresLocationMaterialPoint) {
                return vo;
            }

            @Override
            public ResultVO<Boolean> synchronizationBaseModule(List<WaresLocationMaterialPointSynchronizationDTO> synchronizationData, Integer typeCode) {
                return vo;
            }
        };
    }
}
