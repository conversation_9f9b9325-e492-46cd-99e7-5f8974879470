package com.hvisions.wms.client.fall;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.OutOrderClient;
import com.hvisions.wms.dto.outstock.BaseOutStockDTO;
import com.hvisions.wms.dto.outstock.OutOrderAndLineDTO;
import com.hvisions.wms.dto.outstock.OutStockDTO;
import com.hvisions.wms.dto.outstock.OutStockQueryDTO;
import org.springframework.stereotype.Component;

/**
 * <p>Title: OutOrderClientFallBack</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/12/16</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
public class OutOrderClientFallBack extends BaseFallbackFactory<OutOrderClient> {
    @Override
    public OutOrderClient getFallBack(ResultVO vo) {
        return new OutOrderClient() {
            @Override
            public ResultVO<Integer> createOutStock(BaseOutStockDTO baseOutStockDto) {
                return vo;
            }

            @Override
            public ResultVO<Integer> createOutStockAndMaterial(OutOrderAndLineDTO outOrderAndLineDto) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateOutStock(BaseOutStockDTO baseOutStockDto) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<OutStockDTO>> getOutStock(OutStockQueryDTO outStockQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO autoFindStock(int id, int stockWarning) {
                return vo;
            }

            @Override
            public ResultVO<OutStockDTO> findById(int id) {
                return vo;
            }

            @Override
            public ResultVO<OutStockDTO> findByCode(String code) {
                return vo;
            }

            @Override
            public ResultVO deleteByCode(String code) {
                return vo;
            }

            @Override
            public ResultVO deleteById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO finishStock(Integer id) {
                return vo;
            }

            @Override
            public ResultVO updateState(Integer id, String state) {
                return vo;
            }

            @Override
            public ResultVO approveOutOrder(Integer id, String state) {
                return vo;
            }
        };
    }
}