package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.ReceiptMaterialFallBack;
import com.hvisions.wms.dto.receipt.ReceiptMaterialDTO;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: HistoryService</p >
 * <p>Description:原材料收货单控制器 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */

@FeignClient(value = "wms", path = "/receiptMaterial", fallbackFactory = ReceiptMaterialFallBack.class)
public interface ReceiptMaterialClient {

    /**
     * 创建原材料收货单
     *
     * @param receiptMaterialDTO 收货单信息
     */
    @PostMapping(value = "/createReceiptMaterial")
    @ApiOperation(value = "创建原材料收货单")
    ResultVO<Integer> createReceiptMaterial(@RequestBody ReceiptMaterialDTO receiptMaterialDTO);

    /**
     * 新增原材料收货信息
     *
     * @param receiptMaterialDTOS 收货单物料信息列表
     */
    @PostMapping(value = "/createReceiptMaterialList")
    ResultVO createReceiptMaterialList(@RequestBody List<ReceiptMaterialDTO> receiptMaterialDTOS);

    /**
     * 修改原材料收货单
     *
     * @param receiptMaterialDTO 收货单信息
     */
    @PutMapping(value = "/updateReceiptMaterial")
    @ApiOperation(value = "修改原材料收货单")
    ResultVO updateReceiptMaterial(@RequestBody ReceiptMaterialDTO receiptMaterialDTO);


    /**
     * 根据主键删除原材料收货单
     *
     * @param id 主键
     */
    @DeleteMapping(value = "deleteById/{id}")
    @ApiOperation(value = "根据主键删除原材料收货单")
    ResultVO deleteById(@PathVariable Integer id);

    /**
     * 根据主键查询原材料收货单
     *
     * @param id 主键
     * @return 收货单物料
     */
    @GetMapping(value = "getAllByReceiptId/{id}")
    @ApiOperation(value = "根据收货单Id查询原材料收货单")
    ResultVO<List<ReceiptMaterialDTO>> getAllByReceiptId(@PathVariable Integer id);

    /**
     * 获取收货单类型
     *
     * @return 收货单状态键值对
     */
    @GetMapping("/getReceiptMaterialType")
    @ApiOperation(value = "获取收货单物料类型")
    ResultVO<Map<Integer, String>> getReceiptMaterialType();


    /**
     * 上架
     *
     * @param receiptId 收货单物料Id
     */
    @PostMapping(value = "/goShelf")
    @ApiOperation(value = "收货单物料准备上架")
    ResultVO goShelf(@RequestParam Integer receiptId);

}
