package com.hvisions.wms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.adjust.AdjustOrderDTO;
import com.hvisions.wms.dto.adjust.AdjustOrderLineDTO;
import com.hvisions.wms.dto.adjust.AdjustOrderQuery;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>Title: AdjustOrderController</p>
 * <p>Description: 调整单控制器</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/9/8</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "wms", path = "/stockTaskingOrder")
public interface AdjustOrderClient {
    /**
     * 添加修改调整单
     *
     * @param adjustOrderDTO 调整单信息
     * @return 调整单id
     */
    @PostMapping("/create")
    @ApiOperation(value = "添加修改调整单")
    ResultVO<Integer> create(@RequestBody AdjustOrderDTO adjustOrderDTO);


    /**
     * 删除调整单
     *
     * @param id 调整单id
     * @return 执行结果
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除调整单")
    ResultVO delete(@PathVariable int id);

    /**
     * 查询调整单分页数据信息
     *
     * @param query 查询条件
     * @return 分页数据
     */
    @PostMapping("/getPage")
    @ApiOperation(value = "查询调整单分页信息")
    ResultVO<HvPage<AdjustOrderDTO>> getPage(@RequestBody AdjustOrderQuery query);

    /**
     * 查询调整单明细信息
     *
     * @param id 调整单id
     * @return 明细信息
     */
    @GetMapping("/getLine/{id}")
    @ApiOperation(value = "查询调整单明细信息")
    ResultVO<List<AdjustOrderLineDTO>> getLine(@PathVariable int id);

    /**
     * 添加修改调整单明细信息
     *
     * @param lineDTO 明细信息
     * @return 明细id
     */
    @PostMapping("/createLine")
    @ApiOperation(value = "添加修改明细信息")
    ResultVO<Integer> createLine(@RequestBody AdjustOrderLineDTO lineDTO);

    /**
     * 删除明细信息
     *
     * @param id 明细id
     * @return 执行结果
     */
    @DeleteMapping("/deleteLine/{id}")
    @ApiOperation(value = "删除明细信息")
    ResultVO deleteLine(@PathVariable int id);

    /**
     * 确认调整单明细信息，并提交调整单，调整库存
     *
     * @param id 调整单id
     * @return 执行结果
     */
    @PutMapping("/confirm/{id}")
    @ApiOperation(value = "确认调整单信息，并调整库存")
    ResultVO confirm(@PathVariable int id);
}









