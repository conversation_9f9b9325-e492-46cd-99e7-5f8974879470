package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.ShelfMaterialFallBack;
import com.hvisions.wms.dto.receipt.ShelfMaterialDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: ShelfMaterialController</p >
 * <p>Description: 收货单物料上架控制器</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(value = "wms", path = "/shelfMaterial", fallbackFactory = ShelfMaterialFallBack.class)
public interface ShelfMaterialClient {

    /**
     * 修改原材料收货单状态
     *
     * @param id    主键
     * @param state 收货单状态
     */
    @GetMapping(value = "/setShelfState")
    @ApiOperation(value = "修改上架状态")
    ResultVO setShelfState(@RequestParam int id, @RequestParam int state);

    /**
     * 根据库位编号、上架数量做库存验证
     *
     * @param materialBatchNum 物料批次号
     * @param receiptCount     上架数量
     * @return 验证结果 0可以上架，1抵达仓库上限
     */
    @GetMapping(value = "validStock")
    @ApiOperation(value = "根据物料批次号、上架数量做仓储上限验证")
    ResultVO<Boolean> validStock(@RequestParam String materialBatchNum, @RequestParam BigDecimal receiptCount);

    /**
     * 上架、批量上架
     *
     * @param shelfMaterialDTOS 上架物料信息
     */
    @PostMapping(value = "/putShelf")
    @ApiOperation(value = "上架")
    ResultVO putShelf(@RequestBody ShelfMaterialDTO shelfMaterialDTOS);

    /**
     * 上架、批量上架
     *
     * @param shelfMaterialDTOS 上架物料信息
     */
    @PostMapping(value = "/putAllShelf")
    @ApiOperation(value = "批量上架")
    ResultVO putShelf(@RequestBody List<ShelfMaterialDTO> shelfMaterialDTOS);


    /**
     * 全部上架
     *
     * @param locationId 库位Id
     * @param receiptId  收货单ID
     */
    @PostMapping(value = "/allPut")
    @ApiOperation(value = "全部上架")
    ResultVO allPut(@RequestParam Integer locationId, @RequestParam Integer receiptId);

    /**
     * 完成上架
     *
     * @param shelfId 上架物料Id
     */
    @PutMapping(value = "/finishShelf")
    @ApiOperation(value = "完成上架")
    ResultVO finishShelf(@RequestParam Integer shelfId);


    /**
     * 完成上架入库
     *
     * @param receiptId 收货单Id
     */
    @PutMapping(value = "/finishShelfAndInStock")
    @ApiOperation(value = "完成上架入库")
    ResultVO finishShelfAndInStock(@RequestParam Integer receiptId);

    /**
     * 清楚上架物料信息
     *
     * @param shelfId 上架物料Id
     */
    @DeleteMapping(value = "/deleteById/{shelfId}")
    @ApiOperation(value = "清楚上架物料信息")
    ResultVO deleteById(@PathVariable Integer shelfId);

    /**
     * 根据收货单id查询上架物料信息
     *
     * @param receiptId 收货单Id
     * @return 上架物料信息
     */
    @GetMapping(value = "/getAllByReceiptId/{receiptId}")
    @ApiOperation(value = "根据收货单id查询上架物料信息")
    ResultVO<List<ShelfMaterialDTO>> getAllByReceiptId(@PathVariable int receiptId);

    /**
     * 上架重置
     *
     * @param receiptId 收货单Id
     */
    @PutMapping(value = "/resetShelf/{receiptId}")
    @ApiOperation(value = "上架重置")
    ResultVO resetShelf(@PathVariable Integer receiptId);
}