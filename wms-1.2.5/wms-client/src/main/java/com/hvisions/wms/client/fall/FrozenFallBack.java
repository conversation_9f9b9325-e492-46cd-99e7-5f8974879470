package com.hvisions.wms.client.fall;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.FrozenClient;
import com.hvisions.wms.dto.stock.FrozenMaterialDTO;
import com.hvisions.wms.dto.stock.FrozenMaterialQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: FrozenFallBack</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/22</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
public class FrozenFallBack extends BaseFallbackFactory<FrozenClient> {
    @Override
    public FrozenClient getFallBack(ResultVO vo) {
        return new FrozenClient() {
            @Override
            public ResultVO frozenMaterial(FrozenMaterialDTO materialDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteFrozenMaterial(int id) {
                return vo;
            }

            /**
             * 批量解冻物料
             *
             * @param idList id列表
             */
            @Override
            public ResultVO deleteFrozenMaterialByIdIn(List<Integer> idList) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<FrozenMaterialDTO>> getAllFrozenMaterialByQuery(FrozenMaterialQueryDTO frozenMaterialQueryDTO) {
                return vo;
            }
        };
    }
}