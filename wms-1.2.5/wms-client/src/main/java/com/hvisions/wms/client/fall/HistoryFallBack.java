package com.hvisions.wms.client.fall;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.StockInfoDTO;
import com.hvisions.wms.client.HistoryClient;
import com.hvisions.wms.dto.history.HistoryDTO;
import com.hvisions.wms.dto.history.QueryHistory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-07-09 13:49
 */
@Component
public class HistoryFallBack extends BaseFallbackFactory<HistoryClient> {

    @Override
    public HistoryClient getFallBack(ResultVO vo) {
        return new HistoryClient() {
            @Override
            public ResultVO<HvPage<HistoryDTO>> getHistoryByQuery(QueryHistory queryHistory) {
                return vo;
            }

            @Override
            public ResultVO stockSendFailedSaveRedis(StockInfoDTO stockInfoDTO) {
                return null;
            }

        };
    }
}
