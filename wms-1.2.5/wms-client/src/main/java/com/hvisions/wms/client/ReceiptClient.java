package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.ReceiptFallBack;
import com.hvisions.wms.dto.receipt.BaseReceiptDTO;
import com.hvisions.wms.dto.receipt.ReceiptDTO;
import com.hvisions.wms.dto.receipt.ReceiptQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <p>Title: HistoryService</p >
 * <p>Description:收货单控制器 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(value = "wms", path = "/receipt", fallbackFactory = ReceiptFallBack.class)
public interface ReceiptClient {


    /**
     * 创建收货单
     *
     * @param baseReceiptDTO 收货单信息
     */
    @PostMapping(value = "/createReceipt")
    @ApiOperation(value = "创建收货单")
    ResultVO<Integer> createReceipt(@RequestBody BaseReceiptDTO baseReceiptDTO);

    /**
     * 修改收货单
     *
     * @param baseReceiptDTO
     */
    @PutMapping(value = "/updateReceipt")
    @ApiOperation(value = "修改收货单")
    ResultVO<Integer> updateReceipt(@RequestBody BaseReceiptDTO baseReceiptDTO);

    /**
     * 查询收货单
     *
     * @param receiptQueryDTO
     * @return 收货单列表
     */
    @PostMapping(value = "/getReceiptByQuery")
    @ApiOperation(value = "查询收货单")
    ResultVO<Page<ReceiptDTO>> getReceiptByQuery(@RequestBody ReceiptQueryDTO receiptQueryDTO);

    /**
     * 删除收货单
     *
     * @param id 收货单ID
     */
    @DeleteMapping(value = "/deleteReceiptById/{id}")
    @ApiOperation(value = "根据Id删除收货单")
    ResultVO deleteReceiptById(@PathVariable int id);


    /**
     * 获取收货单状态
     *
     * @return 收货单状态键值对
     */
    @GetMapping("/getReceiptState")
    @ApiOperation(value = "获取收货单状态")
    ResultVO<Map<Integer, String>> getReceiptState();

    /**
     * 获取收货单类型
     *
     * @return 收货单状态键值对
     */
    @GetMapping("/getReceiptType")
    @ApiOperation(value = "获取收货单类型")
    ResultVO<Map<Integer, String>> getReceiptType();

}
