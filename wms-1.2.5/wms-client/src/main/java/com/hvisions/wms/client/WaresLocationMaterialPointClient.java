package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.WaresLocationMaterialPointFallBack;
import com.hvisions.wms.dto.location.WaresLocationMaterialPointDTO;
import com.hvisions.wms.dto.location.WaresLocationMaterialPointSynchronizationDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-25 17:11
 */
@FeignClient(name = "wms", path = "/waresLocationMaterialPoint", fallbackFactory = WaresLocationMaterialPointFallBack.class)
public interface WaresLocationMaterialPointClient {
    /**
     * 查询全部
     *
     * @return 列表
     */
    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/getAll")
    ResultVO<List<WaresLocationMaterialPointDTO>> getAll();

    /**
     * 根据库区id和库位编号查询
     */
    @ApiOperation("根据库区id和库位编号查询")
    @PostMapping("/getMaterialPointByLocationIdAndMaterialPointCode")
    ResultVO<WaresLocationMaterialPointDTO> getMaterialPointByLocationIdAndMaterialPointCode(@RequestBody WaresLocationMaterialPointDTO condition);

    /**
     * 添加
     *
     * @param waresLocationMaterialPoint
     */
    @ApiOperation(value = "添加")
    @PostMapping(value = "/add")
    ResultVO add(@RequestBody WaresLocationMaterialPointDTO waresLocationMaterialPoint);

    /**
     * 同步Base模块库区主数据
     *
     * @param synchronizationData 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "同步Base模块库区主数据")
    @PutMapping(value = "/synchronizationBaseModule")
    ResultVO<Boolean> synchronizationBaseModule(@RequestBody List<WaresLocationMaterialPointSynchronizationDTO> synchronizationData, @RequestParam("typeCode") Integer typeCode);
}
