package com.hvisions.wms.client.fall;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.thirdparty.common.dto.LineSchedulingReceiveReqDTO;
import com.hvisions.wms.client.HvWmsLineChedulingFeedReqClient;
import com.hvisions.wms.dto.rcs.HvWmsLineSchedulingFeedReq;
import com.hvisions.wms.dto.rcs.HvWmsLineSchedulingReceiveReqDTO;
import org.springframework.stereotype.Component;

@Component
public class HvWmsLineChedulingFeedReqFallBack extends BaseFallbackFactory<HvWmsLineChedulingFeedReqClient> {
    @Override
    public HvWmsLineChedulingFeedReqClient getFallBack(ResultVO vo) {
        return new HvWmsLineChedulingFeedReqClient() {

            @Override
            public ResultVO addLineChedulingFeedReq(HvWmsLineSchedulingFeedReq hvWmsLineSchedulingFeedReq) {
                return vo;
            }

            @Override
            public ResultVO updateLineChedulingFeedReq(HvWmsLineSchedulingFeedReq hvWmsLineSchedulingFeedReq) {
                return vo;
            }

            @Override
            public ResultVO deleteLineChedulingFeedReqByRequestCode(String requestCode) {
                return vo;
            }

            @Override
            public ResultVO getLineChedulingFeedReqByRequestCode(String requestCode) {
                return vo;
            }

            @Override
            public ResultVO addLineSchedulingReceiveReq(LineSchedulingReceiveReqDTO lineSchedulingReceiveReqDTO) {
                return vo;
            }

            @Override
            public ResultVO updateLineSchedulingReceiveReq(LineSchedulingReceiveReqDTO lineSchedulingReceiveReqDTO) {
                return vo;
            }

            @Override
            public ResultVO getLineSchedulingReceiveReqByRequestCode(String RequestCode) {
                return vo;
            }

            @Override
            public ResultVO deleteLineSchedulingReceiveReqByRequestCode(String RequestCode) {
                return vo;
            }
            @Override
            public ResultVO<ResultVO> generateSchedulingTasks(HvWmsLineSchedulingReceiveReqDTO lineSchedulingReceiveReqDTO) {
                return vo;
            }
        };
    }
}
