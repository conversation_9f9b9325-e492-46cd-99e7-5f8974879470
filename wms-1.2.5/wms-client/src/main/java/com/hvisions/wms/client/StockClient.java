package com.hvisions.wms.client;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.stock.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <p>Title: StockController</p >
 * <p>Description: 库存信息</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(value = "wms", path = "/stock")
public interface StockClient {

    /**
     * 查询库存信息
     *
     * @return 库存信息
     */
    @PostMapping(value = "/getAllByQuery")
    @ApiOperation(value = "查询库存信息")
    ResultVO<HvPage<StockMaterialDTO>> getAllByQuery(@RequestBody StockQueryDTO stockQueryDTO);


    /**
     * 导出库存信息
     *
     * @return 库存信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/export")
    @ApiOperation(value = "导出库存信息 支持超链接")
    ResultVO<ResponseEntity<byte[]>> export() throws IOException, IllegalAccessException;

    /**
     * 导出库存信息
     *
     * @return 供应商信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/exportLinker")
    @ApiOperation(value = "导出库存信息 ")
    ResultVO<ExcelExportDto> exportLinker() throws IOException, IllegalAccessException;

    /**
     * 根据库存id查询物料占用信息
     *
     * @param stockId 库存id
     * @return 物料占用信息
     */
    @GetMapping(value = "/getOccupy/{stockId}")
    @ApiOperation(value = "查询物料占用信息")
    ResultVO<List<StockOccupyDTO>> getOccupy(@PathVariable("stockId") Integer stockId);


    /**
     * 增加库存
     *
     * @param adjustStorageDTOS 库存增加信息列表
     * @return 执行结果
     */
    @PostMapping(value = "/addStorage")
    @ApiOperation(value = "批量增加库存")
    ResultVO addStorageList(@RequestBody List<AdjustStorageDTO> adjustStorageDTOS);

    /**
     * 减少库存
     *
     * @param adjustStorageDTOS 库存减少信息
     * @return 执行结果
     */
    @PostMapping(value = "/removeStorage")
    @ApiOperation(value = "批量减少库存")
    ResultVO removeStorage(@RequestBody @Valid List<AdjustStorageDTO> adjustStorageDTOS);

    /**
     * 占用库存
     *
     * @param adjustStorageDTOS 库存信息
     * @return 执行结果
     */
    @PostMapping(value = "/occupyStorage")
    @ApiOperation(value = "批量占用库存")
    ResultVO occupyStorage(@RequestBody @Valid List<AdjustStorageDTO> adjustStorageDTOS);

    /**
     * 取消占用，外部业务占用后，取消操作使用
     *
     * @param operation 操作
     * @param orderCode 业务号
     * @return 执行结果
     */
    @PostMapping(value = "/occupyCancel")
    @ApiOperation(value = "取消占用")
    ResultVO occupyCancel(@RequestParam("operation") String operation,@RequestParam("orderCode") String orderCode);

    /**
     * 核销占用
     *
     * @param operation 操作
     * @param orderCode 业务号
     * @return 执行结果
     */
    @PostMapping(value = "/occupyWriteOff")
    @ApiOperation(value = "核销库存")
    ResultVO occupyWriteOff(@RequestParam("operation") String operation,@RequestParam("orderCode") String orderCode);


    @ApiOperation(value = "物料库存新增")
    @PutMapping(value = "/updateMaterialStock")
    ResultVO updateMaterialStock(@RequestParam("orderCode") String orderCode,@RequestBody List<StockMaterialDTO> stockMaterials);

    /**
     * 删除物料库存（2024/4/30）
     */
    @ApiOperation(value = "删除物料库存")
    @DeleteMapping(value = "/deleteStock")
    ResultVO deleteStock(@RequestParam("orderCode") String orderCode,@RequestBody List<StockMaterialDTO> stockMaterials);


    @PostMapping(value = "/getStocks")
    @ApiOperation(value = "查询库存信息")
    ResultVO<List<StockMaterialDTO>> getStocks(@RequestBody StockQueryDTO stockQueryDTO);

    /**
     * 根据料框查询库存中所有的物料与数量
     *
     * @param frameCode 料框
     * @return 库存信息
     */
    @PostMapping(value = "/getStocksByFrame")
    @ApiOperation(value = "根据料框查询库存中所有的物料与数量")
    ResultVO<List<StockMaterialDTO>> getStocksByFrame(@RequestParam("frameCode") String frameCode);


    @ApiOperation(value = "删除物料占用")
    @DeleteMapping(value = "/occupyRemove")
    ResultVO occupyRemove(@RequestParam Integer occupyId);


    /**
     * 批量新增占用库存
     *
     * @param stockOccupyDTOS 库存信息
     */
    @PostMapping(value = "/batchAddOccupyStorage")
    @ApiOperation(value = "批量新增占用库存")
    ResultVO batchAddOccupyStorage(@RequestBody @Valid List<StockOccupyDTO> stockOccupyDTOS);

    /**
     * 批量修改占用库存
     * @param stockOccupyDTOS
     * @return
     */
    @PostMapping(value = "/batchUpOccupyStorage")
    @ApiOperation(value = "批量修改占用库存")
    ResultVO batchUpOccupyStorage(@RequestBody @Valid List<StockOccupyDTO> stockOccupyDTOS);

    /**
     * 根据库存id查询库存
     *
     * @param id 库存ID
     */
    @PostMapping(value = "/getStocksById")
    @ApiOperation(value = "批量新增占用库存")
    ResultVO<StockMaterialDTO>  getStocksById(@RequestParam("id") Integer id);


    /**
     *
     * @param materialCode
     * @param locationCode
     * @return
     */
    @PostMapping(value = "/getQuantityByMaterialCodeAndLocationCode")
    @ApiOperation(value = "根据库区编码和物料编码查询库存数量")
    ResultVO<StockMaterialPreparationDTO>  getQuantityByMaterialCodeAndLocationCode(@RequestParam("materialCode")  String materialCode, @RequestParam("locationCode") String locationCode);

    /**
     *
     * @param materialCode
     * @param locationCode
     * @return
     */
    @PostMapping(value = "/getUsedCountByMaterialCodeAndLocationCode")
    @ApiOperation(value = "根据库区编码和物料编码查询库存占用数量")
    ResultVO<StockMaterialPreparationDTO> getUsedCountByMaterialCodeAndLocationCode(@RequestParam("materialCode")  String materialCode, @RequestParam("locationCode") String locationCode);

    /**
     * 新增库存占用
     * @param stockOccupyDTO
     * @return
     */
    @PostMapping(value = "/addOccupyStock")
    @ApiOperation(value = "批量新增库存占用")
    ResultVO addOccupyStock(@RequestBody StockOccupyDTO stockOccupyDTO);


    /**
     * 修改库存占用
     * @param stockOccupyDTO
     * @return
     */
    @PostMapping(value = "/upOccupyStock")
    @ApiOperation(value = "修改库存占用")
    ResultVO upOccupyStock(@RequestBody StockOccupyDTO stockOccupyDTO);

    @GetMapping(value = "/getListByFrameCode")
    @ApiOperation(value = "根据料框编码获取库存数据")
    ResultVO<List<StockMaterialDTO>> getListByFrameCode(@RequestParam("frameCode") String frameCode);


    @GetMapping(value = "/getStockOccupyByOrderCodeAndMaterialCode/{orderCode}/{materialCode}")
    @ApiOperation(value = "根据工单号和物料编码查询库存占用信息")
    ResultVO<List<StockOccupyDTO>> getStockOccupyByOrderCodeAndMaterialCode(@PathVariable("orderCode")  String orderCode, @PathVariable("materialCode") String materialCode);

    @ApiOperation(value = "根据工单号删除库存占用")
    @DeleteMapping(value = "/deleteStockOccupyByOrderCode")
    ResultVO deleteStockOccupyByOrderCode(@RequestParam String orderCode);


    @DeleteMapping(value = "/deleteStockByFrameCode")
    ResultVO deleteStockByFrameCode(@RequestParam String palletCode);
}