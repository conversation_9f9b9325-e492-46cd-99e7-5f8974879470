package com.hvisions.wms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.client.fall.FrozenFallBack;
import com.hvisions.wms.dto.stock.FrozenMaterialDTO;
import com.hvisions.wms.dto.stock.FrozenMaterialQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: FrozenController</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/2</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(name = "wms", path = "/frozen", fallbackFactory = FrozenFallBack.class)
public interface FrozenClient {


    /**
     * 库存冻结
     *
     * @param materialDTO 冻结信息
     * @return 执行结果
     */
    @PostMapping(value = "/frozenMaterial")
    @ApiOperation(value = "库存冻结")
    ResultVO frozenMaterial(@RequestBody FrozenMaterialDTO materialDTO);


    /**
     * 解冻物料
     *
     * @param id 冻结信息Id
     * @return 执行结果
     */
    @ApiOperation(value = "解除库存冻结")
    @DeleteMapping(value = "/deleteFrozenMaterial/{id}")
    ResultVO deleteFrozenMaterial(@PathVariable int id);

    /**
     * 批量解冻物料
     *
     * @param idList id列表
     */
    @DeleteMapping(value = "/deleteFrozenMaterialByIdIn")
    ResultVO deleteFrozenMaterialByIdIn(@RequestBody List<Integer> idList);


    /**
     * 库存冻结查询
     *
     * @param frozenMaterialQueryDTO 查询条件
     * @return 返回值
     */
    @ApiOperation(value = "库存冻结查询")
    @PostMapping(value = "/getAllFrozenMaterialByQuery")
    ResultVO<HvPage<FrozenMaterialDTO>> getAllFrozenMaterialByQuery(@RequestBody FrozenMaterialQueryDTO frozenMaterialQueryDTO);


}