package com.hvisions.wms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.wms.dto.location.AddWaresLocationPropertyDTO;
import com.hvisions.wms.dto.location.WaresLocationPropertyDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: WaresLocationPropertyContonller</p >
 * <p>Description:仓储属性 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/7/21</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(value = "wms",path = "/wares_property")
public interface WaresLocationPropertyClient {

    /**
     * 修改仓储属性
     *
     * @param propertyDTOs 属性
     * @return 执行结果
     */
    @PutMapping(value = "/update")
    @ApiOperation(value = "修改仓储属性")
    ResultVO update(@RequestBody List<WaresLocationPropertyDTO> propertyDTOs);

    /**
     * 获取仓储属性
     *
     * @param id 仓储id
     * @return 仓储属性列表
     */
    @GetMapping(value = "/findByLocationId/{id}")
    @ApiOperation(value = "获取仓储属性")
    ResultVO<List<WaresLocationPropertyDTO>> findByLocationId(@PathVariable Integer id);


    /**
     * 添加仓储属性
     *
     * @param propertyDTO 仓储属性
     * @return 执行结果
     */
    @ApiOperation(value = "添加仓储属性")
    @PostMapping(value = "/addPropertyToLocations")
    ResultVO addPropertyToLocations(@RequestBody AddWaresLocationPropertyDTO propertyDTO);
    /**
     * 根据Id删除仓储属性
     *
     * @param id 仓储id
     * @return 执行结果
     */
    @DeleteMapping(value = "/deletePropertyById/{id}")
    @ApiOperation(value = "根据Id删除仓储属性")
    ResultVO deletePropertyById(@PathVariable int id);
}