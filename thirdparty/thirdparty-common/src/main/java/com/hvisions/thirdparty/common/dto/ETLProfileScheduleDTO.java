package com.hvisions.thirdparty.common.dto;

import lombok.Data;

/**
 * <P>型材切割线->进度、每日零件产量、计划零件产量 <P>
 *
 * <AUTHOR>
 * @date 2024/8/14
 */
@Data
public class ETLProfileScheduleDTO {
    private Long id;
    //产线
    private String lineCode;
    //日期
    private String statisticalDate;
    //进度(每日零件产量/计划零件产量)
    private Double schedule;
    //每日零件产量
    private Integer planQuantity;
    //计划零件产量
    private Integer finishQuantity;

}
