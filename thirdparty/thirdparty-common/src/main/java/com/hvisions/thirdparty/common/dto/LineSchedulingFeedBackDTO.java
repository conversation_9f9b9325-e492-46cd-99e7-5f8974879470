package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import java.util.List;

@Data
public class LineSchedulingFeedBackDTO {

    private String requestCode;//请求调度 时 线控系统生成 的 请求码
    private String frameCode;//料框编号
    private String frameTypeCode;//料框类型
    private String result;//OK/NG
    private String taskType;//任务类型
    private String pointCode;//料点
    private String processCode;//工序编号
    private String workOrderCode;//工单编号
    private Integer lineId;//产线id,通过请求系统编号，到第三方系统 找到对应产线
    private String startPointCode;//料点起点位置
    private List<MaterialFeedBackDTO> materialList;

}