package com.hvisions.thirdparty.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/5/23
 */
@Data
public class MaterialPreparationTaskDetail {
    // 任务号
    @J<PERSON><PERSON>ield(name = "taskCode")
    private String taskCode;

    // 工单编号
    @JSONField(name = "workOrderCode")
    private String workOrderCode;

    //型材物料编码
    @JSONField(name = "matCode")
    private String matCode;

    //型材原材数量
    @JSONField(name = "qty")
    private Integer qty;

    //型材原材规格
    @JSONField(name = "sepces")
    private String sepces;

    //备用1
    @JSONField(name = "udf1")
    private Integer udf1;

    //备用2
    @JSONField(name = "udf2")
    private Integer udf2;

    //备用3
    @JSONField(name = "udf3")
    private Integer udf3;

    //船号
    @ApiModelProperty(value = "船号")
    private String shipCode;

    //分段号
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;
}
