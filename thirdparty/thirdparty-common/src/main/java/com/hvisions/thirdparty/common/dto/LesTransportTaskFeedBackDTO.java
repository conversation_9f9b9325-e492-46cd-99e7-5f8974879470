package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
@Data
public class LesTransportTaskFeedBackDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工单编号
     */
    @NotBlank(message = "工单编号不能为空")
    @Size(max = 50,message = "工单编号长度不能超过{max}个字符")
    private String workOrderNumber;

    /**
     * 状态
     * 0：转运失败
     * 1：转运成功
     */
    @NotNull(message = "状态不能为空")
    @Digits(integer = 10, fraction = 0)
    private Integer status;

    /**
     * 反馈信息
     * status=0时，填写失败原因
     */
    @Size(max = 200,message = "反馈信息长度不能超过{max}个字符")
    private String msg;
}
