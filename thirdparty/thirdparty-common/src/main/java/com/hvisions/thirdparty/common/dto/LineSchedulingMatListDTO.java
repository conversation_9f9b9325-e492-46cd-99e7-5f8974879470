package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class LineSchedulingMatListDTO {
    /**
     * 物料编号
     * 功能说明: 满框调度时需包含物料信息
     */
    @NotBlank(message = "物料编号不能为空")
    @Size(max = 32, message = "物料编号长度不能超过32个字符")
    private String materialCode;

    /**
     * 物料名称
     * 功能说明: 满框调度时需包含物料信息
     */
    @NotBlank(message = "物料名称不能为空")
    @Size(max = 32, message = "物料名称长度不能超过32个字符")
    private String materialName;

    /**
     * 工单号
     * 功能说明: 满框调度时需包含物料信息
     */
    @NotBlank(message = "工单号不能为空")
    @Size(max = 32, message = "工单号长度不能超过32个字符")
    private String orderNumber;

    /**
     * 数量
     * 功能说明: 满框调度时需包含物料信息
     */
    private Integer quality;

    /**
     * 物料摆放顺序
     * 功能说明: 满框调度时需包含物料信息，从1开始，1为最底下，越往上数字越大，仅用于大板切割线大件（底板）下架
     */
    private Integer matOrder;

    private String requestCode;

}
