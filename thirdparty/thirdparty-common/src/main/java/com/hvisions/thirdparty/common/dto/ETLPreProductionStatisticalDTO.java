package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

//生产准备时间
@Data
public class ETLPreProductionStatisticalDTO  implements Serializable {

    private Long id;
    private String statisticalDate;

    private Double actValue;

    private String lineCode;		//产线

    private String equipmentCode;	//设备



}
