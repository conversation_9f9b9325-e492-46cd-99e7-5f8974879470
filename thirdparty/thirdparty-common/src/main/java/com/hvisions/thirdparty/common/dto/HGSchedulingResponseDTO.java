package com.hvisions.thirdparty.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class HGSchedulingResponseDTO {

    @JSONField(name = "no")
    private String no; // 唯一编号（唯一标识，匹配请求接口）

    @JSONField(name = "zk_code")
    private String zkCode; // 系统编号（每个编号代表不同的系统）

    @JSONField(name = "step")
    private String step; // 步骤（字典值：1-5，如上表含义）

    @JSONField(name = "type")
    private String type; // 1、预叫料 2、生产叫料 3、空托呼叫 、4.托盘回库 5、托盘转运

    @JSONField(name = "transfer_type")
    private String transferType; // 2级类型（托盘转运必传）

    @JSONField(name = "comple_time")
    private String completeTime; // 步骤完成时间

    @JSONField(name = "pallet_code")
    private String palletCode; // 托盘

    @JSONField(name = "bottom_pallet_code")
    private String bottomPalletCode; // 母托编号

    @JSONField(name = "status")
    private String status; // 托盘状态（1正常、3返修）

    @JSONField(name = "route_code")
    private String routeCode; // 作业路径

    @JSONField(name = "process_code")
    private String processCode; // 工序
}
