package com.hvisions.thirdparty.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class NestingMaterialFileDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private Integer id;

    private String materialCode;

    private String filePath;

    @JSONField(name = "marking_file_path")
    private String markingFilePath;

    private String shipNo;
}
