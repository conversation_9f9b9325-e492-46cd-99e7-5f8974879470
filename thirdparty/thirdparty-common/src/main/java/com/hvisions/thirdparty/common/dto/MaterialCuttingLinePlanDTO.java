package com.hvisions.thirdparty.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 型材切割计划
 */
@Data
public class MaterialCuttingLinePlanDTO {

    @NotNull
    @ApiModelProperty("请求码唯一")
    @JSONField(name = "id")
    private String id;

    @NotNull
    @ApiModelProperty("请求名称")
    @JSONField(name = "name")
    private String name;
    
    @NotNull
    @ApiModelProperty("请求时间戳")
    @JSONField(name = "timestamp")
    private Long timestamp;

    @NotNull
    @ApiModelProperty("工位编号")
    @JSONField(name = "station_id")
    private String station_id;

    @NotNull
    @ApiModelProperty("工单编号")
    @JSONField(name = "order_no")
    private String order_no;


    @ApiModelProperty("工单类型")
    @JSONField(name = "orderType")
    private String orderType;


    @ApiModelProperty("船型")
    @JSONField(name = "shipmode")
    private String shipmode;

    @ApiModelProperty("船号")
    @JSONField(name = "shipcode")
    private String shipcode;


    @ApiModelProperty("分段号")
    @JSONField(name = "segmentationCode")
    private String segmentationCode;


    @ApiModelProperty("数量")
    @JSONField(name = "qty")
    private String qty;

    @ApiModelProperty("计划完成时间")
    @JSONField(name = "prod_date",format = "yyyy-MM-dd HH:mm:ss")
    private Date prod_date;


    @ApiModelProperty("生产班组")
    @JSONField(name = "bc")
    private String bc;

    @ApiModelProperty("是否备料  1：备料中或备料完成、0：未开始备料")
    private String MatPrepFlag;


//
//    @NotNull
//    @ApiModelProperty("gen套料文件位置/工程号-分段号")
//    @JSONField(name = "order_loc")
//    private String order_loc;
//
//    @NotNull
//    @ApiModelProperty("工单数据，含多个gen套料文件名称")
//    @JSONField(name = "order_data")
//    private List<String> order_data;
    
}
