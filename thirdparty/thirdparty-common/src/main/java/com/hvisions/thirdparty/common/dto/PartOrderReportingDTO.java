package com.hvisions.thirdparty.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/15
 */
@Data
public class PartOrderReportingDTO {
    // 切割任务编号
    private String code;
    // 堆场出库开始时间
    private String outStartTime;
    // 堆场出库结束时间
    private String outEndTime;
    // 打码开始时间
    private String markStartTime;
    // 打码结束时间
    private String markEndTime;
    // 理料间入库开始时间
    private String inStockStartTime1;
    // 理料间入库结束时间
    private String inStockEndTime1;
    // 理料间出库开始时间
    private String outStockStartTime1;
    // 理料间出库结束时间
    private String outStockEndTime1;
    // 切割开始时间
    private String cuttingStartTime;
    // 切割结束时间
    private String cuttingEndTime;
    // 分拣开始时间
    private String pickStartTime;
    // 分拣结束时间
    private String pickEndTime;
    // 大件分拣开始时间
    private String bigPartpickStartTime;
    // 大件分拣结束时间
    private String bigPartpickEndTime;
    // 小件分拣开始时间
    private String smallPickStartTime;
    // 小件分拣结束时间
    private String smallPickEndTime;
    // 人工分拣开始时间
    private String artificialPickStartTime;
    // 人工分拣结束时间
    private String artificialPickEndTime;
    // 打码设备编号
    private String dev1Code;
    // 切割设备编号
    private String dev2Code;
    // 分拣设备编号
    private String dev3Code;
    // 钢板炉批号
    private String furnaceNo;
    // 原料库位编码
    private String pickingPositionCode;
    // 状态(1：堆场出库、2：钢板打码中、3：理料间入库、4：理料间出库、5：切割开始、6：切割结束、7：开始分拣、8：完成)
    private String status;
    // 产线
    private String lineCode;

    @ApiModelProperty(value = "物料信息")
    private List<SnDataDTO> snData;
}
