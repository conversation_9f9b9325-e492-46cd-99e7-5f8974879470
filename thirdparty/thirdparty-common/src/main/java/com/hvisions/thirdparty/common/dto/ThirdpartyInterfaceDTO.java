package com.hvisions.thirdparty.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ThirdpartyInterfaceDTO {

    protected Long id;
    //接口
    private String interfaceCode;
    //接口名称
    private String interfaceName;
    //对接系统
    private Integer systemId;
    //请求/反馈
    private Integer requestOrResponse;
    //队列
    private String queue;
    //目标 - imb mq 需要
    private String destination;
    //服务名称 - imb mq 需要
    private String serverName;

    //主题
    private String topic;
    //地址
    private String url;
    //请求类型
    @NotNull
    private Integer requestType;
    //通讯类型
    private Integer communicationType;
    //启用
    private Integer enable;
    //线体ID
    private Integer lineId;

}
