package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * RCS任务取消请求DTO
 */
@Data
public class RcsCancelTaskRequestDTO {
    /**
     * 任务号（全局唯一）
     */
    @NotBlank
    @Size(max = 64,message = "长度不能超过64个字符")
    private String robotTaskCode;

    /**
     * 取消类型：
     *  1.CANCEL  取消
     *  2.DROP 人工接入
     *  定义枚举类RcsTaskCancelTypeEnum
     */
    @NotBlank
    @Size(max = 16,message = "长度不能超过16个字符")
    private String cancelType;

    /**
     * 回库载具编号
     */
    @Size(max = 16,message = "长度不能超过16个字符")
    private String carrierCode;

    /**
     * 取消原因
     */
    @Size(max = 64,message = "长度不能超过64个字符")
    private String reason;

    /**
     *  软取消的回库任务类型。
     *  潜伏、叉车、CTU默认不用传值，系统初始化了默认的流程。
     * 滚筒车可填的任务类型在数据字典软取消任务类型中。
     */
    private String returnTaskType; // 新任务类型

    /**
     * 执行步骤集合
     */
    @Valid
    private List<RcsCancelTaskTargRoutDTO> targetRoute;

    /**
     * 自定义拓展字段
     */
    private Map<String,Object> extra;
}
