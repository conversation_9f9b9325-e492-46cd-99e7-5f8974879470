package com.hvisions.thirdparty.common.dto;

import lombok.Data;

@Data
public class WeldLineCallMaterialsDTO {

    //请求码
    private String requestCode;
    //请求时间
    private String requestTime;
    //请求用户编码
    private String requestUserCode;
    //工单号
    private String workOrder;
    //料框号
    private String frameCode;
    //料点编号
    private String pointCode;
    //任务类型  默认40=生产叫料，后续调度反馈需要，线控可以不传
    private String taskType = "40";
    //请求系统编号
    private String requestSystem;
}
