package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class RcsCancelTaskTargRoutDTO {
    /**
     * 预制类型：ZONE 区域
     * 只能在区域中挑选位置
     */
    @NotBlank
    @Size(max = 16,message = "长度不能超过16个字符")
    private String type;

    /**
     * type对应目标编号
     */
    @NotBlank
    @Size(max = 256,message = "长度不能超过256个字符")
    private String code;
}
