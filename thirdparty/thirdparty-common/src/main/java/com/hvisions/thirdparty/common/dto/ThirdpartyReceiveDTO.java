package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import java.util.Date;

@Data
public class ThirdpartyReceiveDTO {

    private Long id;

    /**
     * 创建时间
     */

    private Date createTime;
    /**
     * 修改时间
     */

    private Date updateTime;

    /**
     * 内容(功能)类型
     */
    private String infoType;


    //请求类型
    private String requestSystem;
    /**
     * 接收内容
     */
    private String receiveContent;
    /**
     * 处理状态（0：未处理，1：成功，2：失败）
     */
    private Integer status;


    /**
     * 发送Ip
     */
    private String sendIp;

    /**
     * 处理次数
     */
    private Integer handleQuality = 1;


    private String errorMessage;
}
