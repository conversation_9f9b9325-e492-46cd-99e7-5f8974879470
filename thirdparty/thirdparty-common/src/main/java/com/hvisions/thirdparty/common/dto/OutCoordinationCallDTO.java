package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import java.util.List;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
@Data
public class OutCoordinationCallDTO {

    //请求单号
    private String reqNumber;
    //托盘编号
    private String palletCode;
    //库区类型
    private String regionType;
    //转出库区
    private String outWarehouseCode;
    //传出库位
    private String outLocationCode;
    //转入库区
    private String inWarehouseCode;
    //物料列表
    private List<OutCoordinationCallMaterialDTO> materialList;

}
