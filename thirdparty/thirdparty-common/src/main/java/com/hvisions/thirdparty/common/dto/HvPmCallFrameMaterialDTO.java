package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class HvPmCallFrameMaterialDTO {
    /**
     * 主键ID
     */
    private Long id;


    /**
     * 物料编号
     */
    private String materialCode;

    /**
     * 船号
     */
    private String shipNo;

    /**
     * 料框编号
     */
    private String frameCode;

    private String warehouseCode;//库区

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 已使用数量
     */
    private BigDecimal usedQuantity;

    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;

    /**
     * 占用数量
     */
    private BigDecimal occupationQuantity;
    /**
     * 工单编码
     */
    private String workOrderCode;
    /**
     * 是否存在线边 true：在 false 不在
     */
    private Boolean isPresent;
    /**
     * 流向
     */
    private String blockCode;

}
