package com.hvisions.thirdparty.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MesWeldingMaterialIssuanceDTO {
    // 请求任务编号（产线系统生成唯一码）
    @NotBlank(message = "请求任务编号不能为空")
    @Size(max = 32, message = "请求任务编号长度不能超过32")
    private String requestCode;

    // 请求时间（格式：DATETIME）
    @NotBlank(message = "请求时间不能为空")
    @Size(max = 32, message = "请求时间长度不能超过32")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reqTime;

    // 任务类型（10=请求空框，20=满框调度，40=请求物料，50=空框调度）
    @NotBlank(message = "任务类型不能为空")
    @Pattern(regexp = "10|20|40|50", message = "任务类型必须是10,20,40或50")
    private String taskType;

    // 托盘编号（请求满框调度时必填）
    @Size(max = 16, message = "托盘编号长度不能超过16")
    private String frameCode;

    // 托盘类型（请求空框和空框调度时必填）
    @Size(max = 16, message = "托盘类型长度不能超过16")
    private String frameType;

    // 起点料点编号（托盘转运任务时必填）
    @Size(max = 16, message = "起点料点编号长度不能超过16")
    private String startpointCode;

    // 终点料点编号（请求空框时必填）
    @Size(max = 16, message = "终点料点编号长度不能超过16")
    private String pointCode;

    // 调度优先级别（默认1）
    @Min(value = 1, message = "优先级最小为1")
    @Max(value = 9, message = "优先级最大为9") // 长度为2，最大99但实际值更小
    private Integer priority = 1; // 默认值

    // 请求方系统编号（产线使用线体编码）
    @Size(max = 16, message = "请求方系统编号长度不能超过16")
    private String requestSystem;

    // 工位编号
    @Size(max = 16, message = "工位长度不能超过16")
    private String stationCode;

    // 物料信息列表（满框调度时必填）
    @Valid
    private List<MesWeldingMaterialIssuanceMaterialDTO> materialList;

}
