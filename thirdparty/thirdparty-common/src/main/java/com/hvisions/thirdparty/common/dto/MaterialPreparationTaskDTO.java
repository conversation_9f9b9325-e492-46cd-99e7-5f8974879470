package com.hvisions.thirdparty.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Date 2024/5/23
 */
@Data
public class MaterialPreparationTaskDTO {
    // 消息时间，格式为yyyy-MM-dd HH:mm:ss
    @JSONField(name = "msgTime")
    private String msgTime;

    // 消息编号
    @JSONField(name = "msgId")
    private String msgId = UUID.randomUUID().toString();

    //数据
    @JSONField(name = "datas")
    private List<MaterialPreparationTaskDetail> datas;
}
