package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Data
public class XcStorageMaterialsDTO {

    /**
     * 零件编码
     * 业务含义：标识唯一零件
     * 错误提示：零件编码不能为空
     */
    @NotBlank(message = "零件编码不能为空")
    @Size(max = 32, message = "零件编码长度不能超过{max}个字符")
    private String materialsCode;

    /**
     * 唯一码
     * 业务含义：零件的序列号或唯一标识
     * 错误提示：唯一码长度不能超过{max}个字符
     */
    @Size(max = 32, message = "唯一码长度不能超过{max}个字符")
    private String uniqNo;

    /**
     * 长
     * 业务含义：零件的长度尺寸
     * 错误提示：零件长度不能为空
     */
    @Size(max = 20, message = "零件长度长度不能超过{max}个字符")
    private String length;

    /**
     * 宽
     * 业务含义：零件的宽度尺寸
     * 错误提示：零件宽度不能为空
     */
    @Size(max = 20, message = "零件宽度长度不能超过{max}个字符")
    private String width;

    /**
     * 厚
     * 业务含义：零件的厚度尺寸
     * 错误提示：零件厚度不能为空
     */
    @Size(max = 20, message = "零件厚度长度不能超过{max}个字符")
    private String height;

    /**
     * 重量
     * 业务含义：零件的重量
     * 错误提示：零件重量不能为空
     */
    private Double weight;

    /**
     * 数量
     * 业务含义：托盘上该零件的数量
     * 错误提示：零件数量不能为空
     */
    @NotBlank(message = "零件数量不能为空")
    private Double quantity;


    private String specifications;


    private String textureOfMaterial;
}