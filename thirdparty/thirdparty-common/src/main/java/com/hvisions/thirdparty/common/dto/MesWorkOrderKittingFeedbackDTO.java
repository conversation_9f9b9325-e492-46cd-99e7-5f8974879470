package com.hvisions.thirdparty.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

@Data
public class MesWorkOrderKittingFeedbackDTO {
    // 工单编号 (组立焊接：ZL+年月日+4位数字；分段焊接：FD+年月日+4位数字)
    @NotBlank(message = "工单编号不能为空")
    @Size(max = 32, message = "工单编号长度不能超过32")
    private String workOrderCode;

    // 批次号
    @Size(max = 32, message = "批次号长度不能超过32")
    private String planCode;

    // 任务名称
    @Size(max = 32, message = "任务名称长度不能超过32")
    private String name;

    // 任务类型（组立：0，分段：1）
    @NotBlank(message = "任务类型不能为空")
    @Pattern(regexp = "[01]", message = "任务类型只能为0或1")
    private String type;

    // 线体编号
    @NotBlank(message = "线体编号不能为空")
    @Size(max = 32, message = "线体编号长度不能超过32")
    private String lineCode;

    // 物料类型
    @NotBlank(message = "物料类型不能为空")
    @Size(max = 32, message = "物料类型长度不能超过32")
    private String materialType;

    // 船号
    @NotBlank(message = "船号不能为空")
    @Size(max = 32, message = "船号长度不能超过32")
    private String shipNumber;

    // 分段号
    @NotBlank(message = "分段号不能为空")
    @Size(max = 32, message = "分段号长度不能超过32")
    private String segmentationCode;

    // 加工数量（默认1）
    @NotNull(message = "加工数量不能为空")
    @Min(value = 1, message = "加工数量至少为1")
    private Integer qty;

    // 是否集配完成
    @NotBlank(message = "集配状态不能为空")
    @Size(max = 32, message = "集配状态长度不能超过32")
    private String assemble;

    // 实际开始时间
    @NotNull(message = "实际开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    // 实际结束时间
    @NotNull(message = "实际结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
