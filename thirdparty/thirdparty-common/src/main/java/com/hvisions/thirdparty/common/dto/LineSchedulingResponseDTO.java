package com.hvisions.thirdparty.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class LineSchedulingResponseDTO {
    // LES系统生成唯一码（用于后续反馈给产线）
    @NotBlank(message = "请求任务编号不能为空")
    @Size(max = 32, message = "请求任务编号长度不能超过32")
    private String requestCode;

    // 任务类型（10=请求空框, 20=满框调度, 40=请求物料, 50=空框调度）
    @NotBlank(message = "任务类型不能为空")
    @Pattern(regexp = "10|20|40|50", message = "任务类型必须为10,20,40或50")
    @Size(max = 16, message = "任务类型长度不能超过16")
    private String taskType;

    // 完成时间（时间格式DATETIME）
    @NotNull(message = "完成时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime finishTime;

    // 托盘编号
    @NotBlank(message = "托盘编号不能为空")
    @Size(max = 16, message = "托盘编号长度不能超过16")
    private String frameCode;

    // 托盘类型
    @NotBlank(message = "托盘类型不能为空")
    @Size(max = 16, message = "托盘类型长度不能超过16")
    private String frameType;

    // 起点/终点的料点编号
    @NotBlank(message = "料点编号不能为空")
    @Size(max = 16, message = "料点编号长度不能超过16")
    private String startpointCode;

    // 调度结果（OK/NG）
    @NotBlank(message = "调度结果不能为空")
    @Size(max = 5, message = "调度结果长度不能超过5")
    @Pattern(regexp = "OK|NG", message = "调度结果只能是OK或NG")
    private String result;

    @Valid
    private List<MesWeldingEmptyFreamReturnMaterialDTO> materialList;
}
