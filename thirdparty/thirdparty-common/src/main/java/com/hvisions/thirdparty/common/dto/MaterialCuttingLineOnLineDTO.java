package com.hvisions.thirdparty.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class MaterialCuttingLineOnLineDTO {

    @JSONField(name = "id")
    public String id;
    @JSONField(name = "name")
    public String name;
    @JSONField(name = "timestamp")
    public String timestamp;
    @JSONField(name = "station_id")
    public String station_id;
    @JSONField(name = "orderNo")
    public String orderNo;
    @JSONField(name = "raw_data")
    public List<MaterialCuttingLineOnLineDataDTO> raw_data;
}
