package com.hvisions.thirdparty.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <P> 型材切割计划 DTO<P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Data
public class XcMaterialCutPlanDTO {

    /**
     * 作业编号
     */
    @NotNull(message = "作业编号不能为空")
    private String orderNo;

    /**
     * 船型
     */
    private String shipMode;

    /**
     * 船号
     */
    private String shipCode;

    /**
     * 产线（型材切割A线、B线）
     */
    private String lineCode;

    /**
     * 计划结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date planEndTime;

    /**
     * 分段号
     */
    private String segmentationCode;

    /**
     * 计划完成时间
     */
    private String prodDate;

    /**
     * gen文件列表
     */
    private List<XcMaterialCutPlanGenDTO> gens;

}
