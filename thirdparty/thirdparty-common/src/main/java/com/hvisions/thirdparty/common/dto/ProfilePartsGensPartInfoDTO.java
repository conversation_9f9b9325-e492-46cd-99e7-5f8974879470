package com.hvisions.thirdparty.common.dto;

import lombok.Data;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/27
 */
@Data
public class ProfilePartsGensPartInfoDTO {
    //零件工单编号
    private String work_order_code;

    //物料号
    private String materialCode;

    //物料名称
    private String materialName;

    //是否外发  0否1是（零件工单必填）
    private String outFlag;

    //自制外协  0是自制  1是外协
    private String specialPurchaseTypeCode;

    //流向代码 A1,A2,C1,C2,C3,C4………………
    private String blockCode;

    /**
     * 当前零件所属分段号
     */
    private String segmentationCode;
}
