package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class CuttingPlanPartListDTO {

        /**
         * 物料编码
         * <p>唯一标识物料的关键编码</p>
         */
        @NotBlank(message = "物料编码不能为空")
        @Size(max = 32, message = "物料编码长度不能超过32个字符")
        private String materialCode;

        /**
         * 物料名称
         * <p>物料的描述性名称</p>
         */
        @Size(max = 32, message = "物料名称长度不能超过32个字符")
        private String materialName;

        /**
         * 分拣托盘
         * <p>用于存放物料的托盘标识</p>
         */
        @NotBlank(message = "分拣托盘编码不能为空")
        @Size(max = 32, message = "分拣托盘编码长度不能超过32个字符")
        private String palletCode;

        /**
         * 分拣工位
         * <p>执行分拣操作的工位编码</p>
         */
        @Size(max = 32, message = "分拣工位编码长度不能超过32个字符")
        private String pickStationCode;

        /**
         * 分拣人
         * <p>执行分拣操作的人员标识</p>
         */
        @Size(max = 32, message = "分拣人名称长度不能超过32个字符")
        private String pickPerson;

        /**
         * 物料状态
         * <p>标识物料的当前状态</p>
         */
        @NotNull(message = "物料状态不能为空")
        private Integer materialStatus;

    }

