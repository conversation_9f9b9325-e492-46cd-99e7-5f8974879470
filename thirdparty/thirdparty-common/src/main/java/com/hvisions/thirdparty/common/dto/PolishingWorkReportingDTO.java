package com.hvisions.thirdparty.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PolishingWorkReportingDTO extends AssemblyWorkOrderReportingDTO {
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String name;
    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    private String podCode;
    /**
     * 班次
     */
    @ApiModelProperty(value = "班次")
    private String batchNo;
}
