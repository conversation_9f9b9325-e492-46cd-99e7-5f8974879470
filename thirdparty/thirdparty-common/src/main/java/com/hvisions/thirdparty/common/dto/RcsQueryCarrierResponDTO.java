package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Map;

@Data
public class RcsQueryCarrierResponDTO {
    /**
     * 载具编号
     */
    @NotBlank(message = "载具编号不能为空")
    @Size(max = 16,message = "载具字段最大长度不能超过16")
    private String carrierCode;

    /**
     * 载具当前的任务编号，无任务为空。
     */
    @Size(max = 64,message = "载具当前任务编号长度不能超过64个字符")
    private String robotTaskCode;

    /**
     * 与载具绑定的站点的编号。无绑定为空
     */
    @Size(max = 32,message = "载具当前任务编号长度不能超过32个字符")
    private String siteCode;

    /**
     * 载具的x坐标
     */
    @Size(max = 32,message = "载具的x坐标长度不能超过32个字符")
    @NotBlank(message = "x坐标不能为空")
    private String x;

    /**
     * 载具的y坐标
     */
    @Size(max = 32,message = "载具的y坐标长度不能超过32个字符")
    @NotBlank(message = "y坐标不能为空")
    private String y;

    /**
     * 货架相对于站点的方向，取值范围 [0, 360)
     */
    @Size(max = 32,message = "货架相对于站点的方向不能超过32个字符")
    @NotEmpty(message = "货架相对于站点的方向部位空")
    private Integer carrierDir;

    /**
     * 载具状态，固定枚举值NORMAL（正常）/LOCKED（禁用）
     */
    @NotEmpty(message = "载具状态不能为空")
    private Integer carrierStatus;

    /**
     * 自定义拓展字段
     */
    private Map<String,Object> extra;
}
