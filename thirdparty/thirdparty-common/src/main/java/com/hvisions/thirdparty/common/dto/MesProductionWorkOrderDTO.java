package com.hvisions.thirdparty.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.pms.dto.productWorkOrder.ProductWorkOrderDTO;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

@Data
public class MesProductionWorkOrderDTO {
    // 删除标识 (0:否, 1:是)
    @NotNull(message = "删除标识不能为空")
    private Integer deleteFlag;

    // 工单编号（组立焊接：ZL+年月日+4位数字；分段焊接：FD+年月日+4位数字）
    @NotBlank(message = "工单编号不能为空")
    @Size(max = 32, message = "工单编号长度不能超过32")
    private String workOrderCode;

    @Size(max = 32, message = "批次号长度不能超过32")
    private String planCode;

    @Min(value = 1, message = "顺序/优先级最小值是1")
    private Integer executeSequence;

    @Size(max = 32, message = "任务名称长度不能超过32")
    private String name;

    // 任务类型（组立：0，分段：1）
    @NotBlank(message = "任务类型不能为空")
    @Size(max = 32, message = "任务类型长度不能超过32")
    private String type;

    @NotBlank(message = "线体编号不能为空")
    @Size(max = 32, message = "线体编号长度不能超过32")
    private String lineCode;

    @NotBlank(message = "物料类型不能为空")
    @Size(max = 32, message = "物料类型长度不能超过32")
    private String materialType;

    @NotBlank(message = "船号不能为空")
    @Size(max = 32, message = "船号长度不能超过32")
    private String shipNumber;

    @NotBlank(message = "分段号不能为空")
    @Size(max = 32, message = "分段号长度不能超过32")
    private String segmentationCode;

    // 加工数量（默认1）
    @NotNull(message = "加工数量不能为空")
    @Min(value = 1, message = "加工数量至少为1")
    private Integer qty;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planEndTime;

    @NotBlank(message = "物料编码不能为空")
    @Size(max = 32, message = "物料编码长度不能超过32")
    private String materialCode;

    @Size(max = 32, message = "物料名称长度不能超过32")
    private String materialName;

    @Size(max = 32, message = "bom编号长度不能超过32")
    private String bomCode;

    @Size(max = 32, message = "组立bom版本长度不能超过32")
    private String bomVersion;

    public ProductWorkOrderDTO convertToMesOrderDTO() {
        ProductWorkOrderDTO productWorkOrderDTO = new ProductWorkOrderDTO();
        productWorkOrderDTO.setProductWorkOrderCode(workOrderCode);
        productWorkOrderDTO.setProductCode(bomCode);
        productWorkOrderDTO.setBomVersion(bomVersion);
        productWorkOrderDTO.setProductWorkOrderAttribute(type);
        productWorkOrderDTO.setProductionLineCode(lineCode);
        productWorkOrderDTO.setProductName(materialName);
        productWorkOrderDTO.setPlanQuantity(qty);
        productWorkOrderDTO.setPlanStartTime(toUtcIsoLocalDateTime(planStartTime));
        productWorkOrderDTO.setPlanEndTime(toUtcIsoLocalDateTime(planEndTime));
        productWorkOrderDTO.setShipCode(shipNumber);
        return productWorkOrderDTO;
    }

    public static LocalDateTime toUtcIsoLocalDateTime(LocalDateTime src) {
        if (src == null) return null;
        return src.atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneOffset.UTC)
                .toLocalDateTime();
    }
}

