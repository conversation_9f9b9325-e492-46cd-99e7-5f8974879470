package com.hvisions.thirdparty.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MaterialCutPlanPartDTO {

    /**
     * 主表id
     */
    private Integer cutPlanId;

    /**
     * 切割计划
     */
    private String cutPlanCode;

    /**
     * 物料号
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 数量
     */
    private Integer quality;

    /**
     * 工单号
     */
    private String workOrder;

    /**
     * 零件图
     */
    private String materialPicture;

    /**
     * part name，从物料主数据来
     */
    private String pn;

    @ApiModelProperty(value = "零件长度（mm）")
    @JSONField(name = "length")
    private String length;

    @ApiModelProperty(value = "零件宽度（mm）")
    @JSONField(name = "width")
    private String width;

    @ApiModelProperty(value = "零件厚度（mm）")
    @JSONField(name = "thick")
    private String thick;

    /**
     * 坐标X
     */
    @JsonIgnore
    private String coordinateX;

    /**
     * 坐标Y
     */
    @JsonIgnore
    private String coordinateY;

    /**
     * 零件序号
     */
    private String no;

    /**
     * 顺序
     */
    private Integer sequence;

    // 套料数量（个）
    private Integer nestingCount;

    // 订单数量（个）
    private Integer orderCount;

    // 零件净重量（KG）
    private String partNetWeight; // 单位：千克

    // 零件矩形重量（KG）
    private String partRectWeight; // 单位：千克

    // 零件净面积（平方米）
    private String partNetArea; // 单位：平方米

    // 零件矩形面积（平方米）
    private String partRectArea; // 单位：平方米

    // 单个零件切割长度（米）
    private String singlePartCutLength; // 单位：米

    // 单个零件打标长度（米）
    private String singlePartMarkingLength; // 单位：米

    // 单个零件刺穿孔数（个）
    private Integer singlePartPierceCount;
}
