package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import java.util.List;

/**
 * <P> gen文件列表<P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Data
public class XcMaterialCutPlanGenDTO {

    /**
     * 切割计划子编号
     */
    private String subPlanNo;

    /**
     * 零件数量
     */
    private String qty;

    /**
     * GEN文件地址
     */
    private String genFilePath;

    /**
     * 废料总长度（型材-零件-余料=废料）
     */
    private String scrapLength;

    /**
     * 型材利用率
     */
    private String plateUtilRate;

    /**
     * 订单满足率
     */
    private String orderFulfillRate;

    /**
     * 余料率
     */
    private String scrapRate;

    /**
     * 报废率
     */
    private String wasteRate;

    /**
     *型材总长度
     */
    private String totalPlateLength;

    /**
     * 零件总长度
     */
    private String totalPartLength;

    /**
     * 余料总长度
     */
    private String totalScrapLength;

    /**
     * 型材原料物料编码
     */
    private String materialCode;

    /**
     *  零件信息列表
     */
    private List<XcMaterialCutPlanPartinfoDTO> partInfo;

}
