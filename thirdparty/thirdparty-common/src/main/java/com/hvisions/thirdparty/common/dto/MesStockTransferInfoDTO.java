package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import java.util.List;

@Data
public class MesStockTransferInfoDTO {
    private String palletCode; // 托盘编号
    private String factoryCode; // 工厂编码，默认cg1
    private String outWarehouseCode; // 转出仓库编码
    private String outLocationCode  = "A0101"; // 转出库位编码
    private String inWarehouseCode; // 转入仓库编码
    private String inLocationCode = "A0101"; // 转入库位编码
    private List<MesStockTransferMaterialDTO> materialList; // 物料集合
}
