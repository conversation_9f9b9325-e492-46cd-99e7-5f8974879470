package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 西部车间到总装车间转运工单下发请求DTO
 */
@Data
public class LesTransportTaskApplyDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 申请开始时间
     * 格式示例：2024-06-05T11:00:00+08:00
     * 长度限制：最大 50 字符（包含日期时间格式及时区信息）
     */
    @NotBlank(message = "申请开始时间不能为空")
    @Size(max = 50, message = "申请开始时间长度不能超过 {max} 个字符")
    private String applyStartDate;

    /**
     * 申请结束时间
     * 格式示例：2024-06-05T12:00:08+08:00
     * 长度限制：最大 50 字符（包含日期时间格式及时区信息）
     */
    @NotBlank(message = "申请结束时间不能为空")
    @Size(max = 50, message = "申请结束时间长度不能超过 {max} 个字符")
    private String applyEndDate;

    /**
     * 任务编码
     * 示例值：001
     * 长度限制：最大 20 字符（支持字母数字组合）
     */
    @NotBlank(message = "任务编码不能为空")
    @Size(max = 20, message = "任务编码长度不能超过 {max} 个字符")
    private String taskCode;

    /**
     * 车辆类型
     * 取值说明：FLAT_CAR-液压平板车（其他类型按实际需求扩展）
     * 长度限制：最大 20 字符（枚举值固定，预留扩展空间）
     */
    @NotBlank(message = "车辆类型不能为空")
    @Size(max = 20, message = "车辆类型长度不能超过 {max} 个字符")
    private String carTypeId;

    /**
     * 车辆作业类型
     * 取值说明：SECTION_TRANSPORT-分段运输/HATCHOVER_TRANSPORT-舱盖运输/OTHER_TRANSPORT-其它
     * 长度限制：最大 30 字符（枚举值拼接字符串，预留空间）
     */
    @NotBlank(message = "车辆作业类型不能为空")
    @Size(max = 30, message = "车辆作业类型长度不能超过 {max} 个字符")
    private String carJobType;

    /**
     * 作业内容
     * 选择分段运输时可选值：油漆房到现场/沙现场到油漆房/现场到沙现场/分段移位/配合下舱
     * 选择舱盖运输或其它时支持自定义输入
     * 长度限制：最大 200 字符（支持长文本描述）
     */
    @NotBlank(message = "作业内容不能为空")
    @Size(max = 200, message = "作业内容长度不能超过 {max} 个字符")
    private String jobContent;

    /**
     * 用车部门名称
     * 示例值：制造-工厂
     * 长度限制：最大 50 字符（部门名称常规长度）
     */
    @NotBlank(message = "用车部门名称不能为空")
    @Size(max = 50, message = "用车部门名称长度不能超过 {max} 个字符")
    private String deptId;

    /**
     * 项目编码
     * 示例值：项目01
     * 长度限制：最大 50 字符（支持中文+数字组合）
     */
    @NotBlank(message = "项目编码不能为空")
    @Size(max = 50, message = "项目编码长度不能超过 {max} 个字符")
    private String projectId;

    /**
     * 配送起点编码
     * 示例值：F-101
     * 长度限制：最大 20 字符（编码规则固定，预留空间）
     */
    @NotBlank(message = "配送起点编码不能为空")
    @Size(max = 20, message = "配送起点编码长度不能超过 {max} 个字符")
    private String distributionStartId;

    /**
     * 配送终点编码
     * 示例值：F-102
     * 长度限制：最大 20 字符（编码规则固定，预留空间）
     */
    @NotBlank(message = "配送终点编码不能为空")
    @Size(max = 20, message = "配送终点编码长度不能超过 {max} 个字符")
    private String distributionEndId;

    /**
     * 发货人工号
     * 示例值：0002001
     * 长度限制：最大 30 字符（工号规则可能包含字母数字）
     */
    @NotBlank(message = "发货人工号不能为空")
    @Size(max = 30, message = "发货人工号长度不能超过 {max} 个字符")
    private String deliveryUserId;

    /**
     * 收货人工号
     * 示例值：0002002
     * 长度限制：最大 30 字符（工号规则可能包含字母数字）
     */
    @NotBlank(message = "收货人工号不能为空")
    @Size(max = 30, message = "收货人工号长度不能超过 {max} 个字符")
    private String contactUserId;

    /**
     * 申工人号
     * 示例值：0002003
     * 长度限制：最大 30 字符（工号规则可能包含字母数字）
     */
    @NotBlank(message = "申工人号不能为空")
    @Size(max = 30, message = "申工人号长度不能超过 {max} 个字符")
    private String applyUserId;

    /**
     * 分段运输字段
     * 说明：分段运输和其它时为必填，舱盖运输不传
     * 长度限制：最大 10 字符（数字编码，如"01"）
     */
    @Size(max = 10, message = "分段运输字段长度不能超过 {max} 个字符")
    private String sectionNumber;

    /**
     * 备注
     * 长度限制：最大 500 字符（支持长文本备注）
     */
    @Size(max = 500, message = "备注长度不能超过 {max} 个字符")
    private String remarks;

    /**
     * 状态
     * 取值说明：ADD-新增/UPDATE-更新/CANCEL-撤销
     * 示例值：ADD
     * 长度限制：最大 10 字符（枚举值固定）
     */
    @NotBlank(message = "状态不能为空")
    @Size(max = 10, message = "状态长度不能超过 {max} 个字符")
    private String cssStatus;
}
