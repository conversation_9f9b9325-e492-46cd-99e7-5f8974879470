package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class RcsQueryRobotResponDTO {
    /**
     * 机器人编号
     */
    @NotBlank(message = "机器人编号不能为空")
    @Size(max = 16,message = "长度不能超过16个字节")
    private String singleRobotCode;

    /**
     * 机器人方向，取值[0,360)
     */
    @NotEmpty(message = "机器人方向不能为空")
    private Integer robotDir;

    /**
     * 机器人IP地址
     */
    @Size(max = 16,message = "长度不能超过16个字节")
    private String robotIp;

    /**
     * 机器人电量
     */
    @Min(value = 0,message = "电量最小值不能小于0")
    @Max(value = 100,message = "电量最大值不能大于100")
    @NotEmpty(message = "机器人电量不能为空")
    private Integer battery;

    /**
     * 机器人当前位置X坐标
     */
    @Size(max = 32,message = "x坐标长度不能超过32个字符")
    @NotBlank(message = " 机器人当前位置X坐标不能为空")
    private String x;

    /**
     *  机器人当前位置Y坐标
     */
    @Size(max = 32,message = "y坐标长度不能超过32个字符")
    @NotBlank(message = " 机器人当前位置Y坐标不能为空")
    private String y;

    /**
     * 机器人当前速度
     */
    @NotEmpty(message = "机器人当前速度不能为空")
    private Integer speed;

    /**
     * 机器人当前状态
     */
    @NotEmpty(message = "机器人当前状态不能为空")
    private RobotStatus robotStatus;

    /**
     * 机器人携带载具编号
     */
    @Size(max = 16,message = "载具编号不能超过16个字符")
    private String carrierCode;

    /**
     * 正在发生告警列表
     */
    private List<Warnings> warnings;

    /**
     * 自定义扩展字段
     */
    private Map<String,Object> extra;

    @Data
    public static class RobotStatus{
        /**
         * 异常状态，固定枚举值YES/NO
         */
        @NotBlank(message = "异常状态不能为空")
        private String abnormal;

        /**
         * 充电状态，固定枚举值YES/NO
         */
        @NotBlank(message = "充电状态不能为空")
        private String charging;

        /**
         * 网络状态，固定枚举值YES/NO
         */
        @NotBlank(message = "网络状态不能为空")
        private String network;

        /**
         * 执行任务状态，固定枚举值：IDLE（空闲）/WORKING（执行任务）/PAUSE（暂停）
         */
        @NotBlank(message = "任务执行状态不能为空")
        private String taskable;

        /**
         * 手动状态，固定枚举值：Manual（手动）/Auto（自动）
         */
        private String manual;

        /**
         * 急停状态，固定枚举值：EMERGENCY（急停）/NORMAL（正常）
         */
        @NotBlank(message = "急停状态不能为空")
        private String emergency;

        /**
         * 自定义扩展字段
         */
        private Map<String,Object> extra;
    }

    @Data
    public static class Warnings{
        /**
         * 任务异常告警单号
         */
        @Size(max = 16,message = "任务异常告警单号长度不能超过16个字符")
        @NotBlank(message = "任务异常告警单号不能为空")
        private String taskWarnCode;

        /**
         * 机器人唯一标识
         */
        @Size(max = 16,message = "机器人唯一标识不能超过16个字符")
        private String singleRobotCode;

        /**
         * 初次出现故障时间
         */
        private Date startTime;

        /**
         * 自定义故障码
         */
        @Size(max = 32,message = "自定义故障码长度不能超过32个字符")
        @NotBlank(message = "自定义故障码不能为空")
        private String errorCode;

        /**
         * 自定义故障消息
         */
        @Size(max = 256,message = "自定义故障码长度不能超过256个字符")
        private String errorMsg;
    }
}
