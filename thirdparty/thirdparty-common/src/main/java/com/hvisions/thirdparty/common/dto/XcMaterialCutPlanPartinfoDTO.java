package com.hvisions.thirdparty.common.dto;

import lombok.Data;

/**
 * <P>零件信息 <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Data
public class XcMaterialCutPlanPartinfoDTO {
    /**
     * 零件工单编号
     */
    private String workOrderCode;

    /**
     * 物料号
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 是否外发 0否，1是
     */
    private String outFlag;

    /**
     * 自制外协 0自制，1外协
     */
    private String specialPurchaseTypeCode;

    /**
     * 流向代码
     */
    private String blockCode;

    /**
     * 套料数量
     */
    private Integer nestingCount;

    /**
     * 订单数量
     */
    private String orderCount;

    /**
     * 零件长度
     */
    private String partnetLength;


    /**
     * 当前零件所属分段号
     */
    private String segmentationCode;

}
