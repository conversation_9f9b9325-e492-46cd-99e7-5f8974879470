package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 库区库位主数据同步通用DTO
 */
@Data
public class MomWarehouseLocationDTO {

    /**
     * 仓库编码(工厂)
     */
    @NotBlank(message = "仓库编码不能为空")
    @Size(max = 64, message = "仓库编码长度不能超过64个字符")
    private String whCode;

    /**
     * 仓库名称(工厂)
     */
    @NotBlank(message = "仓库名称不能为空")
    @Size(max = 64, message = "仓库名称长度不能超过64个字符")
    private String whText;

    /**
     * 区域编号(库区编码)
     * 部分产线点位可能无库区编码
     */
    @Size(max = 16, message = "区域编号长度不能超过16个字符")
    private String stgTypCode;

    /**
     * 区域名称(库区名称)
     */
    @Size(max = 64, message = "区域名称长度不能超过64个字符")
    private String stgTypText;

    /**
     * 呼叫站点(库位编码)
     * 定义的地码代号，非中文
     */
    @NotBlank(message = "呼叫站点不能为空")
    @Size(max = 32, message = "呼叫站点长度不能超过32个字符")
    private String dataName;

    /**
     * 地码
     * XY的坐标
     */
    @NotBlank(message = "地码不能为空")
    @Size(max = 32, message = "地码长度不能超过32个字符")
    private String berthCode;

    /**
     * 料框类型编码
     * 接线上料点对应能放的料框类型编号
     */
    @Size(max = 32, message = "料框类型编码长度不能超过32个字符")
    private String binTypeCode;

}
