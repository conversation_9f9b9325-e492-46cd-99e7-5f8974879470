package com.hvisions.thirdparty.common.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class ProfileOutboundTaskDTO {

    // 消息编号
    @JSONField(name = "msgId")
    private String msgId;

    // 消息时间，格式为yyyy-MM-dd HH:mm:ss
    @JSONField(name = "msgTime")
    private String msgTime;

    // 数据，类型为数组
    @JSONField(name = "datas")
    private List<ProfileTaskDetail> datas;
}
