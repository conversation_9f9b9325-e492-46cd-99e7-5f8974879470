package com.hvisions.thirdparty.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/7
 */
@Data
public class XcOrderReportDTOMaterialDTO {

    @ApiModelProperty("零件物料编号")
    private String materialCode;

    @ApiModelProperty("零件工单编号")
    private String workOrderCode;

    @ApiModelProperty("PN码")
    private String pn;

    @ApiModelProperty("托盘编号")
    private String palletCode;

    @ApiModelProperty("零件数量（零件总数）")
    private Integer quality;

    @ApiModelProperty("分拣数量（分拣合格零件的总数）")
    private Integer qualifiedQty;

    @ApiModelProperty("丢件数量（报废数量）")
    private Integer lossQty;

    @ApiModelProperty("型材下料开始")
    private String   startProfileCutting;

    @ApiModelProperty("型材下料结束")
    private String   endProfileCutting;

}
