package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class SteelPlateCuttingOrderDTO {
    /**
     * 切割计划编码
     */
    @Size(max = 32,message = "切割计划编码不能超过32个字符")
    @NotBlank(message = "切割计划编码不能为空")
    private String planCode;

    /**
     * 原材料流向
     * 新车间（0）/老车间（1）
     */
    @NotBlank(message = "原材料流向不能为空")
    private Integer rawMatFlow;

    /**
     * 切割批次号
     */
    @NotBlank(message = "切割批次号不能为空")
    @Size(max = 32,message = "切割计划批次不能超过32个字符")
    private String cutPlanBatch;

    /**
     * 任务类型
     * 钢板：0
     * 型材：1
     */
    @NotBlank(message = "任务类型不能为空")
    private Integer taskType;

    /**
     * 船号
     */
    @NotBlank(message = "船号不能为空")
    @Size(max = 32,message = "船号不能超过32个字符")
    private String shipNo;

    /**
     * 分段号
     */
    @NotBlank(message = "分段号不能为空")
    @Size(max = 32,message = "船号不能超过32个字符")
    private String sectionNo;

    /**
     * 班次班组
     */
    @NotBlank(message = "班次班组不能为空")
    @Size(max = 32,message = "班次班组不能超过32个字符")
    private String shiftCrew;

    /**
     * 下发时间
     */
    @NotBlank(message = "下发时间不能为空")
    @Size(max = 32,message = "下发时间不能超过32个字符")
    private String issueTime;

    /**
     * 切割计划开始时间
     */
    @NotBlank(message = "切割计划开始时间不能为空")
    @Size(max = 32,message = "切割计划开始时间不能超过32个字符")
    private String cutStart;

    /**
     * 切割计划结束时间
     */
    @NotBlank(message = "切割计划结束时间不能为空")
    @Size(max = 32,message = "切割计划结束时间不能超过32个字符")
    private String cutEnd;

    /**
     * 原材料物料编码
     */
    @NotBlank(message = "原材料物料编码不能为空")
    @Size(max = 32,message = "原材料物料编码不能超过32个字符")
    private String rawMatCode;

    /**
     * 原材料物料规格
     */
    @Size(max = 32,message = "原材料物料规格不能超过32个字符")
    private String rawSpec;

    /**
     * 数量
     */
    @NotBlank(message = "数量不能为空")
    private Integer qty;

    /**
     * 原材料长度（型材钢板必填）
     */
    @NotBlank(message = "原材料长度不能为空")
    @Size(max = 32,message = "型材钢板长度不能超过32个字符")
    private String rawLen;

    /**
     * 原材料宽度（型材钢板必填）
     */
    @NotBlank(message = "原材料宽度不能为空")
    @Size(max = 32,message = "型材钢板长度不能超过32个字符")
    private String rawWid;

    /**
     * 原材料厚度（型材钢板必填）
     */
    @NotBlank(message = "原材料厚度不能为空")
    @Size(max = 32,message = "型材钢板长度不能超过32个字符")
    private String rawThick;

    /**
     * 原材料规格（型材钢板必填）
     */
    @NotBlank(message = "原材料规格不能为空")
    @Size(max = 32,message = "原材料规格长度不能超过32个字符")
    private String rawSpec1;

    /**
     * 原材料规格（型材钢板必填）
     */
    @NotBlank(message = "原材料规格不能为空")
    @Size(max = 32,message = "原材料规格长度不能超过32个字符")
    private String rawSpec2;

    /**
     * 原材料规格（型材钢板必填）
     */
    @NotBlank(message = "原材料规格不能为空")
    @Size(max = 32,message = "原材料规格长度不能超过32个字符")
    private String rawSpec3;

    /**
     * 原材料材质
     */
    @NotBlank(message = "原材料材质不能为空")
    @Size(max = 32,message = "原材料材质长度不能超过32个字符")
    private String rawMat;

    /**
     * 线号
     */
    @NotBlank(message = "线号不能为空")
    @Size(max = 32,message = "线号长度不能超过32个字符")
    private String lineCode;

    /**
     * 零件总数
     */
    @NotBlank(message = "零件总数不能为空")
    private Integer totalParts;

    /**
     * 套料程序文件地址dxf
     */
    @NotBlank(message = "套料程序文件地址dxf不能为空")
    @Size(max = 32,message = "套料程序文件地址dxf长度不能超过32个字符")
    private String nestProgDxf;

    /**
     * 喷码程序
     */
    @NotBlank(message = "喷码程序文件地址不能为空")
    @Size(max = 32,message = "喷码程序文件地址长度不能超过32个字符")
    private String sprayProgGen;

    /**
     * 切割程序
     */
    @NotBlank(message = "切割程序文件地址不能为空")
    @Size(max = 32,message = "切割程序文件地址长度不能超过32个字符")
    private String cutProgCnc;

    /**
     * 套料程序
     */
    @NotBlank(message = "套料程序文件地址不能为空")
    @Size(max = 32,message = "套料程序文件地址长度不能超过32个字符")
    private String nestPdf;

    /**
     * 生产顺序
     */
    @NotBlank(message = "生产顺序不能为空")
    private Integer prodOrder;

    /**
     * 零件信息
     */
    private List<SteelPlateCuttingOrderPartListDTO> cutPlanPartList;

}
