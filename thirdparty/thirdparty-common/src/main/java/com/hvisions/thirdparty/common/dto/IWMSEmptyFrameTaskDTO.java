package com.hvisions.thirdparty.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class IWMSEmptyFrameTaskDTO {
    @ApiModelProperty("任务编号（唯一值）")
    private String taskCode;//任务编号（唯一编号）

    private String taskType = "D2";    //任务类型，空框回库： D2
    @ApiModelProperty("料点编号（待确定是否能传，初始化入库必须上传）")
    private String wbCode;//料点编号（当前位置）
    @ApiModelProperty("货架编号（料框编号）")
    private String podCode;//货架编号（托盘/料框）
    @ApiModelProperty("是否已打磨,0：未打磨 1：不用打磨或已打磨")
    private String burnishFlag;

    private List<IWMSMaterialDataDTO> intoData;
    @ApiModelProperty("初始化入库（1-是，0-否） 立库填：1，其他库填：0")
    private String initPodFlag;
    private String lineCode;
    //库区类型（A01-外发，B01-立库1，B02立库2，C01-平库）(后面会确定明确的编号)
    private String stgCategory;
}
