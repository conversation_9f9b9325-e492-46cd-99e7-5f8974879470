package com.hvisions.thirdparty.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/11/8
 */
@Data
public class SyncXcOrderReportDTO {

    @ApiModelProperty(value = "工单号")
    private String orderCode;

    @ApiModelProperty(value = "型材立库备料开始")
    private String profileWarehousePrepStart;

    @ApiModelProperty(value = "型材立库备料结束")
    private String profileWarehousePrepEnd;

    @ApiModelProperty(value = "型材立库出库开始")
    private String profileWarehouseOutStart;

    @ApiModelProperty(value = "型材立库出库结束")
    private String profileWarehouseOutEnd;

    @ApiModelProperty(value = "报工产线")
    private String lineCode;

    @ApiModelProperty(value = "船号")
    private String shipNumber;

    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

}
