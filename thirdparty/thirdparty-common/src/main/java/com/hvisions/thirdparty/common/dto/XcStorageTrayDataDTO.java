package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 托盘数据对象
 * 存储托盘及零件相关信息
 */
@Data
public class XcStorageTrayDataDTO {

    /**
     * 托盘编码
     * 业务含义：标识唯一托盘
     * 错误提示：托盘编码不能为空
     */
    @NotBlank(message = "托盘编码不能为空")
    @Size(max = 32, message = "托盘编码长度不能超过{max}个字符")
    private String trayCode;

    /**
     * 托盘名称
     * 业务含义：托盘的描述性名称
     * 错误提示：托盘名称不能为空
     */
    @NotBlank(message = "托盘名称不能为空")
    @Size(max = 32, message = "托盘名称长度不能超过{max}个字符")
    private String trayName;

    /**
     * 零件集合
     * 业务含义：存储托盘上的所有零件信息
     * 错误提示：零件集合不能为空
     */
    @NotBlank(message = "零件集合不能为空")
    private List<XcStorageMaterialsDTO> mlist;
}
