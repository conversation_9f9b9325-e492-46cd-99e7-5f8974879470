package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import java.util.List;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/9/27
 */
@Data
public class OutCoordinationStandStockDTO {

    //任务编号
    private String taskCode;

    //出库入库，出库为0；入库为1
    private String  inOutStatus;

    //库位编码
    private String locationCode;

    //库位名称
    private String locationName;

    //料框编号
    private String podCode;

    //库区类型
    private String stgCategory;

    //物料列表
    private List<OutCoordinationStandStockMaterialDTO> materialsList;


}
