package com.hvisions.thirdparty.common.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class RcsQueryTaskResponDTO {
    /**
     * 任务号（全局唯一）
     */
    @NotBlank
    @Size(max = 64,message = "长度不能超过64个字符")
    private String robotTaskCode;


    /**
     * 任务类型
     */
    @NotBlank(message = "任务类型不能为空")
    @Size(max = 16,message = "长度不能超过16个字符")
    private String taskType;

    /**
     * 执行步骤集合
     */
    @Valid
    @NotEmpty(message = "执行步骤集合不能为空")
    private List<TargetRoute> targetRoute;

    /**
     * 任务执行优先顺序
     */
    @Max(value =9999999L,message = "任务执行优先顺序长度不能超过8位")
    private Integer initPriority;

    /**
     * 任务截至时间
     */
    private Date deadline; // ISO 8601格式

    private String taskStatus;

    private Warning warning;

    private String singleRobotCode;

    /**
     * 自定义拓展字段
     */
    private Map<String,Object> extra;

    @Data
    public static class TargetRoute{
        /**
         * 目标路径序列。从 0 开始。
         */
        @Max(value = Integer.MAX_VALUE,message = "seq长度超过整数最大值")
        private Integer seq;

        /**
         * 目标类型，预制枚举值在枚举类：RCSTargetRouteType
         */
        @NotBlank
        @Size(max = 16,message = "目标类型长度不能超过16个字符")
        private String type;

        /**
         * 与 type 对应的目标编号
         * 枚举为 ZONE 时：支持多个区域，以逗号隔开， 从 前 往后 ， 依 次 查找 ， 例如：ZONE1,ZONE2
         * 枚举为 MIX_CONDITION 时，格式如下：
         * [{"type":"ZONE","code":"SF2"},{"type":"CARRIER_TYPE","code":"P1"}]
         */
        @NotBlank
        @Size(max = 256,message = "目标编号长度不能超过256个字符")
        private String code; // 可以是JSON数组字符串

        @NotBlank
        @Max(value = 10L,message = "目标类型长度不能超过1个字节")
        private Integer autoStart;

        /**
         * 机器人到达目标位置后的操作。国标要求，非必要。对 AMR 动作无影响。
         * 可扩展的枚举值：RcsTrOperEnum
         */
        @Size(max = 8,message = "operation长度不能超过8个字符")
        private String operation;

        /**
         *要求调度系统仅在当前指定的范围内选择机器人执行该步骤。可能出现任务步骤与机器人类型不匹配的异常，需要业务系统确保任务与机器人类型的匹配。
         * 如果指定当前步骤的机器人选择范围，则调度系统无视任务中设定的机器人选择范围；
         * 如果任务中也没有指定机器人选择范围，则调度系统会在所有可用机器人的范围内寻找最优方案。
         * 固定枚举值：RcsTrRobotTypeEnum
         */
        private String robotType;

        /**
         * 与 robotType 匹配的资源类型唯一标识
         */
        private List<String> robotCode;

        /**
         * 自定义扩展字段
         */
        private Map<String,Object> extra;
    }

    @Data
    public static class Warning{
        @NotBlank(message = "任务异常告警单号不能为空")
        @Size(max = 16,message = "长度不能超过16个字符")
        private String taskWarnCode;

        private Date startTime;

        @NotBlank(message = "自定义故障码不能为空")
        @Size(max = 32,message = "长度不能超过32个字符")
        private String errorCode;

        @Size(max = 256,message = "长度不能超过256个字符")
        private String errorMsg;
    }
}
