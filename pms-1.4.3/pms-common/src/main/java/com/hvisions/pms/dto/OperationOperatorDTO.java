package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <p>Title: OperationOperatorDTO</p >
 * <p>Description: 工序操作记录</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/15</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OperationOperatorDTO {


    /**
     * 任务Id
     */
    @ApiModelProperty(value = "任务Id")
    private Integer taskId;


    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;
    /**
     * 下一道工艺步骤编码
     */
    @ApiModelProperty(value = "下一道工艺步骤编码")
    private String nodeCode = null;

    /**
     * 工单参数
     */
    @ApiModelProperty(value = "工单参数")
    private Map<String, Object> map;
}
