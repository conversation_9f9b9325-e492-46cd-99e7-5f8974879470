package com.hvisions.pms.dto;

import lombok.Data;

import java.util.Date;

@Data
public class HvPmPlanProductBomDTO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 计划ID
     */
    private Integer planId;
    /**
     * 计划编号
     */
    private String planCode;
    /**
     * 零件物料编号
     */
    private String materialCode;
    /**
     * 数量
     */
    private Long quantity;
    /**
     * 船型
     */
    private String model;
    /**
     * 分段号
     */
    private String segmentationCode;
    /**
     * 是否坡口
     */
    private String isGroove;
    /**
     * 坡口朝向
     */
    private String grooveDirection;
    /**
     * 零件长度（mm）
     */
    private String length;
    /**
     * 零件宽度（mm）
     */
    private String width;
    /**
     * 零件厚度（mm）
     */
    private String thick;
    /**
     * 零件类型
     */
    private String matType;
    /**
     * 零件毛重
     */
    private String roughWeight;
    /**
     * 零件净重
     */
    private String netWeight;
    /**
     * 计量单位
     */
    private String unitMeasures;
    /**
     * 工艺路线编号
     */
    private String routeCode;
    /**
     * 工艺路线版本
     */
    private String routeVersion;
    /**
     * 创建时间
     */
    protected Date createTime;
    /**
     * 修改时间
     */
    protected Date updateTime;

    private String frameCode;

    /**
     * 实际数量
     */
    private Integer actQuantity;

    /**
     * 齐套时间
     */
    private Date CompleteTime;
}
