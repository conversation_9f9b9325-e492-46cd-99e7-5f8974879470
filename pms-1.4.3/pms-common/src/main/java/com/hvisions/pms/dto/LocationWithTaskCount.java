package com.hvisions.pms.dto;

import com.hvisions.hiperbase.equipment.location.LocationDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

/**
 * <p>Title: LocationWithTaskCount</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2022/7/5</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@Getter
@Setter
@ToString
public class LocationWithTaskCount extends LocationDTO {

    public static LocationWithTaskCount from(LocationDTO locationDTO) {
        LocationWithTaskCount location = new LocationWithTaskCount();
        location.setCode(locationDTO.getCode());
        location.setName(locationDTO.getName());
        location.setParentId(locationDTO.getParentId());
        location.setId(locationDTO.getId());
        location.setType(locationDTO.getType());
        return location;
    }

    /**
     * 任务数量
     */
    @ApiModelProperty(value = "任务数量", notes = "1:就绪，2：进行中，3：未就绪")
    private Map<Integer, Integer> taskCount;
}









