package com.hvisions.pms.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/5/30
 */
@Data
public class HvPmMaterialPreparationQueryDTO extends PageInfo {


    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrderCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipCode;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

    /**
     * 备料明细任务号
     */
    @ApiModelProperty(value = "备料明细任务号")
    private String taskCode;
}
