package com.hvisions.pms.dto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P> 工单手动报工物料信息  <P>
 *
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class HvPmWorkOrderCompleteMaterialDTO {

    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String orderCode;

    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 物料sn码
     */
    @ApiModelProperty(value = "物料pn码")
    private String pn;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private int quality;

    /**
     * 合格数
     */
    @ApiModelProperty(value = "合格数")
    private int qualifiedQty;

    /**
     * 丢失数量
     */
    @ApiModelProperty(value = "丢失数量")
    private int lossQty;

    /**
     * 报废数量
     */
    @ApiModelProperty(value = "报废数量")
    private int scrapQty;

    /**
     * 返修数量
     */
    @ApiModelProperty(value = "返修数量")
    private int repairQty;

    /**
     * 托盘编号
     */
    @ApiModelProperty("托盘编号")
    private String palletCode;
}
