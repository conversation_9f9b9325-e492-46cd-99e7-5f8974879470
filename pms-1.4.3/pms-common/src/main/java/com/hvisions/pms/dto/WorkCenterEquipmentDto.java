package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: WorkCenterEquipmentDto</p >
 * <p>Description: 工单详情--工位下设备</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/9</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
@ApiModel("工单详情--工位下设备")
public class WorkCenterEquipmentDto {

    @ApiModelProperty("设备ID")
    private Integer equipmentId;

    @ApiModelProperty("设备名称")
    private String equipmentName;

    @ApiModelProperty("设备编码")
    private String equipmentCode;

    @ApiModelProperty("类型")
    private Integer codeType;
}
