package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: WorkIssuedDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/1/25</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class WorkIssuedDTO {

    @ApiModelProperty(value = "工单Id列表")
    private List<Integer> orderIds;
    @ApiModelProperty(value = "工单信息")
    private WorkOrderDTO workOrderDTO;
    @ApiModelProperty(value = "工艺路线编码")
    private String nodeCode;
    @ApiModelProperty(value = "工单参数")
    private Map<String, Object> map;
    @ApiModelProperty(value = "状态：用于表示页面传递还是自动 1：页面传输")
    private Integer status;
}