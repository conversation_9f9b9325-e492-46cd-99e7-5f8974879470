package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: ChangeEquipmentDTO</p >
 * <p>Description: 工序切换加工工位</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ChangeWorkCenterDTO {


    /**
     * 工序ID列表
     */
    @ApiModelProperty(value = "工序ID列表")
    private List<Integer> operationS;


    /**
     * 工位ID
     */
    @ApiModelProperty(value = "工位ID")
    private Integer workCenterId;
}
