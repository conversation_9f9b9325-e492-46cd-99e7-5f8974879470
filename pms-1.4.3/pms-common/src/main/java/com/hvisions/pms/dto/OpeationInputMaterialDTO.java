package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>Title: OpeationInputMaterialDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-10-11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OpeationInputMaterialDTO {
    /**
     * 工单工序id
     */
    @ApiModelProperty(value = "工单工序id")
    private Integer operationId;
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "计划物料编码")
    private String materialCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "计划物料名称")
    private String materialName;
    /**
     * 物料特征值
     */
    @ApiModelProperty(value = "计划物料特征值")
    private String materialEigenvalue;
    /**
     * 物料实际绑定的编码
     */
    @ApiModelProperty(value = "物料实际绑定的编码")
    private String materialActualCode;
    /**
     * 计划投料量
     */
    @ApiModelProperty(value = "计划投料量")
    private BigDecimal planCount;



    /**
     * 实际投料量
     */
    @ApiModelProperty(value = "实际投料量")
    private BigDecimal actualCount;


    /**
     * 物料批次号
     */
    @ApiModelProperty(value = "物料批次号")
    private String batchNumber;
}