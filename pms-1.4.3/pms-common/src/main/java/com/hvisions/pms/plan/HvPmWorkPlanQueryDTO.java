package com.hvisions.pms.plan;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>Title: HvPmWorkPlanQueryDTO</p >
 * <p>Description: 生产计划查询DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "生产计划 DTO")
public class HvPmWorkPlanQueryDTO extends PageInfo {

    /**
     * 计划编码
     */
    @ApiModelProperty(value = "计划编码")
    private String planCode;


    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;
    /**
     * 物料特征值
     */
    @ApiModelProperty(value = "物料特征值")
    private String eigenvalue;


    /**
     * 工艺路线编码
     */
    @ApiModelProperty(value = "工艺路线编码")
    private String routeCode;
    /**
     * 工艺路线名称
     */
    @ApiModelProperty(value = "工艺路线名称")
    private String routeName;
    /**
     * 工艺路线版本
     */
    @ApiModelProperty(value = "工艺路线版本")
    private String routeVersion;

    /**
     * 计划开始时间早于
     */
    @ApiModelProperty(value = "计划开始时间早于")
    private Date timeBeforeStartTime;
    /**
     * 计划开始时间晚于
     */
    @ApiModelProperty(value = "计划开始时间晚于")
    private Date timeAfterStartTime;
    /**
     * 计划结束时间早于
     */
    @ApiModelProperty(value = "计划结束时间早于")
    private Date timeBeforeEndTime;
    /**
     * 计划结束时间晚于
     */
    @ApiModelProperty(value = "计划结束时间晚于")
    private Date timeAfterEndTime;


    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id")
    private Integer cellId;


    /**
     * 车间id
     */
    @ApiModelProperty(value = "车间id")
    private Integer areaId;

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    /**
     * 是否完成
     */
    @ApiModelProperty(value = "是否完成")
    private Integer ifFinish;
}