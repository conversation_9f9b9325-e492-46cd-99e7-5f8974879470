package com.hvisions.pms.utils;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hvisions.common.config.coderule.enums.CodeRuleSerializeEnum;
import com.hvisions.common.config.coderule.serialize.RuleValue;
import com.hvisions.common.config.coderule.serialize.SerialNumRuleValue;
import com.hvisions.common.config.coderule.setting.CodeRuleSetting;
import com.hvisions.common.config.coderule.utils.RuleValueUtils;
import com.hvisions.common.config.coderule.utils.SerialCodeUtils;
import com.hvisions.common.exception.BaseKnownException;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024-06-20 13:55
 */
@Component
public class SerialCodeUtilsV2 {
    private static final Logger log = LoggerFactory.getLogger(SerialCodeUtils.class);
    public static final String CODE_RULE_KEY = "hvisions-code-rule:";
    @Value("${h-visions.serial-code.lock-time:30}")
    private Integer timeout;
    private static final Pattern WHIT_SPACE_PATTERN = Pattern.compile("\\s");
    private final StringRedisTemplate redisTemplate;
    private final RedissonClient redissonClient;
    private final ObjectMapper objectMapper;

    @Autowired
    public SerialCodeUtilsV2(StringRedisTemplate redisTemplate, RedissonClient redissonClient, ObjectMapper objectMapper) {
        this.redisTemplate = redisTemplate;
        this.redissonClient = redissonClient;
        this.objectMapper = objectMapper;
    }

    public String generateCode(String ruleCode) {
        return this.generateCode(ruleCode, Collections.emptyMap(), true);
    }

    public String generateCode(String ruleCode, Object bean) {
        return this.generateCode(ruleCode, BeanUtil.beanToMap(bean, new String[0]), true);
    }

    public String generateCode(String ruleCode, Map<String, Object> businessAttr) {
        return this.generateCode(ruleCode, businessAttr, true);
    }

    public String generateTestCode(String ruleCode, Map<String, Object> businessAttr) {
        return this.generateCode(ruleCode, businessAttr, false);
    }

    private String generateCode(String ruleCode, Map<String, Object> businessAttr, Boolean checkState) {
        if (checkState) {
            String ruleState = (String)this.redisTemplate.opsForValue().get("hvisions-code-rule:" + ruleCode + ":enable");
            if (!StringUtils.isBlank(ruleState) && "false".equals(ruleState)) {
                log.warn("规则编码: {},未启用!", ruleCode);
                return "";
            }
        }

        StringBuilder code = new StringBuilder();

        try {
            List<CodeRuleSetting> ruleSettings = this.deserializeRuleSetting(ruleCode);

            String codePiece;
            for(Iterator var6 = ruleSettings.iterator(); var6.hasNext(); code.append(codePiece)) {
                CodeRuleSetting ruleSetting = (CodeRuleSetting)var6.next();
                if (!ruleSetting.getRuleType().equals(CodeRuleSerializeEnum.SERIAL_NUMBER.getType())) {
                    codePiece = ruleSetting.getRuleValue().generateCodePiece(businessAttr, ruleSetting.getRuleCode());
                } else {
                    codePiece = this.getAndSetNextSerialNum(ruleSetting, ruleCode, checkState);
                }
            }
        } catch (JsonProcessingException var9) {
            log.error("规则编码: {},从redis解析规则配置失败!请查看配置是否错误!", ruleCode);
            throw new BaseKnownException("反序列化失败!规则编码:" + ruleCode, new Object[0]);
        }

        return code.toString();
    }

    public List<CodeRuleSetting> deserializeRuleSetting(String ruleCode) throws JsonProcessingException {
        String ruleSetting = (String)this.redisTemplate.opsForValue().get("hvisions-code-rule:" + ruleCode);
        if (StringUtils.isBlank(ruleSetting)) {
            log.warn("规则编码: {},redis中没有对应的规则配置!", ruleCode);
            return Collections.emptyList();
        } else {
            JavaType javaType = this.objectMapper.getTypeFactory().constructParametricType(ArrayList.class, new Class[]{CodeRuleSetting.class});
            List<CodeRuleSetting> ruleSettings = (List)this.objectMapper.readValue(ruleSetting, javaType);
            ruleSettings.sort(Comparator.comparing(CodeRuleSetting::getRuleOrder));
            ruleSettings.forEach((r) -> {
                RuleValue ruleValue = RuleValueUtils.deSerializeStringRuleValue(r.getRuleType(), r.getValue(), r.getRuleCode());
                r.setRuleValue(ruleValue);
            });
            return ruleSettings;
        }
    }

    private String getAndSetNextSerialNum(CodeRuleSetting codeRuleSetting, String ruleCode, Boolean updateSerial) {
        SerialNumRuleValue ruleValue = (SerialNumRuleValue)codeRuleSetting.getRuleValue();
        String serialKey = String.format("%s%s:serial:%s", "hvisions-code-rule:", ruleCode, ruleValue.getKey());
        RLock rLock = this.redissonClient.getLock(serialKey + "_lock");
        rLock.lock((long)this.timeout, TimeUnit.SECONDS);

        String serialVal;
        try {
            serialVal = (String)this.redisTemplate.opsForValue().get(serialKey);
            long expireTime = RuleValueUtils.getExpireTime(ruleValue.getResetType());
            if (StringUtils.isBlank(serialVal)) {
                serialVal = String.valueOf(ruleValue.getFirstValue());
                this.setSerialNum(serialKey, serialVal, expireTime);
            } else if (updateSerial) {
                String next = String.valueOf(Integer.parseInt(serialVal) + ruleValue.getStep());
                serialVal = next;
                this.setSerialNum(serialKey, next, expireTime);
            }
        } finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }

        }

        if (ruleValue.getFill() == 0) {
            return serialVal;
        } else {
            if (serialVal.length() <= ruleValue.getLength()) {
                String fullStr = String.format("%" + ruleValue.getLength() + "s", serialVal);
                String fillChar = StringUtils.isBlank(ruleValue.getFillChar()) ? "0" : ruleValue.getFillChar();
                Matcher matcher = WHIT_SPACE_PATTERN.matcher(fullStr);
                serialVal = matcher.replaceAll(fillChar);
            }

            return serialVal;
        }
    }

    private void setSerialNum(String serialKey, String value, long expireTime) {
        if (expireTime != -1L) {
            this.redisTemplate.opsForValue().set(serialKey, value, expireTime, TimeUnit.SECONDS);
        } else {
            this.redisTemplate.opsForValue().set(serialKey, value);
        }
    }
}
