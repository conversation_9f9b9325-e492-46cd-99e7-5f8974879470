package com.hvisions.pms.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-04-11 10:10
 */
@Data
public class HvPmLineReportMaterialDTO extends PageInfo {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", notes = "主键ID")
    private long id;
    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID", notes = "主表ID")
    private long reportId;
    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号", notes = "物料号")
    private String materialCode;
    /**
     * 物料sn码
     */
    @ApiModelProperty(value = "物料pn码", notes = "物料sn码")
    private String pn;


    /**
     * 料框编号（托盘编号）
     */
    @ApiModelProperty(value = "料框编号（托盘编号）", notes = "料框编号（托盘编号）")
    private String frameCode;
    /**
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量", notes = "数量")
    private Integer quality;
    /**
     * 合格数
     */
    @ApiModelProperty(value = "合格数", notes = "合格数")
    private Integer qualifiedQty;
    /**
     * 丢失数量
     */
    @ApiModelProperty(value = "丢失数量", notes = "丢失数量")
    private Integer lossQty;
    /**
     * 报废数量
     */
    @ApiModelProperty(value = "报废数量", notes = "报废数量")
    private Integer scrapQty;
    /**
     * 返修数量
     */
    @ApiModelProperty(value = "返修数量", notes = "返修数量")
    private Integer repairQty;

    private Date createTime;

    @ApiModelProperty("零件工单编号")
    private String workOrderCode;

    @ApiModelProperty("型材原料编码")
    private String profileCode;

    @ApiModelProperty("开始上料（yyyy-MM-dd HH:mm:ss）")
    private String startFeeding;

    @ApiModelProperty("结束上料 （yyyy-MM-dd HH:mm:ss）")
    private String endFeeding;

    @ApiModelProperty("喷码画线开始 （yyyy-MM-dd HH:mm:ss）")
    private String startCodingMarking;

    @ApiModelProperty("喷码画线结束 （yyyy-MM-dd HH:mm:ss）")
    private String endCodingMarking;

    @ApiModelProperty("切割开始 （yyyy-MM-dd HH:mm:ss）")
    private String startCutting;

    @ApiModelProperty("切割结束 （yyyy-MM-dd HH:mm:ss）")
    private String endCutting;

    @ApiModelProperty("型材下料开始 （yyyy-MM-dd HH:mm:ss）")
    private String startProfileCutting;

    @ApiModelProperty("型材下料结束 （yyyy-MM-dd HH:mm:ss）")
    private String endProfileCutting;

    //切割秘书
    private Double cuttingLength;

    //破边框秘书
    private Double damagedLength;

}
