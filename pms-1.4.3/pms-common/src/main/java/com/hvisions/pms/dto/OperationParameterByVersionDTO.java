package com.hvisions.pms.dto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: OperationParameterByVersionDTO</p >
 * <p>Description: 根据工序ID和版本查询返回DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "参数版本工序查询返回DTO")
public class OperationParameterByVersionDTO extends SysBaseDTO {

    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private Integer operationId;


    /**
     * 生产任务Id
     */
    @ApiModelProperty(value = "生产任务Id")
    private Integer taskId;

    /**
     * 参数类型
     */
    @ApiModelProperty(value = "参数类型")
    private Integer dataType;

    /**
     * 最大值
     */
    @ApiModelProperty(value = "最大值")
    private String maxValue;

    /**
     * 最小值
     */
    @ApiModelProperty(value = "最小值")
    private String minValue;

    /**
     * 参数默认值
     */
    @ApiModelProperty("参数默认值")
    private String defaultValue;

    /**
     * 参数编码
     */
    @ApiModelProperty(value = "参数编码")
    private String parameterCode;

    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称")
    private String parameterName;

    /**
     * 参数类型
     */
    @ApiModelProperty(value = "参数类型")
    private Integer parameterType;

    /**
     * 参数用途 1表示生产 2表示质量
     */
    @ApiModelProperty(value = "参数用途 1表示生产 2表示质量")
    private Integer parameterUsage;
    /**
     * 标准值 仅录入参数 可用
     */
    @ApiModelProperty(value = "标准最小值")
    private String standardValue;

    /**
     * 实际值
     */
    @ApiModelProperty(value = "实际值")
    private String actualValue;

    /**
     * 工序参数版本
     */
    @ApiModelProperty(value = "工序参数版本")
    private Integer parameterVersion;
}
