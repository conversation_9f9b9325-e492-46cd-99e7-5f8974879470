package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>Title: MaterialStatistics</p>
 * <p>Description: 物料统计对象</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/4/7</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class MaterialStatistics {
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    private BigDecimal planQuantity;
    /**
     * 完工数量
     */
    @ApiModelProperty(value = "完工数量")
    private BigDecimal quantity;
}









