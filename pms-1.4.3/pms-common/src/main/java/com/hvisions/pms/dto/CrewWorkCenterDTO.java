package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: CrewEquipmentDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/13</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class CrewWorkCenterDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 工位Id
     */
    @ApiModelProperty(value = "工位Id")
    private Integer workCenterId;


    /**
     * 班组ID
     */
    @ApiModelProperty(value = "班组ID")
    private List<Integer> crewId;
}
