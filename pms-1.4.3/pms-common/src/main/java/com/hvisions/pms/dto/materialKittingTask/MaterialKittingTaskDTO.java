package com.hvisions.pms.dto.materialKittingTask;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 集配任务系统添加DTO
 */
@Data
@ApiModel(description = "集配任务系统添加DTO")
public class MaterialKittingTaskDTO {

    /**
     * 任务编号(PK)
     */
    @ApiModelProperty(value = "任务编号(PK)自动生成")
    private String taskNo;

    /**
     * 关联生产工单号
     */
    @ApiModelProperty(value = "关联生产工单号")
    private String productWorkOrderCode;

    /**
     * 关联需求单号
     */
    @ApiModelProperty(value = "关联需求单号")
    private String requirementCode;

    /**
     * 需求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @ApiModelProperty(value = "需求时间")
    private LocalDateTime requirementTime;

    /**
     * 集配任务区域
     */
    @ApiModelProperty(value = "集配任务区域编码")
    private String areaCode;

    /**
     * 目标料点
     */
    @ApiModelProperty(value = "目标料点编码")
    private String targetPointCode;

    /**
     * 优先级（默认为1，数字越大，优先级越高）
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;

    /**
     * 状态 0-待开始 1-已集配
     */
    @ApiModelProperty(value = "状态 0-待开始 1-已集配")
    private Integer status;

    /**
     * 子项物料列表
     */
    private List<HvPmMaterialBomItemDTO> materialBomItems;

    /**
     * 子项物料已集配数量
     */
    @ApiModelProperty(value = "子项物料已集配数量")
    private Integer isCollectedNumber;

    /**
     * 子项物料绑定托盘号
     */
    @ApiModelProperty(value = "子项物料绑定托盘号")
    private String palletNo;

    /**
     * 托盘当前料点
     */
    @ApiModelProperty(value = "托盘当前料点")
    private String palletCurrentPointCode;

    /**
     * 配送类型（0-自动配送、1-人工配送）
     */
    @ApiModelProperty(value = "配送类型（0-自动配送、1-人工配送）")
    private Integer deliveryType = 0;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 计划配送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime planDeliveryTime;

    /**
     * 状态 (配送任务状态 0-待开始、1-进行中、3-已完成)
     */
    @ApiModelProperty(value = "状态 (配送任务状态 0-待开始、1-进行中、3-已完成)")
    private Integer state;


}
