package com.hvisions.pms.type;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: OrderTypeMaterialDTO</p >
 * <p>Description: 工单类型物料关系</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/1/12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OrderTypeMaterialDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "id")
    protected Integer id;

    /**
     * 工单类型id
     */
    @ApiModelProperty(value = "工单类型id")
    private Integer orderTypeId;

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    /**
     * 是否强制绑定
     */
    @ApiModelProperty(value = "是否强制绑定")
    private boolean isForce = false;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称", readOnly = true)
    private String materialName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", readOnly = true)
    private String materialCode;
}