package com.hvisions.pms.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/10/8
 */
@Data
public class HvPmMaterialPreparationDetailQueryDTO extends PageInfo {

    @ApiModelProperty(value = "主表ID")
    private Long preparationId;
    @ApiModelProperty(value = "任务号")
    private String taskCode;
    @ApiModelProperty(value = "物料号")
    private String materialCode;
    @ApiModelProperty(value = "型材原材规格")
    private String sepces;
    @ApiModelProperty(value = "状态")
    private Integer status;
}
