package com.hvisions.pms.dto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
@Data
public class ShipRawMaterialDTO {
    /**
     * id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    private Long id;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipModel;

    /**
     * 分段
     */
    @ApiModelProperty(value = "分段")
    private String segmentationCode;

    /**
     * 创建时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建时间", notes = "此字段不必传递", readOnly = true)
    protected Date createTime = new Date();

    /**
     * 修改时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新时间", notes = "此字段不必传递", readOnly = true)
    protected Date updateTime = new Date();
}
