package com.hvisions.pms.enums;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

public enum CutPlanStatusEnum {

    //0:未下发，1：已下发 2：未下发3：堆场出库、4：钢板打码中、5：理料间入库、
    // 6：理料间出库、 7：切割开始 8：切割结束 9：开始分拣 10:完成
    UN_SEND(0, "未下发", null, null),
    SEND_OK(1, "已下发", null, null),
    SEND_ERROR(2, "下发失败", null, null),
    DC_OUT_STOCK(3, "堆场出库", "1", "OP0020_堆场出库"),
    STEEL_PLATE_CODING(4, "钢板打码", "2", null),
    LLJ_IN_STOCK(5, "理料间入库", "3", "OP0210_理料间入库"),
    LLJ_OUT_STOCK(6, "理料间出库", "4", "OP0230_理料间出库"),
    CUT_START(7, "切割开始", "5", null),
    CUT_START_LINE_1(7, "切割开始-大板", "5.1", "OP0610_大板钢板上料"),
    CUT_START_LINE_2(7, "切割开始-部件A", "5.2", "OP0710_部件A钢板上料"),
    CUT_START_LINE_3(7, "切割开始-部件B", "5.3", "OP0810_部件B钢板上料"),
    CUT_END(8, "切割结束", "6", null),
    CUT_END_1(8, "切割结束-大板", "6.1", "OP0620_大板切割划线"),
    CUT_END_2(8, "切割结束-部件A", "6.2", "OP0720_部件A切割划线"),
    CUT_END_3(8, "切割结束-部件B", "6.3", "OP0820_部件B切割划线"),
    START_SORTING(9, "开始分拣", "7", null),
    START_SORTING_1(9, "开始分拣-大板", "7.1", "OP0630_大板分拣"),
    START_SORTING_2(9, "开始分拣-部件A", "7.2", "OP0730_部件A分拣"),
    START_SORTING_3(9, "开始分拣-部件B", "7.3", "OP0830_部件B分拣"),
    SMALL_PIECE_SORT_START(90, "小件分拣开始", "7.1", null),
    SMALL_PIECE_SORT_END(91, "小件分拣结束", "7.2", null),
    LARGE_PIECE_SORT_START(92, "大件分拣开始", "7.3", null),
    LARGE_PIECE_SORT_END(93, "大件分拣结束", "7.4", null),
    MANUAL_SORT_START(94, "人工分拣开始", "7.5", null),
    MANUAL_SORT_END(95, "人工分拣结束", "7.6", null),
    PICK_START(96, "分拣开始", "7.7", null),
    FINISH(10, "完成", "8", null),
    FINISH_1(9, "完成-大板", "8.1", "OP0660_完成"),
    FINISH_2(9, "完成-部件A", "8.2", "OP0760_完成"),
    FINISH_3(9, "完成-部件B", "8.3", "OP0860_完成"),
    CANCEL(11,"已取消",null,null);

    static Map<String, CutPlanStatusEnum> enumMap = new HashMap<>();

    static {
        for (CutPlanStatusEnum value : CutPlanStatusEnum.values()) {
            if (value.hgCode != null)
                enumMap.put(value.hgCode, value);
        }
    }

    //根据hgCode获取枚举数据
    public static CutPlanStatusEnum getByHGCode(@NotNull String hgCode) {
        return enumMap.get(hgCode);
    }



    static Map<Integer, CutPlanStatusEnum> enumMap2 = new HashMap<>();

    static {
        for (CutPlanStatusEnum value : CutPlanStatusEnum.values()) {
            if (value.code != null)
                enumMap2.put(value.code, value);
        }
    }

    //根据Code获取枚举数据
    public static CutPlanStatusEnum getEnumByCode(@NotNull Integer code) {
        return enumMap2.get(code);
    }


    private final Integer code;
    private final String name;

    private final String  hgCode;

    private final String defaultStation;

    CutPlanStatusEnum(Integer code, String name, String  hgCode, String defaultStation) {
        this.code = code;
        this.name = name;
        this.hgCode = hgCode;
        this.defaultStation = defaultStation;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String  getHgCode() {
        return this.hgCode;
    }

    public String getDefaultStation() {
        return this.defaultStation;
    }
}
