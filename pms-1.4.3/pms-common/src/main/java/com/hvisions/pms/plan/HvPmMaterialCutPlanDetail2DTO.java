package com.hvisions.pms.plan;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/2
 */
@Data
public class HvPmMaterialCutPlanDetail2DTO {
    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private Long cutPlanId;

    /**
     * 切割计划
     */
    @ApiModelProperty(value = "切割计划")
    private String cutPlanCode;

    /**
     * 操作类型 （堆场出库、理料间入库、理料间出库、切割开始、完成）
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     *  开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     *  结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;
}
