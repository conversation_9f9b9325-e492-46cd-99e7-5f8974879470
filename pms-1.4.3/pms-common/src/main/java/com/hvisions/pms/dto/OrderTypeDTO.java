package com.hvisions.pms.dto;

import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: OrderTypeDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/11/6</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OrderTypeDTO extends SysBaseDTO {


    /**
     * 工单类型编码
     */
    @ApiModelProperty(value = "工单类型编码")
    private String orderTypeCode;

    /**
     * 工单类型名称
     */
    @ApiModelProperty(value = "工单类型名称")
    private String orderTypeName;

    /**
     * 扩展属性
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(name = "扩展属性", value = "扩展属性")
    private Map<String, Object> extend = new HashMap<>();

    /**
     * 工单类型配置的扩展属性
     */
    @ApiModelProperty(value = "工单类型配置的扩展属性")
    private List<TypeExtendRelationDTO> relationDTOList;

}

