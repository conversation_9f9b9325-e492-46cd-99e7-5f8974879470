package com.hvisions.pms.exportdto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hvisions.pms.ExcelConverter.DateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <P>  报工记录导出  <P>
 *
 * <AUTHOR>
 * @date 2025/1/6
 */
@Data
public class LineReportExportDTO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", notes = "主键ID")
    @ExcelIgnore
    private long id;
    /**
     * 工单号
     */
    @ExcelProperty(value = "工单号")
    @ApiModelProperty(value = "工单号", notes = "工单号")
    private String orderCode;
    /**
     * 报工类型
     */
    @ExcelProperty(value = "报工类型")
    @ApiModelProperty(value = "报工类型", notes = "报工类型")
    private String reportType;
    /**
     * 物料编码
     */
    @ExcelProperty(value = "物料编码")
    @ApiModelProperty(value = "物料编码", notes = "物料编码")
    private String materialCode;
    /**
     * 物料型号
     */
    @ExcelProperty(value = "物料型号")
    @ApiModelProperty(value = "物料型号", notes = "物料型号")
    private String materialType;
    /**
     * 船号
     */
    @ExcelProperty(value = "船号")
    @ApiModelProperty(value = "船号", notes = "船号")
    private String shipNumber;
    /**
     * 分段号
     */
    @ExcelProperty(value = "分段号")
    @ApiModelProperty(value = "分段号", notes = "分段号")
    private String segmentationCode;
    /**
     * 报工产线
     */
    @ExcelProperty(value = "报工产线")
    @ApiModelProperty(value = "报工产线", notes = "报工产线")
    private String lineCode;
    /**
     * 工序(工位)编号
     */
    @ExcelProperty(value = "工序(工位)编号")
    @ApiModelProperty(value = "工序(工位)编号", notes = "工序(工位)编号")
    private String stationCode;
    /**
     * 工序(工位)名称
     */
    @ExcelProperty(value = "工序(工位)名称")
    @ApiModelProperty(value = "工序(工位)名称", notes = "工序(工位)名称")
    private String stationName;
    /**
     * 上报时间
     */
    @ExcelProperty(value = "上报时间",converter = DateConverter.class)
    @ApiModelProperty(value = "上报时间", notes = "上报时间")
    private Date reportTime;
    /**
     * 实际开始时间
     */
    @ExcelProperty(value = "实际开始时间",converter = DateConverter.class)
    @ApiModelProperty(value = "实际开始时间", notes = "实际开始时间")
    private Date actualStartTime;
    /**
     * 实际完成时间
     */
    @ExcelProperty(value = "实际完成时间",converter = DateConverter.class)
    @ApiModelProperty(value = "实际完成时间", notes = "实际完成时间")
    private Date actualCompletionTime;
    /**
     * 消耗工时(分)
     */
    @ExcelProperty(value = "消耗工时(分)")
    @ApiModelProperty(value = "消耗工时(分)", notes = "消耗工时(分)")
    private Integer depleteTime;
    /**
     * 生产设备编码
     */
    @ExcelProperty(value = "生产设备编码")
    @ApiModelProperty(value = "生产设备编码", notes = "生产设备编码")
    private String equipmentCode;
    /**
     * 报工用户编码
     */
    @ExcelProperty(value = "报工用户编码")
    @ApiModelProperty(value = "报工用户编码", notes = "报工用户编码")
    private String reportUserCode;
    /**
     * 报工用户姓名
     */
    @ExcelProperty(value = "工单号")
    @ApiModelProperty(value = "报工用户姓名", notes = "报工用户姓名")
    private String reportUserName;
    /**
     * 报工数量
     */
    @ExcelProperty(value = "报工数量")
    @ApiModelProperty(value = "报工数量", notes = "报工数量")
    private Integer reportQty;
    /**
     * 合格数
     */
    @ExcelProperty(value = "合格数")
    @ApiModelProperty(value = "合格数", notes = "合格数")
    private Integer qualifiedQty;
    /**
     * 报废数量
     */
    @ExcelProperty(value = "报废数量")
    @ApiModelProperty(value = "报废数量", notes = "报废数量")
    private Integer scrapQty;
    /**
     * 返修数量
     */
    @ExcelProperty(value = "返修数量")
    @ApiModelProperty(value = "返修数量", notes = "返修数量")
    private Integer repairQty;
    /**
     * mes报工0：是，1：否
     */
    @ExcelIgnore
    @ApiModelProperty(value = "mes报工0：是，1：否", notes = "mes报工0：是，1：否")
    private Integer reportMesFlag;
    /**
     * mes报工时间
     */
    @ExcelProperty(value = "报工时间",converter = DateConverter.class)
    @ApiModelProperty(value = "mes报工时间", notes = "mes报工时间")
    private Date reportMesTime;

    /**
     * 查询时间(开始时间)
     */
    @ExcelIgnore
    private String startTimeUTC;

    /**
     * 查询时间(结束时间)
     */
    @ExcelIgnore
    private String endTimeUTC;

    /**
     * 补充报工
     */
    @ExcelProperty(value = "补充报工")
    @ApiModelProperty(value = "补充报工：0：否，1：是", notes = "补充报工：0：否，1：是")
    private Integer supplement;
}
