package com.hvisions.pms.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>Title: OrderFinishDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/4/25</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OrderFinishDTO implements Serializable {

    /**
     * ID
     */
    @ApiModelProperty(value = "工单ID")
    private Integer id;

    /**
     * 实际产出数量
     */
    @ApiModelProperty(value = "实际产出数量")
    private BigDecimal actualCount;

    @ApiModelProperty(value = "工单编码")
    private String orderCode;

    @ApiModelProperty(value = "工单流水号")
    private Integer serialNum;
}
