package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>Title: WorkAllDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class WorkAllDTO extends WorkOrderDTO {

    /**
     * 工序信息列表
     */
    @ApiModelProperty(value = "工序信息列表")
    private List<OperationAllDTO> operationAllDTOS;

}