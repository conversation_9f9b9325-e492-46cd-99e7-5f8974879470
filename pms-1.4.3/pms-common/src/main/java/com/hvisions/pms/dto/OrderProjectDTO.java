package com.hvisions.pms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: OrderProjectDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/4/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OrderProjectDTO {
    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Integer materialId;


    /**
     * 工艺路线ID
     */
    @ApiModelProperty(value = "工艺路线ID")
    private Integer routeId;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
}