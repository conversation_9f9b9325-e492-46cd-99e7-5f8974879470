package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>Title: OrderBomDTO</p >
 * <p>Description: 生产工单需要的料单 </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OrderBomDTO {


    /**
     * bom编码
     */
    @ApiModelProperty(value = "bom编码")
    private String bomCode;

    /**
     * bom名称
     */
    @ApiModelProperty(value = "bom名称")
    private String bomName;
    /**
     * bom版本
     */
    @ApiModelProperty(value = "bom版本")
    private String bomVersion;
    /**
     * bom单位
     */
    @ApiModelProperty(value = "bom单位")
    private String unit;

    @ApiModelProperty(value = "bom数量")
    private BigDecimal bomQuantity;
    /**
     * 工单需求料
     */
    @ApiModelProperty(value = "需求物料")
    private List<OrderBomItemDTO> orderBomItemDTOS;

    /**
     * 组立套料文件地址
     */
    @ApiModelProperty(value = "组立套料文件地址")
    private String nestingFilePath;


}