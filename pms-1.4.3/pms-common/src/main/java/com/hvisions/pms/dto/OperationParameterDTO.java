package com.hvisions.pms.dto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>Title: OperationParameterDTO</p >
 * <p>Description: 参数DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "工序参数")
public class OperationParameterDTO extends SysBaseDTO {

    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private Integer operationId;

    /**
     * 参数类型
     */
    @ApiModelProperty(value = "参数类型")
    private Integer dataType;

    /**
     * 最大值
     */
    @ApiModelProperty(value = "最大值")
    private String maxValue;

    /**
     * 最小值
     */
    @ApiModelProperty(value = "最小值")
    private String minValue;

    /**
     * 参数默认值
     */
    @ApiModelProperty("参数默认值")
    private String defaultValue;

    /**
     * 参数编码
     */
    @ApiModelProperty(value = "参数编码")
    private String parameterCode;

    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称")
    private String parameterName;

    /**
     * 参数类型
     */
    @ApiModelProperty(value = "参数类型")
    private Integer parameterType;

    /**
     * 参数用途 1表示生产 2表示质量
     */
    @ApiModelProperty(value = "参数用途 1表示生产 2表示质量")
    private Integer parameterUsage;
    /**
     * 标准值 仅录入参数 可用
     */
    @ApiModelProperty(value = "标准最小值")
    private String standardValue;

    /**
     * 实际值和版本
     */
    @ApiModelProperty("实际值和版本")
    private List<ParameterVersionDTO> parameterVersionDTOS;
}
