package com.hvisions.pms.tool;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Value;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: OperationToolExpendDTO</p >
 * <p>Description: 工序刀具消耗对象</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/24</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OperationToolExpendDTO {


    /**
     * 设备ID
     */
    @ApiModelProperty(value = "设备ID")
    private Integer equipmentId;


    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Integer materialId;

    /**
     * 工单数量
     */
    @ApiModelProperty(value = "工单数量")
    private BigDecimal quantity;


    /**
     * 使用时间
     */
    @ApiModelProperty(value = "使用时间")
    private Date time;

}
