package com.hvisions.pms.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: TaskStateCountDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/27</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class TaskStateCountDTO {


    /**
     * 工单状态数量
     */
    @ApiModelProperty(value = "工序状态数量")
    private Integer stateCount;
    /**
     * 工序状态
     */
    @ApiModelProperty(value = "工序状态")
    private Integer state;
}