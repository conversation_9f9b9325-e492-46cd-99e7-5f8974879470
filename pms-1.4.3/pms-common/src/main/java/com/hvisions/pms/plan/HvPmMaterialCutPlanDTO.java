package com.hvisions.pms.plan;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HvPmMaterialCutPlanDTO  implements Serializable {



    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 创建时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建时间", notes = "此字段不必传递", readOnly = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    protected Date createTime = new Date();

    /**
     * 修改时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新时间", notes = "此字段不必传递", readOnly = true)
    protected Date updateTime = new Date();

    /**
     * 创建人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建用户Id", notes = "创建记录时传递", readOnly = true)
    protected Integer creatorId = 0;

    /**
     * 修改人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新用户Id", notes = "更新记录时传递", readOnly = true)
    protected Integer updaterId = 0;



    /**
     * 切割计划编码
     */
    @NotNull(message = "切割计划编码不能为空")
    @ApiModelProperty(value = "切割计划编码")
    private String cutPlanCode;

    /**
     * 切割计划编码
     */
    @ApiModelProperty(value = "切割计划编码")
    private Integer planCode;

    /**
     * 套料批次
     */
    @ApiModelProperty(value = "套料批次")
    private String taoBatchCode;

    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    private String materialType;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer quality;

    /**
     * 长度
     */
    @ApiModelProperty(value = "长度")
    private String length;

    /**
     * 宽度
     */
    @ApiModelProperty(value = "宽度")
    private String width;

    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    private String thickness;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    private String batch;

    /**
     * 余料(0:是,1:否)
     */
    @ApiModelProperty(value = "余料")
    private Integer surplusMaterialFlag;

    /**
     * 切割数量
     */
    @ApiModelProperty(value = "切割数量")
    private Integer cutQuality;

    /**
     * 套料程序文件地址
     */
    @ApiModelProperty(value = "套料程序文件地址")
    private String taoFilePath;

    /**
     * 喷码程序文件地址
     */
    @ApiModelProperty(value = "喷码程序文件地址")
    private String markingFilePath;

    /**
     * 切割程序文件地址
     */
    @ApiModelProperty(value = "切割程序文件地址")
    private String cutFilePath;


    /**
     * 套料PDF文件地址pdf
     */
    @ApiModelProperty(value = "套料PDF文件地址pdf")
    private String pdfFilePath;

    /**
     * 产线ID
     */
    @ApiModelProperty(value = "产线ID")
    private Integer lineId;

    /**
     * 产线Code 接收第三方接口
     */
    @ApiModelProperty(value = "产线Code")
    private String lineCode;

    /**
     * 产线名称
     */
    @ApiModelProperty(value = "产线名称")
    private String lineName;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;

    /**
     * 开始时间PlanStartTime
     */
    private Date planStartTime;

    /**
     * 顺序
     */
    @ApiModelProperty(value = "顺序")
    private Integer sequence;

    /**
     * 下发操作人
     */
    @ApiModelProperty(value = "下发操作人")
    private Integer sendUserId;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    private Date sendTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    /**
     * 下发操作人姓名
     */
    @ApiModelProperty(value = "下发操作人姓名")
    private String userName;

    //0:未下发 、1:已下发 、 2：下失败 、3：堆场出库、4：理料间入库、5：理料间出库、6：切割开始、 7：完成
    private Integer status;

    /**
     * 套料系统报工
     */
    private Date reportTaoTime;

    /**
     * 废料总重量（钢板-零件-余料=废料）
     */
    @ApiModelProperty(value = "废料总重量")
    private String scrapWeight;

    //零件
    private List<HvPmMaterialCutPlanDetail0DTO> detailList;

    //余料
    private List<HvPmMaterialCutPlanDetail1DTO> detail1List;

    // 切割效率
    private String cuttingEff;

    // 钢板利用率
    private String plateUtilRate;

    // 订单满足率
    private String orderFulfillRate;

    // 余料率
    private String scrapRate;

    // 报废率
    private String wasteRate;

    // 钢板总重量
    private String totalPlateWeight;

    // 零件总重量
    private String totalPartWeight;

    // 余料总重量
    private String totalScrapWeight;

    // 整版切割长度
    private String fullCutLength;

    // 整版理论切割时间
    private String fullCutTheoTime;

    // 整版空走长度
    private String idleTravelLength;

    // 整版空走时间
    private String idleTravelTime;

    // 整版穿孔数
    private Integer perfCount;

    //分段号
    private String segmentationCode;

    //船号
    private String shipNumber;
}
