package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HvPmStockMovementDTO {
    private Long id;

    // 任务号
    private String taskNo;
    // 料框编号
    private String palletCode;
    /**
     * 工单编码
     */
    @ApiModelProperty(value = "工单编码", required = true)
    private String workOrderCode;
    //1:完成 0新建
    private Integer state;
    private Integer stockId;
    private long planProductBomId;

}
