package com.hvisions.pms.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: AssignDTO</p >
 * <p>Description: 分配任务</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/1/17</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class AssignDTO {

    @ApiModelProperty(value = "任务id列表")
    private List<Integer> taskIdList;

    @ApiModelProperty(value = "任务id列表")
    private Integer userId;
}