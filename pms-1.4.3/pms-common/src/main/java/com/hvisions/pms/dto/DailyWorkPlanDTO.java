package com.hvisions.pms.dto;

import lombok.Data;

import java.util.List;

/**
 * <P>主页计划数据<P>
 *
 * <AUTHOR>
 * @date 2024/6/12
 */
@Data
public class DailyWorkPlanDTO {
    private double todayPlan; // 本日计划
    private double todayCompletedPlan; //已完成日计划（百分比）
    private double monthAccumulatedOutput; // 本月累计产量
    private double todayCompleted; // 本日完成
    private double monthCompleted; // 本月完成(百分比)
    private double dayOnDayComparison; // 日同比（可能是一个百分比或具体数值）
    private double weekOnWeekComparison; // 周同比（可能是一个百分比或具体数值）
    private double monthOnMonthComparison; // 月同比（可能是一个百分比或具体数值）

    private List<String> sevenPlanDate;//近七日产量日期列表
    private List<Integer> sevenPlanQuantity;//近七日产量任务列表
    private List<DailyWorkPlanTaskDTO> todayPlannedTasks; // 本日计划任务列表
    private List<DailyWorkPlanTaskDTO> todayCompletedTasks; // 本日完成任务列表
    private List<DailyWorkPlanTaskDTO> monthPlannedTasks; // 本月计划任务列表
    private List<DailyWorkPlanTaskDTO> monthCompletedTasks; // 本月完成任务列表

}
