package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: OperationParameterUpdateDTO</p >
 * <p>Description: 参数录入用DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/22</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OperationParameterUpdateDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    protected Integer id;

    @ApiModelProperty(value = "任务Id")
    private Integer taskId;
    /**
     * 实际值
     */
    @ApiModelProperty(value = "实际值")
    private String actualValue;


}
