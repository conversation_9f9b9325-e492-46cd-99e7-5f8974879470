package com.hvisions.pms.enums;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

public enum PolishPlanStatusEnum {

    //0:未下发，1：已下发 2：下发失败 3：备料中、4：备料完成、5：开始切割、
    // 6：完成
    UN_SEND(0, "未下发", null, null),
    SEND_OK(1, "已下发", null, null),
    SEND_ERROR(2, "下发失败", null, null),
    TRANSPORT_ING(3, "搬运中", null, null),
    TRANSPORT_FINISH(4, "搬运完成",null , null),
    WORK_ING(5, "作业中", null, null),
    FINISH(6, "已完成", null, null);

    static Map<Integer, PolishPlanStatusEnum> enumMap = new HashMap<>();

    static {
        for (PolishPlanStatusEnum value : PolishPlanStatusEnum.values()) {
            if (value.hgCode != null)
                enumMap.put(value.hgCode, value);
        }
    }

    public static PolishPlanStatusEnum getByHGCode(@NotNull Integer hgCode) {
        return enumMap.get(hgCode);
    }


    private final Integer code;
    private final String name;

    private final Integer hgCode;

    private final String defaultStation;

    PolishPlanStatusEnum(Integer code, String name, Integer hgCode, String defaultStation) {
        this.code = code;
        this.name = name;
        this.hgCode = hgCode;
        this.defaultStation = defaultStation;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getHgCode() {
        return this.hgCode;
    }

    public String getDefaultStation() {
        return this.defaultStation;
    }
}
