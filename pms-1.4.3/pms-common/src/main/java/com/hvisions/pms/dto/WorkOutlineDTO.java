package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: WorkOutlineDTO</p >
 * <p>Description: 生产概要</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/9</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class WorkOutlineDTO {

    /**
     * 计划已下发
     */
    @ApiModelProperty(value = "")
    private Integer planIssued;


    /**
     * 计划完成
     */
    @ApiModelProperty(value = "计划完成")
    private Integer planFinish;

    /**
     * 计划进行中
     */
    @ApiModelProperty(value = "计划完成")
    private Integer planExecuting;

    /**
     * 工单已下发
     */
    @ApiModelProperty(value = "工单已下发")
    private Integer workIssued;
    /**
     * 工单已完成
     */
    @ApiModelProperty(value = "工单已完成")
    private Integer workFinish;
    /**
     * 工单执行中
     */
    @ApiModelProperty(value = "工单执行中")
    private Integer workExecuting;


    /**
     * 工单已下发
     */
    @ApiModelProperty(value = "工单已下发")
    private Integer workIssued0;
    /**
     * 工单已完成
     */
    @ApiModelProperty(value = "工单已完成")
    private Integer workFinish0;
    /**
     * 工单执行中
     */
    @ApiModelProperty(value = "工单执行中")
    private Integer workExecuting0;



    /**
     * 工单已下发
     */
    @ApiModelProperty(value = "工单已下发")
    private Integer workIssued1;
    /**
     * 工单已完成
     */
    @ApiModelProperty(value = "工单已完成")
    private Integer workFinish1;
    /**
     * 工单执行中
     */
    @ApiModelProperty(value = "工单执行中")
    private Integer workExecuting1;


    /**
     * 工单已下发
     */
    @ApiModelProperty(value = "工单已下发")
    private Integer workIssued2;
    /**
     * 工单已完成
     */
    @ApiModelProperty(value = "工单已完成")
    private Integer workFinish2;
    /**
     * 工单执行中
     */
    @ApiModelProperty(value = "工单执行中")
    private Integer workExecuting2;

    /**
     * 作业工位
     */
    @ApiModelProperty(value = "作业工位")
    private Integer taskWorkCenter;

    /**
     * 工位任务
     */
    @ApiModelProperty(value = "工位任务")
    private Integer workCenterTaskCount;


    public Integer getWorkCenterTaskCount() {
        return taskFinish + taskExecuting;
    }

    /**
     * 任务完成
     */
    @ApiModelProperty(value = "任务完成")
    private Integer taskFinish;

    /**
     * 执行中
     */
    @ApiModelProperty(value = "任务执行中")
    private Integer taskExecuting;

    /**
     * 成品物料
     */
    @ApiModelProperty(value = "成品物料")
    private Integer product;

    /**
     * 半成品
     */
    @ApiModelProperty(value = "半成品物料")
    private Integer semiProduct;

    /**
     * 原材料
     */
    @ApiModelProperty(value = "原材料")
    private Integer rawMaterial;

    /**
     * 成品bom
     */
    @ApiModelProperty(value = "成品bom")
    private Integer bomProduct;

    /**
     * 半成品bom
     */
    @ApiModelProperty(value = "半成品bom")
    private Integer bomSemiProduct;

    /**
     * 工艺路径
     */
    @ApiModelProperty(value = "工艺路径")
    private Integer routeCount;

    /**
     * 成品制造工艺
     */
    @ApiModelProperty(value = "成品制造工艺")
    private Integer productRoute;

    /**
     * 半成品制造工艺
     */
    @ApiModelProperty(value = "半成品制造工艺")
    private Integer semiProductRoute;

    /**
     * 班组
     */
    @ApiModelProperty(value = "班组")
    private Integer crew;

    /**
     * 班次
     */
    @ApiModelProperty(value = "班次")
    private Integer schedule;


    /**
     * 当月排班
     */
    @ApiModelProperty(value = "当月排班")
    private String scheduling;



    /**
     * 人员数量
     */
    @ApiModelProperty(value = "人员工位配置 人员数量")
    private Integer userCount;

    /**
     * 工位配置
     */
    @ApiModelProperty(value = "人员工位配置 工位数量")
    private Integer workCenterCount;

    @ApiModelProperty(value = "交接班配置")
    private Integer changShiftCount;

}