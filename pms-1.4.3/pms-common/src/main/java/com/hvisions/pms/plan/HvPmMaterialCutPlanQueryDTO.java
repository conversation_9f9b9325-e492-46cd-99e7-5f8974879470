package com.hvisions.pms.plan;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "钢板切割计划查询条件")
public class HvPmMaterialCutPlanQueryDTO extends PageInfo {
    /**
     * 切割计划编码
     */
    @ApiModelProperty(value = "切割计划编码")
    private String cutPlanCode;

    /**
     * 切割计划编码
     */
    @ApiModelProperty(value = "切割计划编码")
    private Integer planCode;

    /**
     * 物料类型（钢板原材料编码）
     */
    @ApiModelProperty(value = "物料类型（钢板原材料编码）")
    private String materialType;

    /**
     * 产线ID
     */
    @ApiModelProperty(value = "产线ID")
    private Integer lineId;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;


    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;


    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号")
    private String materialCode;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrder;

    /**
     * 操作开始时间
     */
    @ApiModelProperty(value = "操作开始时间")
    private Date operateStartTime;

    /**
     * 操作结束时间
     */
    @ApiModelProperty(value = "操作结束时间")
    private Date operateEndTime;

    /**
     * 下发开始时间
     */
    @ApiModelProperty(value = "下发开始时间")
    private Date sendBeginTime;

    /**
     * 下发结束时间
     */
    @ApiModelProperty(value = "下发结束时间")
    private Date sendEndTime;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipNumber;

    /**
     * 完成开始时间
     */
    @ApiModelProperty(value = "完成开始时间")
    private String  completionStartTime;

    /**
     * 完成结束时间
     */
    @ApiModelProperty(value = "完成结束时间")
    private String  completionEndTime;

}
