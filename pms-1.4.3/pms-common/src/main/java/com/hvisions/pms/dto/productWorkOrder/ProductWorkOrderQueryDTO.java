package com.hvisions.pms.dto.productWorkOrder;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>Title: ProductWorkOrderDTO</p>
 * <p>Description: 生产工单</p>
 */
@Data
@ApiModel("生产工单查询")
public class ProductWorkOrderQueryDTO extends PageInfo {

    /**
     * 工单编号 (PK)
     */
    @ApiModelProperty(value = "工单编号")
    private String productWorkOrderCode;

    /**
     * 工单属性
     */
    @ExcelProperty(value = "工单属性")
    @ApiModelProperty(value = "工单属性/工单类型")
    private String productWorkOrderAttribute;

    /**
     * 产品名称（物料名称）
     */
    @ExcelProperty(value = "产品名称")
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品编码（物料编号）
     */
    @ExcelProperty(value = "产品编码")
    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 生产产线（产线编号）
     */
    @ExcelProperty(value = "生产产线")
    @ApiModelProperty(value = "生产产线编号")
    private String productionLineCode;

    /**
     * 船号
     */
    @ExcelProperty(value = "船号")
    @ApiModelProperty(value = "船号")
    private String shipCode;

    /**
     * 计划开始时间
     */
    @ExcelProperty(value = "计划开始时间")
    @ApiModelProperty(value = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    @ExcelProperty(value = "计划结束时间")
    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime planEndTime;

    /**
     * 是否集配
     */
    @ExcelProperty(value = "是否集配")
    @ApiModelProperty(value = "是否集配")
    private Integer isCollect;

//
//    /**
//     * 创建时间
//     */
//    @CreatedDate
//    @ExcelProperty(value = "创建时间")
//    @ApiModelProperty(value = "创建时间")
//    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
//    protected LocalDateTime createTime;
//
//    /**
//     * 修改时间
//     */
//    @LastModifiedDate
//    @ExcelProperty(value = "修改时间")
//    @ApiModelProperty(value = "修改时间")
//    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
//    protected LocalDateTime updateTime;
//
//    /**
//     * 创建人
//     */
//    @CreatedBy
//    @ExcelProperty(value = "创建人")
//    @ApiModelProperty(value = "创建人")
//    protected Integer creatorId;
//
//    /**
//     * 修改人
//     */
//    @LastModifiedBy
//    @ExcelProperty(value = "修改人")
//
//    protected Integer updaterId;
//
//    /**
//     * 用于后续saas服务租户字段
//     */
//    @ExcelIgnore
//    protected String siteNum;

}