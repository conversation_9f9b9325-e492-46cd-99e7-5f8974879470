package com.hvisions.pms.enums;

import java.util.HashMap;
import java.util.Map;

public enum WorkOrderUsedTypeEnum {


    PRODUCT(0, "组立"),
    STEEL(1, "板材"),
    XC(2, "型材"),
    ;

    private final Integer code;
    private final String name;

    WorkOrderUsedTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public static Map<Integer, String> map = new HashMap<>();

    static {
        for (WorkOrderUsedTypeEnum value : WorkOrderUsedTypeEnum.values()) {
            map.put(value.getCode(), value.getName());
        }
    }

    public static String getNameByCode(int code) {
        return map.get(code);
    }
}
