package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-04-03 13:53
 */
@Data
public class HvBmFrameMaterialDTO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private long id;
    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    private String frameCode;
    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号")
    private String materialCode;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private String quailty;
    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String creatorId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updaterId;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
