package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>Title: OrderBomItemDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/9/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OrderBomItemDTO {

    /**
     * 物料Id
     */
    @ApiModelProperty(value = "物料ID")
    private Integer materialId;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    /**
     * 特征值
     */
    @ApiModelProperty(value = "特征值")
    private String eigenvalue;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;
    /**
     * 需求量
     */
    @ApiModelProperty(value = "需求数量")
    private BigDecimal quantity;

    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private String inventoryQuantity;

}