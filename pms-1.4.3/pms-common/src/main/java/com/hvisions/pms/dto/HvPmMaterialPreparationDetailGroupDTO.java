package com.hvisions.pms.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description HvPmMaterialPreparationDetailDTO
 * <AUTHOR>
 * @Date 2024-05-21
 */
@Data
public class HvPmMaterialPreparationDetailGroupDTO extends PageInfo {

    @ApiModelProperty(value = "主表ID")
    private Long preparationId;
    @ApiModelProperty(value = "任务号")
    private String taskCode;
    @ApiModelProperty(value = "物料号")
    private String materialCode;
    @ApiModelProperty(value = "型材原材规格")
    private String sepces;
    @ApiModelProperty(value = "需求总数量")
    private Integer reqTotalQuantity;
    @ApiModelProperty(value = "实际总数量")
    private Integer actTotalQuantity;

}
