package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: WorkOrderIssuedDTO</p >
 * <p>Description: 工单下发使用Dto</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/5</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class WorkOrderIssuedDTO {


    /**
     * 工单主键ID
     */
    @ApiModelProperty(value = "工单创建表主键ID")
    private Integer orderId;
    /**
     * 产线ID
     */
    @ApiModelProperty(value = "工单创建表主键ID")
    private Integer cellId;
    /**
     * 班组ID
     */
    @ApiModelProperty(value = "工单创建表主键ID")
    private Integer crewId;
    /**
     * 班次ID
     */
    @ApiModelProperty(value = "工单创建表主键ID")
    private Integer shiftId;
    /**
     * 车间ID
     */
    @ApiModelProperty(value = "工单创建表主键ID")
    private Integer areaId;

}
