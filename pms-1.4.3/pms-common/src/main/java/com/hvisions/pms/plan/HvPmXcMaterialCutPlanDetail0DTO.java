package com.hvisions.pms.plan;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <P> <P>
 *
 * <AUTHOR>
 * @date 2024/8/26
 */
@Data
public class HvPmXcMaterialCutPlanDetail0DTO {

    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private Long orderId;

    /**
     * 作业编号
     */
    @ApiModelProperty(value = "作业编号")
    private String orderNo;

    /**
     * 切割计划子编号
     */
    private String subPlanNo;

    /**
     * 零件数量
     */
    private String qty;

    /**
     * GEN文件地址
     */
    private String genFilePath;


    /**
     * 型材原料物料编码
     */
    private String materialCode;

    /**
     * 废料总长度（型材-零件-余料=废料）
     */
    private String scrapLength;

    /**
     * 型材利用率
     */
    private String plateUtilRate;

    /**
     * 订单满足率
     */
    private String orderFulfillRate;

    /**
     * 余料率
     */
    private String scrapRate;

    /**
     * 报废率
     */
    private String wasteRate;

    /**
     *型材总长度
     */
    private String totalPlateLength;

    /**
     * 零件总长度
     */
    private String totalPartLength;

    /**
     * 余料总长度
     */
    private String totalScrapLength;

    /**
     *  零件信息列表
     */
    private List<HvPmXcMaterialCutPlanDetail1DTO> detail1List;
}
