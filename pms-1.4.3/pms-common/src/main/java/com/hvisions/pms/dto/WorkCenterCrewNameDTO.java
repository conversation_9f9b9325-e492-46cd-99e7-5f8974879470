package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: EquipmentCrewNameDTO</p >
 * <p>Description: 设备班组名称信息</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class WorkCenterCrewNameDTO {


    /**
     * id
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;


    /**
     * 班组ID
     */
    @ApiModelProperty(value = "班组ID")
    private Integer crewId;

    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称")
    private String crewName;

    public String getCrewName() {
        if (crewName == null) {
            return "班组已被删除";
        } else {
            return crewName;
        }
    }

}
