package com.hvisions.pms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <P>  工单手动报工主数据  <P>
 *
 * <AUTHOR>
 * @date 2024/12/5
 */

@Data
public class HvPmWorkOrderCompleteDTO extends SysBaseDTO{

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String orderCode;
    /**
     * 报工类型
     */
    @ApiModelProperty(value = "报工类型 DM:打磨  XC:型材  ZL:组立")
    private String reportType;
    /**
     *物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;
    /**
     * 物料型号
     */
    @ApiModelProperty(value = "物料型号")
    private String materialType;
    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipNumber;
    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;
    /**
     * 报工产线
     */
    @ApiModelProperty(value = "报工产线")
    private String lineCode;
    /**
     * 工序（工位）编号
     */
    @ApiModelProperty(value = "工位编号")
    private String stationCode;
    /**
     * 工序（工位）名称
     */
    @ApiModelProperty(value = "工位名称")
    private String stationName;

    /**
     * 上报时间
     */
    @ApiModelProperty(value = "上报时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;

    /**
     *实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualStartTime;

    /**
     * 实际完成时间
     */
    @ApiModelProperty(value = "实际完成时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualCompletionTime;

    /**
     * 消耗工时(分)
     */
    @ApiModelProperty(value = "消耗工时(分)")
    private Integer depleteTime;
    /**
     * 生产设备编码
     */
    @ApiModelProperty(value = "生产设备编码")
    private String equipmentCode;
    /**
     * 报工用户编码
     */
    @ApiModelProperty(value = "报工用户编码")
    private String reportUserCode;
    /**
     * 报工用户姓名
     */
    @ApiModelProperty(value = "报工用户姓名")
    private String reportUserName;
    /**
     * 报工数量
     */
    @ApiModelProperty(value = "报工数量")
    private int reportQty;
    /**
     * 合格数
     */
    @ApiModelProperty(value = "合格数")
    private int qualifiedQty;
    /**
     * 报废数量
     */
    @ApiModelProperty(value = "报废数量")
    private int scrapQty;
    /**
     * 返修数量
     */
    @ApiModelProperty(value = "返修数量")
    private int repairQty;
}
