package com.hvisions.pms.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-04-03 10:58
 */
@Data
public class HvPmAgvTaskRecordDTO {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private long id;
    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    private String taskCode;
    /**
     * 请求码
     */
    @ApiModelProperty(value = "请求码")
    private String requestCode;
    /**
     * 起点
     */
    @ApiModelProperty(value = "起点")
    private String startPoint;
    /**
     * 终点
     */
    @ApiModelProperty(value = "终点")
    private String endPoint;


    @ApiModelProperty(value = "料框编号")
    private String frameCode;
    /**
     * 料框类型
     */
    @ApiModelProperty(value = "料框类型")
    private String frameTypeCode;
    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    private Integer taskType;

    @ApiModelProperty(value = "任务类型描述,导出显示用")
    private String taskTypeDescription;

    /**
     * AGV编号
     */
    @ApiModelProperty(value = "AGV编号")
    private String agvCode;
    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号")
    private String materialCode;
    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    private String materialType;


    /**
     * 调度状态 -1 : 已发起 0：调度中、1;调度完成、
     2：取消 3：失败
     */
    @ApiModelProperty(value = "调度状态")
    private Integer schedulingState;

    @ApiModelProperty(value = "调度状态描述,导出显示用")
    private String schedulingStateDescription;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createId;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    private String updaterId;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 查询时间(开始时间)
     */
    @ApiModelProperty(value = "查询开始时间(YYYY-MM-DD HH:mm:ss)")
    private String startTimeUTC;

    /**
     * 查询时间(结束时间)
     */
    @ApiModelProperty(value = "查询结束时间(YYYY-MM-DD HH:mm:ss)")
    private String endTimeUTC;

    @ApiModelProperty(value = "请求系统")
    private String requestSystem;//请求系统

    /**
     * 工单编号
     */
    @ExcelProperty(value = "工单编号")
    private String workOrderCode;

    // 方法名
    private String method;
}
