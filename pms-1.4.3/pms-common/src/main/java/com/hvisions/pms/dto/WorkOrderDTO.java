package com.hvisions.pms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.common.interfaces.IExtendObject;
import com.hvisions.common.interfaces.IObjectType;
import com.hvisions.pms.SysBaseDTO;
import com.hvisions.pms.enums.OrderManageObjectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: SysDepartmentDTO</p>
 * <p>Description: 工单创建DTO </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/7</p>
 *
 * <AUTHOR> banziyang
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WorkOrderDTO extends SysBaseDTO implements IObjectType, IExtendObject, Serializable {


    /**
     * 工单计划编码
     */
    @ApiModelProperty(value = "工单计划编码", required = true)
    private String planCode;
    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID", required = true)
    private Integer materialId;
    /**
     * 计划开始时间
     */

    @ApiModelProperty(value = "计划开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planStartTime;
    /**
     * 计划结束时间
     */

    @ApiModelProperty(value = "计划结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planEndTime;
    /**
     * 生产计划 计划开始时间
     */
    @ApiModelProperty(value = "生产计划计划开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workPlanStartTime;
    /**
     * 生产计划 计划结束时间
     */
    @ApiModelProperty(value = "生产计划计划结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workPlanEndTime;

    /**
     * 工艺路线ID
     */
    @ApiModelProperty(value = "工艺路线ID", required = true)
    private Integer routeId;
    /**
     * 工艺路线编码
     */
    @ApiModelProperty(value = "工艺路线编码", readOnly = true)
    private String routeCode;
    /**
     * 工艺路线名称
     */
    @ApiModelProperty(value = "工艺路线名称", readOnly = true)
    private String routeName;
    /**
     * 工艺路线版本
     */
    @ApiModelProperty(value = "工艺路线版本", readOnly = true)
    private String routeVersion;
    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态(0-新建，1-已下发，2-撤销，3-运行，4-暂停，5-结束，6-终止，7-报废，8-报工)", readOnly = true)
    private Integer workOrderState;


    /**
     * bom版本
     */
    @ApiModelProperty(value = "bom版本", readOnly = true)
    private String bomVersion;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 特征值与编码拼接的字符串
     */
    @ApiModelProperty(value = "特征值与编码拼接的字符串", readOnly = true)
    private String materialCodeEigenvalue;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", readOnly = true)
    private String materialCode;

    /***
     * 特征值
     */
    @ApiModelProperty(value = "特征值", readOnly = true)
    private String eigenvalue;

    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间", readOnly = true)

    private Date actualStartTime;
    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间", readOnly = true)
    private Date actualEndTime;
    /**
     * 工单编码
     */
    @ApiModelProperty(value = "工单编码", required = true)
    private String workOrderCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称", readOnly = true)
    private String materialName;
    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号", readOnly = true)
    private Integer serialNumber;
    /**
     * 工单下发时间
     */
    @ApiModelProperty(value = "工单下发时间", readOnly = true)
    private Date issuedTime;

    /**
     * 班次 ID
     */
    @ApiModelProperty(value = "班次 ID")
    private Integer shiftId;

    /**
     * 班次 名称
     */
    @ApiModelProperty(value = "班次 名称", readOnly = true)
    private String shiftName;

    /**
     * 车间ID
     */
    @ApiModelProperty(value = "车间ID")
    private Integer areaId;

    /**
     * 车间名称
     */
    @ApiModelProperty(value = "车间名称")
    private String areaName;

    /**
     * 产线Id
     */
    @ApiModelProperty(value = "产线Id")
    private Integer cellId;

    /**
     * 班组ID
     */
    @ApiModelProperty(value = "班组ID")
    private Integer crewId;

    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称", readOnly = true)
    private String crewName;
    /**
     * bomID
     */
    @ApiModelProperty(value = "bomID", readOnly = true)
    private Integer bomId;

    /**
     * bomName
     */
    @ApiModelProperty(value = "bomName", readOnly = true)
    private String bomName;

    /**
     * bomCode
     */
    @ApiModelProperty(value = "bomCode", readOnly = true)
    private String bomCode;

    /**
     * 实际产出数量
     */
    @ApiModelProperty(value = "实际产出数量")
    private BigDecimal actualCount;

    /**
     * 用于区分计划下发工单还是手工创建工单 计划 1 手动 0
     */
    @ApiModelProperty(value = "用于区分计划下发工单还是手工创建工单", readOnly = true)
    private Integer planOrNew;

    /**
     * 工单下发方式
     */
    @ApiModelProperty(value = "工单下发方式 1 下发全部工序，2 逐步下发工序", readOnly = true)
    private Integer orderMode;

    /**
     * 当前工序id
     */
    @ApiModelProperty(value = "当前工序id")
    private Integer orderOperation;

    /**
     * 当前工序名
     */
    @ApiModelProperty(value = "当前工序名")
    private String orderOperationName;

    /**
     * 工单类型Id
     */
    @ApiModelProperty(value = "工单类型Id")
    private Integer orderTypeId;


    /**
     * 工单类型编码
     */
    @ApiModelProperty(value = "工单类型编码")
    private String orderTypeCode;

    /**
     * 工单类型名称
     */
    @ApiModelProperty(value = "工单类型名称")
    private String orderTypeName;

    /**
     * 工单类型扩展属性
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(name = "工单类型扩展属性", value = "扩展属性")
    private Map<String, Object> typeExtend = new HashMap<>();

    /**
     * 工单类型属性扩展字段名称
     */
    private List<TypeExtendRelationDTO> relationDTOS;

    @Override
    public Integer getObjectType() {
        return OrderManageObjectTypeEnum.ORDER_MANAGE_DTO.getCode();
    }


    /**
     * 扩展属性
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(name = "扩展属性", value = "扩展属性")
    private Map<String, Object> extend = new HashMap<>();

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipNo;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipModel;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

    /**
     * 执行顺序
     */
    @ApiModelProperty(value = "执行顺序")
    private Integer executeSequence;

    private Integer usedType;

    /**
     * 齐套校验状态(0:NG ,1:OK)
     */
    @ApiModelProperty(value = "齐套校验状态")
    private Integer completeSetCheckStatus;


    /**
     * 来源(0:MES ,1:自建)
     */
    @ApiModelProperty(value = "来源")
    private Integer comeFrom;

    @ApiModelProperty(value = "父物料编码")
    private String parentMaterialCode;


    @ApiModelProperty(value = "父工单")
    private String parentWorkOrderCode;

    @ApiModelProperty(value = "mes 下发的 bom")
    private List<HvPmPlanProductBomDTO> bomDTOList;

    //是否外发 0 否 1 是  （零件工单必填）
    private String outFlag;

    //自制/外协  2 自制 1外协（零件工单必填）
    private String specialPurchaseTypeCode;
    //流向代码A1,A2,C1,C2,C3,C4………………

    private String blockCode;

    //齐套时间
    private Date completeTime;
}
