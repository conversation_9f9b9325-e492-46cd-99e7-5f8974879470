package com.hvisions.pms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.pms.dto.materialRequirement.HvPmMaterialBomItemDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MaterialKittingTaskDTO {

    /**
     * 任务编号(PK)
     */
    private String taskNo;

    /**
     * 关联生产工单号
     */
    private String productWorkOrderCode;

    /**
     * 关联需求单号
     */
    private String requirementCode;

    /**
     * 需求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date requirementTime;

    /**
     * 集配任务区域
     */
    private String areaCode;

    /**
     * 目标料点
     */
    private String targetPointCode;

    /**
     * 优先级（默认为1，数字越大，优先级越高）
     */
    private Integer priority;

    /**
     * 状态 0-待开始 1-已集配
     */
    private Integer status;

    /**
     * 子项物料列表
     */
    private List<HvPmMaterialBomItemDTO> materialBomItems;

    /**
     * 子项物料已集配数量
     */
    private Integer isCollectedNumber;

    /**
     * 子项物料绑定托盘号
     */
    private String palletNo;

    /**
     * 托盘当前料点
     */
    private String palletCurrentPointCode;
}
