package com.hvisions.pms.dto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/5/22
 */
@Data
public class ShipRawMaterialDetailDTO {
    /**
     * id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    private Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private Long rawMaterialId;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specifications;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer quantity;
}
