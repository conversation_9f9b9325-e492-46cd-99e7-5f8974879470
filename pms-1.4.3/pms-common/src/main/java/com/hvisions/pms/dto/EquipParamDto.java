package com.hvisions.pms.dto;

import com.hvisions.pms.enums.ParamTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: EquipCollectParamDto</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/9</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
@ApiModel("设备采集/下发参数")
public class EquipParamDto {

    @ApiModelProperty("参数编码")
    private String parameterCode;

    @ApiModelProperty("参数名称")
    private String parameterName;

    @ApiModelProperty(value = "开始自动采集")
    private Boolean collectWhenStart;
    @ApiModelProperty(value = "结束自动采集")
    private Boolean collectWhenFinish;
    @ApiModelProperty(value = "工单开始默认下发")
    private Boolean issueWhenStart;

    @ApiModelProperty("标识")
    private String iotTag;

    @ApiModelProperty("单位id")
    private String unit;

    @ApiModelProperty("单位名称")
    private String unitName;

    @ApiModelProperty("标准上限")
    private String maxValue;

    @ApiModelProperty("标准下限")
    private String minValue;

    @ApiModelProperty("当前值")
    private String currentValue;

    @ApiModelProperty("标准值")
    private String defaultValue;

    @ApiModelProperty("参数类型")
    private Integer parameterDataType;

    @ApiModelProperty("是否是数字")
    private Boolean number;

    public Boolean getNumber() {
        return ParamTypeEnum.isNumber(this.parameterDataType);
    }

    public String getConfigName() {
        int i = this.iotTag.indexOf("#");
        if (i == -1) {
            return "";
        }
        return this.iotTag.substring(0, i);
    }

    public String getIotCode() {
        int begin = this.iotTag.indexOf("#");
        if (begin == -1) {
            return "";
        }
        int end = this.iotTag.lastIndexOf("#");
        if (end == -1) {
            return "";
        }
        return this.iotTag.substring(begin + 1, end);
    }

    public String getIotParameterName(){
        int begin = this.iotTag.lastIndexOf("#");
        if (begin == -1) {
            return "";
        }
        return this.iotTag.substring(begin + 1);
    }
}
