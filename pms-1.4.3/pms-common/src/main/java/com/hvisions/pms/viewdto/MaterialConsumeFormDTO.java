package com.hvisions.pms.viewdto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: MaterialConsumeFormDTO</p >
 * <p>Description: 物料消耗报表数据</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@ApiModel(value = "物料投入报表DTO")
public class MaterialConsumeFormDTO {

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Integer materialId;


    /**
     * 计划投料量
     */
    @ApiModelProperty(value = "计划投料量")
    private BigDecimal sumPlanCount;


    /**
     * 实际投料量
     */
    @ApiModelProperty(value = "实际投料量")
    private BigDecimal sumActualCount;



    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;


}
