package com.hvisions.pms.type;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: ReturnOrderTypeMaterialDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/1/12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ReturnOrderTypeMaterialDTO {

    /**
     * 工单类型id
     */
    @ApiModelProperty(value = "工单类型id")
    private Integer orderTypeId;

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    /**
     * 工单类型编码
     */
    @ApiModelProperty(value = "工单类型编码")
    private String orderTypeCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "工单类型编码")
    private String materialName;

    /**
     * 工单类型名称
     */
    @ApiModelProperty(value = "工单类型名称")
    private String orderTypeName;

    /**
     * 是否已绑定
     */
    @ApiModelProperty(value = "是否已绑定")
    private boolean isAlreadyBound;
}