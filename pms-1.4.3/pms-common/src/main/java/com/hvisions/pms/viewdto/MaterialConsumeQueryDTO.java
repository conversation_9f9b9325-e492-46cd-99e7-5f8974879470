package com.hvisions.pms.viewdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: MaterialConsumeQueryDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class MaterialConsumeQueryDTO {

    /**
     * 工单计划开始时间
     */
    @ApiModelProperty(value = "工单计划开始时间")
    private Date planStartTime;

    /**
     * 工单计划结束时间
     */
    @ApiModelProperty(value = "工单计划结束时间")
    private Date planEndTime;

}
