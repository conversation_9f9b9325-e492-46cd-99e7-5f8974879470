package com.hvisions.pms.enums;

/**
 * 0：调度中、1;调度完成、
 * 2：取消 3：失败
 */
public enum SchedulingStateEnum {

    SEND_OK(-1, "已发起"),
    SCHEDULING(0, "调度中"),
    FINISH(1, "调度完成"),
    CANCEL(2, "取消"),
    FAIL(3, "失败"),
     ;

    private final Integer code;
    private final String name;

    SchedulingStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getType() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}
