package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: ProcedureRecordDTO</p >
 * <p>Description: 工序操作记录传输对象</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/15</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ProcedureRecordDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    /**
     * 工序操作时间
     */
    @ApiModelProperty(value = "工序操作时间")
    private Date operationTime;

    /**
     * 操作后工序状态
     */
    @ApiModelProperty(value = "操作后工序状态")
    private Integer operationState;

    /**
     * 用户名称
     */
    @ApiModelProperty(value = "用户名称")
    private String userName;

    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private Integer operationId;


    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Integer taskId;

}
