package com.hvisions.pms.rh.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>Title: MaterialDemandDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/3/12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class MaterialDemandDTO {

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "计划物料编码")
    private String materialCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "计划物料名称")
    private String materialName;

    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    private String materialType;
    /**
     * 物料特征值
     */
    @ApiModelProperty(value = "计划物料特征值")
    private String materialEigenvalue;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    private String materialUom;

    /**
     * 需求量
     */
    @ApiModelProperty(value = "需求量")
    private BigDecimal planCount;
}