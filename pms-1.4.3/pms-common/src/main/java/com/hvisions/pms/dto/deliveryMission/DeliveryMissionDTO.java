package com.hvisions.pms.dto.deliveryMission;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DeliveryMissionDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 配送任务编号(PK)
     */
    @ApiModelProperty(value = "配送任务编号(PK)")
    private String deliveryMissionCode;

    /**
     * 关联生产工单号
     */
    @ApiModelProperty(value = "关联生产工单号")
    private String productWorkOrderCode;

    /**
     * 关联需求单号
     */
    @ApiModelProperty(value = "关联需求单号")
    private String requirementCode;

    /**
     * 优先级（默认为1，数字越大，优先级越高）
     */
    @ApiModelProperty(value = "优先级（默认为1，数字越大，优先级越高）")
    private Integer priority;

    /**
     * 托盘编号
     */
    @ApiModelProperty(value = "托盘编号")
    private String palletNo;

    /**
     * 托盘类型
     */
    @ApiModelProperty(value = "托盘类型")
    private String palletType;

    /**
     * 出发点
     */
    @ApiModelProperty(value = "出发点")
    private String startPointCode;

    /**
     * 目标点
     */
    @ApiModelProperty(value = "目标点")
    private String targetPointCode;

    /**
     * 配送状态(0-待开始、1-进行中、2-已完成、3-已取消)
     */
    @ApiModelProperty(value = "配送状态(0-待开始、1-进行中、2-已完成、3-已取消)")
    private Integer status;

    /**
     * 配送类型（0-自动配送、1-人工配送）
     */
    @ApiModelProperty(value = "配送类型（0-自动配送、1-人工配送）")
    private Integer deliveryType;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 计划配送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @ApiModelProperty(value = "计划配送时间")
    private LocalDateTime planDeliveryTime;

    /**
     * 实际配送开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @ApiModelProperty(value = "实际配送开始时间")
    private LocalDateTime actualStartTime;

    /**
     * 配送完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @ApiModelProperty(value = "配送完成时间")
    private LocalDateTime actualEndTime;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "创建人")
    private String creatorName;

    @ApiModelProperty(value = "更新人")
    private String updaterName;


}
