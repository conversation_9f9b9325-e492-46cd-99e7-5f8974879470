package com.hvisions.pms.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: OperationQueryDTO</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/1/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperationQueryDTO extends PageInfo {
    /**
     * 工位id
     */
    @ApiModelProperty(value = "工位id")
    private Integer workCenterId;
    /**
     * 工单步骤状态
     */
    @ApiModelProperty(value = "工单步骤状态")
    private Integer state;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "工单编码")
    private String workOrderCode;

    @ApiModelProperty(value = "是否分配")
    private Boolean isAssign;

    @ApiModelProperty(value = "任务执行人")
    private Integer userId;
}









