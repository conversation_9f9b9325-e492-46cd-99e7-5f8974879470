package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>Title: ParamHistoryDataDto</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/15</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
public class ParamHistoryDataDto {

    @ApiModelProperty("记录时间")
    private LocalDateTime recordTime;

    @ApiModelProperty("操作结果")
    private Integer status;

    @ApiModelProperty("参数记录列表")
    private List<RouteParamDataDetailDto> paramDataDetailDtos;
}
