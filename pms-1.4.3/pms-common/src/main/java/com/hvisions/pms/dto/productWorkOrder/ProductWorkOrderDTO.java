package com.hvisions.pms.dto.productWorkOrder;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>Title: ProductWorkOrderDTO</p>
 * <p>Description: 生产工单</p>
 */
@Data
@ApiModel("生产工单")
public class ProductWorkOrderDTO {

    /**
     * 工单编号 (PK)
     */
    @ExcelProperty(value = "工单编号")
    @ApiModelProperty(value = "工单编号")
    private String productWorkOrderCode;

    /**
     * 工单属性
     */
    @ExcelProperty(value = "工单属性")
    @ApiModelProperty(value = "工单属性")
    private String productWorkOrderAttribute;

    /**
     * 产品名称（物料名称）
     */
    @ExcelProperty(value = "产品名称")
    @ApiModelProperty(value = "产品名称")
    private String productName;

    /**
     * Bom版本
     */
    @ApiModelProperty(value = "Bom版本")
    private String bomVersion;

    /**
     * 产品编码（物料编号）
     */
    @ExcelProperty(value = "产品编码")
    @ApiModelProperty(value = "产品编码")
    private String productCode;

    /**
     * 计划数量
     */
    @ExcelProperty(value = "计划数量")
    @ApiModelProperty(value = "计划数量")
    private Integer planQuantity;

    /**
     * 生产产线（产线编号）
     */
    @ExcelProperty(value = "生产产线")
    @ApiModelProperty(value = "生产产线编号")
    private String productionLineCode;

    /**
     * 船号
     */
    @ExcelProperty(value = "船号")
    @ApiModelProperty(value = "船号")
    private String shipCode;

    /**
     * 计划开始时间
     */
    @ExcelProperty(value = "计划开始时间")
    @ApiModelProperty(value = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    @ExcelProperty(value = "计划结束时间")
    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime planEndTime;

    /**
     * 是否集配
     */
    @ExcelProperty(value = "是否集配")
    @ApiModelProperty(value = "是否集配")
    private Integer isCollect;

}