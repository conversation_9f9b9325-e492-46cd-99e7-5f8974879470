package com.hvisions.pms.dto.materialRequirement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 中间表
 */
@Data
@ApiModel(description = "物料需求清单Bom（子项Bom）")
public class HvPmMaterialBomItemDTO {

//    子项物料编码
    @ApiModelProperty(value = "子项物料编码")
    private String materialCode;

    @ApiModelProperty(value = "子项物料名称")
    private String materialName;

    @ApiModelProperty(value = "子项物料单位名称")
    private String materialUnitName;

    @ApiModelProperty(value = "子项物料需求量")
    private BigDecimal materialRequiredQuantity;

//    子项物料对应的物料需求编号
    @ApiModelProperty(value = "物料需求编号")
    private String materialRequirementCode;
}
