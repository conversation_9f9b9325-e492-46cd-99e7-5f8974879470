package com.hvisions.pms.dto.materialRequirement;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.excel.LocalDateTimeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: MaterialRequirementDTO</p>
 * <p>Description: 物料需求</p>
 */
@Data
@ApiModel("物料需求")
public class MaterialRequirementDTO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ExcelIgnore
    protected Integer id;

    /**
     * 物料需求编号 (PK)
     */
    @ExcelProperty(value = "物料需求编号")
    private String requirementCode;

    /**
     * 工单编码
     */
    @ApiModelProperty(value = "工单编码")
    private String workOrderCode;

    /**
     * 目标产线编号
     */
    @ApiModelProperty(value = "目标产线")
    private String cellId;

    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private LocalDateTime workPlanStartTime;

    /**
     * 优先级
     */
    @ExcelProperty(value = "优先级")
    private Integer priority = 1;

    /**
     * 是否已集配
     */
    @ExcelProperty(value = "是否已集配")
    private Integer isAssembled;

    /**
     * 是否已关闭
     */
    @ExcelProperty(value = "是否已关闭")
    private Integer isClose;

    /**
     * Bom子项物料列表
     */
    @ApiModelProperty(value = "Bom子项物料列表")
    private List<HvPmMaterialBomItemDTO> bomItems;


    /**
     * 子项物料已集配数量
     */
    @ApiModelProperty(value = "子项物料已集配数量")
    @ExcelProperty(value = "子项物料已集配数量")
    private Integer bomItemAssembledCount;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @ExcelProperty(value = "创建时间",converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    protected LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @ExcelProperty(value = "修改时间",converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    protected LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @ExcelProperty(value = "创建人")
    protected String creatorName;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @ExcelProperty(value = "修改人")
    protected String updaterName;


    /**
     * 用于后续saas服务租户字段
     */
    @ExcelIgnore
    protected String siteNum;


}