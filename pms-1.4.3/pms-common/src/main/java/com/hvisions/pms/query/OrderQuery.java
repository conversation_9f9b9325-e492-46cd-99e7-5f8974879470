package com.hvisions.pms.query;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>Title: OrderQuery</p>
 * <p>Description: 工单查询对象</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
@Setter
@ToString
public class OrderQuery extends PageInfo {
    /**
     * 工单计划编码(相等)
     */
    @ApiModelProperty(value = "工单计划编码")
    private String planCodeEqual;
    /**
     * 工单计划编码(包含)
     */
    @ApiModelProperty(value = "工单计划编码")
    private String planCodeLike;
    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Integer materialId;
    /**
     * 计划开始时间开始
     */
    @ApiModelProperty(value = "计划开始时间开始")
    private Date planStartTimeStart;
    /**
     * 计划开始时间结束
     */
    @ApiModelProperty(value = "计划开始时间结束")
    private Date planStartTimeEnd;
    /**
     * 计划结束时间开始
     */
    @ApiModelProperty(value = "计划结束时间开始")
    private Date planEndTimeStart;
    /**
     * 计划结束时间结束
     */
    @ApiModelProperty(value = "计划结束时间结束")
    private Date planEndTimeEnd;

    /**
     * 计划时间范围开始
     */
    @ApiModelProperty(value = "计划时间范围开始")
    private String  timeAreaStart;
    /**
     * 计划时间范围结束
     */
    @ApiModelProperty(value = "计划时间范围结束")
    private String timeAreaEnd;

    /**
     * 计划时间范围数组
     */
    @ApiModelProperty(value = "计划时间范围数组")
    private List<String> planStartTime;
    /**
     * 工艺路线ID
     */
    @ApiModelProperty(value = "工艺路线ID")
    private Integer routeId;
    /**
     * 工艺路线编码
     */
    @ApiModelProperty(value = "工艺路线编码")
    private String routeCode;
    /**
     * 工艺路线名称
     */
    @ApiModelProperty(value = "工艺路线名称")
    private String routeName;
    /**
     * 工艺路线版本
     */
    @ApiModelProperty(value = "工艺路线版本")
    private String routeVersion;
    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态(0-新建，1-已下发，2-撤销，3-运行，4-暂停，5-结束，6-终止，7-报废，8-报工)")
    private List<Integer> workOrderStates;

    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态")
    private Integer workOrderState;
    /**
     * bom版本
     */
    @ApiModelProperty(value = "bom版本")
    private String bomVersion;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 物料编码(包含)
     */
    @ApiModelProperty(value = "物料编码(包含)")
    private String materialCodeLike;

    /**
     * 物料编码(相等)
     */
    @ApiModelProperty(value = "物料编码(相等)")
    private String materialCodeEqual;

    /***
     * 特征值
     */
    @ApiModelProperty(value = "特征值")
    private String eigenvalue;
    /**
     * 实际开始时间开始
     */
    @ApiModelProperty(value = "实际开始时间开始")
    private Date actualStartTimeStart;
    /**
     * 实际开始时间结束
     */
    @ApiModelProperty(value = "实际开始时间结束")
    private Date actualStartTimeEnd;
    /**
     * 实际结束时间开始
     */
    @ApiModelProperty(value = "实际结束时间开始")
    private Date actualEndTimeStart;
    /**
     * 实际结束时间结束
     */
    @ApiModelProperty(value = "实际结束时间结束")
    private Date actualEndTimeEnd;
    /**
     * 工单编码等于
     */
    @ApiModelProperty(value = "工单编码等于")
    private String workOrderCodeEqual;
    /**
     * 工单编码包含
     */
    @ApiModelProperty(value = "工单编码包含")
    private String workOrderCodeLike;
    /**
     * 物料名称相等
     */
    @ApiModelProperty(value = "物料名称相等")
    private String materialNameEqual;
    /**
     * 物料名称包含
     */
    @ApiModelProperty(value = "物料名称包含")
    private String materialNameLike;
    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private Integer serialNumber;
    /**
     * 工单下发时间开始
     */
    @ApiModelProperty(value = "工单下发时间开始")
    private Date issuedTimeStart;
    /**
     * 工单下发时间结束
     */
    @ApiModelProperty(value = "工单下发时间结束")
    private Date issuedTimeEnd;
    /**
     * 班次 ID
     */
    @ApiModelProperty(value = "班次 ID")
    private Integer shiftId;

    /**
     * 班次 名称
     */
    @ApiModelProperty(value = "班次 名称")
    private String shiftName;

    /**
     * 车间ID
     */
    @ApiModelProperty(value = "车间ID")
    private Integer areaId;
    /**
     * 产线Id
     */
    @ApiModelProperty(value = "产线Id")
    private Integer cellId;

    /**
     * 班组ID
     */
    @ApiModelProperty(value = "班组ID")
    private Integer crewId;

    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称")
    private String crewName;
    /**
     * bomID
     */
    @ApiModelProperty(value = "bomID")
    private Integer bomId;
    /**
     * 用于区分计划下发工单还是手工创建工单 计划 1 手动 0
     */
    @ApiModelProperty(value = "用于区分计划下发工单还是手工创建工单")
    private Integer planOrNew;
    /**
     * 工单下发方式
     */
    @ApiModelProperty(value = "工单下发方式 1 下发全部工序，2 逐步下发工序")
    private Integer orderMode;


    /**
     * 工单类型Id
     */
    @ApiModelProperty(value = "工单类型Id")
    private String orderTypeId;


    /**
     * 工单类型编码
     */
    @ApiModelProperty(value = "工单类型编码")
    private String orderTypeCode;

    /**
     * 工单类型名称
     */
    @ApiModelProperty(value = "工单类型名称")
    private String orderTypeName;
    /**
     * 扩展属性查询条件
     */
    private List<ExtendQuery> extendQueries;

    private Integer usedType;

    private Integer comeFrom;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipNo;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipModel;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;


    /**
     * 零件物料编码
     */
    @ApiModelProperty(value = "零件物料编码")
    private String  partMaterialCode;

}









