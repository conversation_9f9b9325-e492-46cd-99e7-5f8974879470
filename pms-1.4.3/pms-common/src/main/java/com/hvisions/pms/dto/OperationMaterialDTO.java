package com.hvisions.pms.dto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: HvPmOperationMaterial</p>
 * <p>Description: 工单工序与物料关系表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/1/20</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "工单工序与物料的关系")
public class OperationMaterialDTO extends SysBaseDTO {
    /**
     * 工单工序id
     */
    @NotNull(message = "工单工序id生不能为空")
    @ApiModelProperty(value = "工单工序id")
    private Integer operationId;
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "计划物料编码")
    private String materialCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "计划物料名称")
    private String materialName;
    /**
     * 物料特征值
     */
    @ApiModelProperty(value = "计划物料特征值")
    private String materialEigenvalue;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    private String unit;
    /**
     * 物料实际绑定的编码
     */
    @ApiModelProperty(value = "物料实际绑定的编码")
    private String materialActualCode;
    /**
     * 计划投料量
     */
    @ApiModelProperty(value = "计划投料量")
    private BigDecimal planCount;

    /**
     * 生产任务ID
     */
    @NotNull(message = "生产任务Id不能为空")
    @ApiModelProperty(value = "生产任务Id")
    private Integer taskId;


    private List<OperationMaterialBatchNumDTO> batchNumDTOList = new ArrayList<>();

}









