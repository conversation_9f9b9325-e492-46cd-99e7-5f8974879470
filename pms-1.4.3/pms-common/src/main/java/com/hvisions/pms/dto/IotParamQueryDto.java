package com.hvisions.pms.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>Title: IotParamDto</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/16</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
public class IotParamQueryDto {

    private String configName;

    private List<IotParamCodeQuery> iotParamCodeQueries;

    @Data
    public static class IotParamCodeQuery {
        private String code;

        private List<String> fields;
    }
}
