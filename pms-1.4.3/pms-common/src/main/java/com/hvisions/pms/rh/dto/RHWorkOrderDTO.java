package com.hvisions.pms.rh.dto;

import com.hvisions.pms.dto.OrderOperationDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class RHWorkOrderDTO {

    /**
     *
     */
    @ApiModelProperty(value = "工单号")
    private String orderNo;
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    @ApiModelProperty(value = "Bom编码")
    private String bomNo;
    @ApiModelProperty(value = "物料名称")
    private String materialName;
    @ApiModelProperty(value = "物料编码")
    private String materialCode;
    @ApiModelProperty(value = "工艺路线名称")
    private String craftRouteName;
    @ApiModelProperty(value = "计划状态")
    private Integer planStatus;
    @ApiModelProperty(value = "完工状态")
    private String completedStatus;
    @ApiModelProperty(value = "计划开始时间")
    private String planStartDate;
    @ApiModelProperty(value = "计划结束时间")
    private String planEndDate;
    @ApiModelProperty(value = "工序列表")
    private List<OrderOperationDTO> operationIds;


}
