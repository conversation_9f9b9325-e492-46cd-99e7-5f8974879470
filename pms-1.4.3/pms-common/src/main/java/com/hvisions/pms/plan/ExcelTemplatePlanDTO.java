package com.hvisions.pms.plan;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: ExcelTemplatePlanDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020-02-07</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class ExcelTemplatePlanDTO {
    /**
     * 计划编码
     */
    @ApiModelProperty(value = "计划编码")
    private String planCode;


    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;


    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 物料特征值
     */
    @ApiModelProperty(value = "物料特征值")
    private String eigenvalue;

    /**
     * 工艺路线编码
     */
    @ApiModelProperty(value = "工艺路线编码")
    private String routeCode;
    /**
     * 工艺路线名称
     */
    @ApiModelProperty(value = "工艺路线名称")
    private String routeName;
    /**
     * 工艺路线版本
     */
    @ApiModelProperty(value = "工艺路线版本")
    private String routeVersion;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;
    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    private BigDecimal quantity;

    /**
     * 车间编码
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "车间编码")
    private String areaCode;

    /**
     * 产线编码
     */
    @ApiModelProperty(value = "产线编码")
    private String cellCode;
}