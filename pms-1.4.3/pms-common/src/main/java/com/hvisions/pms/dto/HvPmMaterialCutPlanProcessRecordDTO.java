package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-06-28 9:16
 */
@Data
public class HvPmMaterialCutPlanProcessRecordDTO {
    private Long id;

    /**
     * 切割产线编号
     */

    @ApiModelProperty(value = "切割计划编号")
    private String lineCode;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrderNo;

    /**
     * 切割计划流程ID  焊接线 1 ; 切割线 2 ; 型材切割B线 3
     */
    @ApiModelProperty(value = " 切割计划流程ID  焊接线 1 ; 切割线 2 ; 型材切割B线 3")
    private Integer processId;


    /**
     * 切割计划流程步骤ID
     */
    @ApiModelProperty(value = "切割计划流程步骤ID")
    private Integer stepId;

    /**
     * 切割计划流程步骤名称
     */
    @ApiModelProperty(value = "切割计划流程步骤名称")
    private String stepName;

    /**
     * 切割计划流程步骤执行顺序
     */
    @ApiModelProperty(value = "切割计划流程步骤执行顺序")
    private Integer sequence;


    /**
     * 切割步骤状态: 0:未开始,1:已完成
     */
    @ApiModelProperty(value = "切割步骤状态: 0:未开始,1:已完成")
    private Integer stepStatus;

    /**
     *步骤完成时间
     */
    @ApiModelProperty(value = "步骤完成时间")
    private Date stepEndTime;

    /**
     *创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createDateTime;
}
