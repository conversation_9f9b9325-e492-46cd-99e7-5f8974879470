package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <p>Title: RouteParamQuery</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/14</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RouteParamQuery {
    @ApiModelProperty("工艺操作id")
    @NotNull(message = "工艺操作id不能为空")
    private Integer operationId;

    @ApiModelProperty("位置id")
    @NotNull(message = "位置id不能为空")
    private Integer positionId;

    @ApiModelProperty("位置类型,0: 工位;1: 设备")
    @NotNull(message = "位置类型不能为空")
    private Integer positionType;
}
