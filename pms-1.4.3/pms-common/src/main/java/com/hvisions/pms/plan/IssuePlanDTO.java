package com.hvisions.pms.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: IssuePlanDTO</p >
 * <p>Description: 下发计划DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/28</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class IssuePlanDTO {

    /**
     * 计划编号
     */
    @ApiModelProperty(value = "计划编号")
    private String planCode;
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;
    /**
     * 工艺路径id
     */
    @ApiModelProperty(value = "工艺路径id")
    private Integer routeId;
    /**
     * 车间id
     */
    @ApiModelProperty(value = "车间id")
    private Integer areaId;
    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id")
    private Integer cellId;
    /**
     * 序列号
     */
    @ApiModelProperty(value = "序列号")
    private String serialNumber;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrderCode;
    /**
     * 下发数量
     */
    @ApiModelProperty(value = "下发数量")
    private BigDecimal quantity;
}