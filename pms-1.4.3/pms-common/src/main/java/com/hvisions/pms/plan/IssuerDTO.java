package com.hvisions.pms.plan;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class IssuerDTO {


    @ApiModelProperty(value = "生产计划id")
    private Integer id;
    @ApiModelProperty(value = "下发次数")
    private BigDecimal quantity;
    @ApiModelProperty(value = "下发工单数量")
    private BigDecimal orderQuantity;
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    @ApiModelProperty(value = "产线Id")
    private Integer cellId;
    @ApiModelProperty(value = "车间ID")
    private Integer areaId;
    @ApiModelProperty(value = "下发工单类型")
    private Integer orderTypeId;
}
