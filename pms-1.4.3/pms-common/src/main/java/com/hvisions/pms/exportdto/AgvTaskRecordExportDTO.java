package com.hvisions.pms.exportdto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.pms.ExcelConverter.DateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <P>  调度记录导出DTO  <P>
 *
 * <AUTHOR>
 * @date 2025/1/2
 */
@Data
public class AgvTaskRecordExportDTO {

    /**
     * 主键ID
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
     * 请求系统
     */
    @ApiModelProperty(value = "请求系统")
    @ExcelProperty(value = "请求系统")
    private String requestSystem;

    /**
     * 请求用户编号
     */
//    @ApiModelProperty(value = "请求用户编号")
//    @ExcelProperty(value = "请求用户编号")
//    private String requestUser;

    /**
     * 请求时间
     */
//    @ApiModelProperty(value = "请求时间")
//    @ExcelProperty(value = "请求时间")
//    private Date requestTime;


    /**
     * 任务类型 字典：task_type：10 空框请求、20：满框调度、30：空框回库、40：生产叫料、50：空框流转
     */
    @ApiModelProperty(value = "任务类型")
    @ExcelProperty(value = "任务类型")
    @ExcelIgnore
    private Integer taskType;

    /**
     * 任务类型描述
     */
    @ApiModelProperty(value = "任务类型描述")
    @ExcelProperty(value = "任务类型描述")
    private String taskTypeDescription;

    /**
     * 请求码
     */
    @ApiModelProperty(value = "请求码")
    @ExcelProperty(value = "请求码")
    private String requestCode;


    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    @ExcelProperty(value = "任务号")
    private String taskCode;

    /**
     * 起点
     */
    @ApiModelProperty(value = "起点")
    @ExcelProperty(value = "起点")
    private String startPoint;

    /**
     * 终点
     */
    @ApiModelProperty(value = "终点")
    @ExcelProperty(value = "终点")
    private String endPoint;

    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    @ExcelProperty(value = "料框编号")
    private String frameCode;

    /**
     * 料框类型
     */
    @ApiModelProperty(value = "料框类型")
    @ExcelProperty(value = "料框类型")
    private String frameTypeCode;



    /**
     * 工单编号
     */
//    @ApiModelProperty(value = "工单编号")
//    @ExcelProperty(value = "工单编号")
//    private String workOrderCode;



    /**
     * AGV编号
     */
//    @ApiModelProperty(value = "AGV编号")
//    @ExcelProperty(value = "AGV编号")
//    private String agvCode;

    /**
     * 物料号
     */
//    @ApiModelProperty(value = "物料号")
//    @ExcelProperty(value = "物料号")
//    private String materialCode;

    /**
     * 物料类型
     */
//    @ApiModelProperty(value = "物料类型")
//    @ExcelProperty(value = "物料类型")
//    private String materialType;


    /**
     * 调度状态 0：调度中、1;调度完成、
     2：取消 3：失败 4:新建
     */
    @ApiModelProperty(value = "调度状态")
    @ExcelIgnore
    private Integer schedulingState;

    /**
     * 调度状态描述
     */
    @ApiModelProperty(value = "调度状态描述")
    @ExcelProperty(value = "调度状态描述")
    private String schedulingStateDescription;

    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间",converter = DateConverter.class)
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @ExcelProperty(value = "结束时间",converter = DateConverter.class)
    private Date endTime;


    /**
     * pn码
     */
    @ApiModelProperty(value = "pn码")
    @ExcelProperty(value = "pn码")
    private String pn;

    /**
     * 物料编号
     */
    @ApiModelProperty(value = "物料编号")
    @ExcelProperty(value = "物料编号")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @ExcelProperty(value = "物料名称")
    private String materialName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @ExcelProperty(value = "数量")
    private Long quality;

    /**
     * 组立物料
     */
    @ApiModelProperty(value = "组立物料")
    @ExcelProperty(value = "组立物料")
    private String materialParentCode;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    @ExcelProperty(value = "工单编号")
    private String workOrderCode;

    /**
     * 组立工单号
     */
    @ApiModelProperty(value = "组立工单号")
    @ExcelProperty(value = "组立工单号")
    private String parentWorkOrderCode;


    /**
     * 流向
     */
    @ApiModelProperty(value = "流向")
    @ExcelProperty(value = "流向")
    private String blockCode;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    @ExcelProperty(value = "船号")
    private String shipNo;

    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    @ExcelProperty(value = "重量")
    private String  weight;

}
