package com.hvisions.pms.dto;



import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Id;
import java.util.Date;

@Getter
@Setter
@ToString
public class HvPmCutPlanPassPointDTO extends PageInfo {
    /**
     * id
     */
    @ApiModelProperty(value = "id", notes = "id")
    private Long id;
    /**
     * 切割计划id
     */
    @ApiModelProperty(value = "切割计划id", notes = "切割计划id")
    private Long cutPlanId;
    /**
     * 切割计划编号
     */
    @ApiModelProperty(value = "切割计划编号", notes = "切割计划编号")
    private String cutPlanNo;
    /**
     * 工位编号
     */
    @ApiModelProperty(value = "工位编号", notes = "工位编号")
    private String stationCode;
    /**
     * 工位名称
     */
    @ApiModelProperty(value = "工位名称", notes = "工位名称")
    private String stationName;
    /**
     * 通过时间
     */
    @ApiModelProperty(value = "通过时间", notes = "通过时间")
    private Date passTime;
}









