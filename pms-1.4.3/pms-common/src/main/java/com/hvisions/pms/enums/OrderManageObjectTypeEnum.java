package com.hvisions.pms.enums;

/**
 * <p>Title: OrderManageObjectTypeEnum</p>
 * <p>Description: 对象类型枚举</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/12/6</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
public enum OrderManageObjectTypeEnum {
    //各种对象编码
    ORDER_MANAGE_DTO(2),
    ORDER_MANAGE_PARAMETER_DTO(3),
    ORDER_MANAGE_UPDATE_DTO(4),
    ORDER_OPERATION_DTO(5),
    ROUTE_STEP_AND_EQUIPMTEN_DTO(6),
    OPERATION_QUERY_DTO(7),
    OPERATION_MATERIAL_DTO(8),
    MATERIAL_DTO(9),
    BOM_DTO(10),
    BOM_ITEM_DTO(11),
    SUBSTITUTEITEM_DTO(12),
    DEMO_DTO(1),
    ;
    int code;

    OrderManageObjectTypeEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}

    
    
    
    
    
    
    
    
