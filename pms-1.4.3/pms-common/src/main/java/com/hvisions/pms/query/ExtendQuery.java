package com.hvisions.pms.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <p>Title: ExtendQuery</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2020/10/23</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Getter
@Setter
@ToString
public class ExtendQuery {
    /**
    *   and 或者 or
    */
    @ApiModelProperty(value = "and 或者 or",allowableValues = "1,2",notes = "1:and,2:or")
    private int andOr;
    /**
    *   判断符号
    */
    @ApiModelProperty(value = "判断符号",allowableValues = "1,2,3,4,5,6,7,8,9",
            notes = "1: =,2: >,3: >=,4: <,5: <=,6: is null,7: not null,8: like")
    private int symbol;
    /**
    *   扩展列名称
    */
    @ApiModelProperty(value = "扩展列名称")
    private String columnName;

    /**
    *   判断的值
    */
    @ApiModelProperty(value = "判断的值")
    private Object value;
}










