package com.hvisions.pms.plan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HvPmMaterialCutPlanDetail0DTO {
    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private Long cutPlanId;

    /**
     * 切割计划
     */
    @ApiModelProperty(value = "切割计划")
    private String cutPlanCode;

    /**
     * 余料(0:是,1:否)
     */
    @ApiModelProperty(value = "余料(0:是,1:否)")
    private Integer surplusMaterialFlag;

    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer quality;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workOrder;

    /**
     * 零件图
     */
    @ApiModelProperty(value = "零件图")
    private String materialPicture;

    /**
     * pn
     */
    @ApiModelProperty(value = "pN")
    private String pn;

    /**
     * 坐标X
     */
    @JsonIgnore
    @ApiModelProperty(value = "坐标X")
    private String coordinateX;

    /**
     * 坐标Y
     */
    @JsonIgnore
    @ApiModelProperty(value = "坐标Y")
    private String coordinateY;

    /**
     * 顺序
     */
    @JsonIgnore
    @ApiModelProperty(value = "顺序")
    private Integer sequence;

    @ApiModelProperty(value = "零件长度（mm）")
    private String length;

    @ApiModelProperty(value = "零件宽度（mm）")
    private String width;

    @ApiModelProperty(value = "零件厚度（mm）")
    private String thick;

    @ApiModelProperty(value = "零件序号")
    private String no;

    // 套料数量（个）
    @ApiModelProperty(value = "套料数量（个）")
    private Integer nestingCount;

    // 订单数量（个）
    @ApiModelProperty(value = "订单数量（个）")
    private Integer orderCount;

    // 零件净重量（KG）
    @ApiModelProperty(value = "零件净重量（KG）")
    private String partNetWeight; // 单位：千克

    // 零件矩形重量（KG）
    @ApiModelProperty(value = "零件矩形重量（KG）")
    private String partRectWeight; // 单位：千克

    // 零件净面积（平方米）
    @ApiModelProperty(value = "零件净面积（平方米）")
    private String partNetArea; // 单位：平方米

    // 零件矩形面积（平方米）
    @ApiModelProperty(value = "零件矩形面积（平方米）")
    private String partRectArea; // 单位：平方米

    // 单个零件切割长度（米）
    @ApiModelProperty(value = "单个零件切割长度（米）")
    private String singlePartCutLength; // 单位：米

    // 单个零件打标长度（米）
    @ApiModelProperty(value = " 单个零件打标长度（米）")
    private String singlePartMarkingLength; // 单位：米

    // 单个零件刺穿孔数（个）
    @ApiModelProperty(value = "单个零件刺穿孔数（个）")
    private Integer singlePartPierceCount;

    // 流向代码
    @ApiModelProperty(value = "流向代码")
    private String blockCode;
}
