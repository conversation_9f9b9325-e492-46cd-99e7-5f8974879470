package com.hvisions.pms.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hvisions.pms.ExcelConverter.DateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/10/14
 */
@Data
@ApiModel("调度记录及其物料信息")
public class HvPmAgvTaskRecordAndDetailDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private long id;

    @ApiModelProperty(value = "请求系统")
    @ExcelProperty(value = "请求系统")
    private String requestSystem;//请求系统

    @ApiModelProperty(value = "请求用户编号")
    @ExcelProperty(value = "请求用户编号")
    private String requestUser;//请求系统

    @ApiModelProperty(value = "请求时间")
    @ExcelProperty(value = "请求时间")
    private Date requestTime;//请求系统

    /**
     * 请求码
     */
    @ExcelProperty(value = "请求码")
    private String requestCode;


    /**
     * 任务号
     */
    @ExcelProperty(value = "任务号")
    private String taskCode;


    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    @ExcelProperty(value = "料框编号")
    private String frameCode;

    /**
     * 料框类型
     */
    @ExcelProperty(value = "料框类型")
    private String frameTypeCode;

    /**
     * 起点
     */
    @ExcelProperty(value = "起点")
    private String startPoint;
    /**
     * 终点
     */
    @ExcelProperty(value = "终点")
    private String endPoint;

    /**
     * 工单编号
     */
    @ExcelProperty(value = "工单编号")
    private String workOrderCode;

    /**
     * 任务类型 字典：task_type：10 空框请求、20：满框调度、30：空框回库、40：生产叫料、50：空框流转
     */
    @ExcelProperty(value = "任务类型")
    @ExcelIgnore
    private Integer taskType;

    @ApiModelProperty(value = "任务类型描述")
    @ExcelProperty(value = "物料类型")
    private String taskTypeDescription;

    /**
     * AGV编号
     */
    @ExcelProperty(value = "AGV编号")
    private String agvCode;
    /**
     * 物料号
     */
    @ExcelProperty(value = "物料号")
    private String materialCode;
    /**
     * 物料类型
     */
    @ExcelProperty(value = "物料类型")
    private String materialType;


    /**
     * 调度状态 0：调度中、1;调度完成、
     2：取消 3：失败 4:新建
     */
    @ExcelIgnore
    private Integer schedulingState;

    @ApiModelProperty(value = "调度状态描述")
    @ExcelProperty(value = "调度状态")
    private String schedulingStateDescription;
    /**
     * 开始时间
     */
    @ExcelProperty(value = "开始时间",converter = DateConverter.class)
    private Date startTime;
    /**
     * 结束时间
     */
    @ExcelProperty(value = "结束时间",converter = DateConverter.class)
    private Date endTime;
    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private String createId;
    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间",converter = DateConverter.class)
    private Date createTime;
    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private String updaterId;
    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间",converter = DateConverter.class)
    private Date updateTime;

    /**
     * 查询时间(开始时间)
     */
    @ExcelIgnore
    private String startTimeUTC;

    /**
     * 查询时间(结束时间)
     */
    @ExcelIgnore
    private String endTimeUTC;

    /**
     * 物料信息列表
     */
    private List<HvPmAgvTaskRecordMaterialDTO> taskRecordMaterials;
}
