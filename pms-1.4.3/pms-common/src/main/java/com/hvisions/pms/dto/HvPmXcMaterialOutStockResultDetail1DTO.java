package com.hvisions.pms.dto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
@Data
public class HvPmXcMaterialOutStockResultDetail1DTO {
    /**
     * id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    private Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private Long resultId;

    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    private String taskNo;

    /**
     * 仓库编号
     */
    @ApiModelProperty(value = "仓库编号")
    private String warehouseCode;

    /**
     * 库位编号
     */
    @ApiModelProperty(value = "库位编号")
    private String locationCode;

    /**
     * 物料编号
     */
    @ApiModelProperty(value = "物料编号")
    private String materialCode;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchCode;

    /**
     * 出库型材数量
     */
    @ApiModelProperty(value = "出库型材数量")
    private Integer quality;

    /**
     * 托盘型材总数
     */
    @ApiModelProperty(value = "托盘型材总数")
    private Integer totalQuality;

    /**
     * 托盘号
     */
    @ApiModelProperty(value = "托盘号")
    private String palletCode;

    /**
     * 出库时间
     */
    @ApiModelProperty(value = "出库时间")
    private String outTime;

    /**
     * 出库人编号
     */
    @ApiModelProperty(value = "出库人编号")
    private String operationUserCode;

}
