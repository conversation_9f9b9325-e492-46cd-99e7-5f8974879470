package com.hvisions.pms.plan;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class HvPmMaterialCutPlanDetail1DTO {
    /**
     * 主键;
     */
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    private Long cutPlanId;

    /**
     * 切割计划
     */
    @ApiModelProperty(value = "切割计划")
    private String cutPlanCode;

    /**
     * 余料号
     */
    @ApiModelProperty(value = "余料号")
    private String materialCode;

    /**
     * 长度
     */
    @ApiModelProperty(value = "长度")
    private String length;

    /**
     * 宽度
     */
    @ApiModelProperty(value = "宽度")
    private String width;

    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    private String thickness;

    /**
     * 重量
     */
    @ApiModelProperty(value = "重量")
    private String weight;
}
