package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>Title: OperationCollectParamDto</p >
 * <p>Description: 工单详情--设备参数</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/9</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
@ApiModel("工单详情--设备参数")
public class OperationParamDto {

    @ApiModelProperty(value = "刷新时间", readOnly = true)
    private LocalDateTime refreshTime;

    @ApiModelProperty(value = "工位下设备id", readOnly = true)
    List<WorkCenterEquipmentDto> workCenterEquipmentDtos;

    @ApiModelProperty(value = "设备参数")
    List<EquipParamDto> equipParamDtos;

    @ApiModelProperty(value = "历史记录", readOnly = true)
    List<RouteParamDataDto> routeParamDataDtos;

    public LocalDateTime getRefreshTime() {
        return LocalDateTime.now();
    }
}
