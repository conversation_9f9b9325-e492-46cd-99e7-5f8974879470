package com.hvisions.pms.dto.materialRequirement;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: MaterialRequirementQueryDTO</p>
 * <p>Description: 物料需求查询</p>
 */
@Data
@ApiModel("物料需求查询")
public class MaterialRequirementQueryDTO extends PageInfo {

    /**
     * 物料需求编号 (PK)
     */
    @ApiModelProperty(value = "物料需求编号")
    private String requirementCode;

    /**
     * 工单编码
     */
    @ApiModelProperty(value = "工单编码")
    private String workOrderCode;

    /**
     * 目标产线编号
     */
    @ApiModelProperty(value = "目标产线")
    private String cellId;

    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workPlanStartTime;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;

    /**
     * 是否已集配
     */
    @ApiModelProperty(value = "是否已集配")
    private Integer isAssembled;

    /**
     * 是否已关闭
     */
    @ApiModelProperty(value = "是否已关闭")
    private Integer isClose;


}