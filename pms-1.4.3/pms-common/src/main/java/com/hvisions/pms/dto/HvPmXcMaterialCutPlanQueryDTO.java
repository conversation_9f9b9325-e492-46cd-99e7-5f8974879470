package com.hvisions.pms.dto;


import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class HvPmXcMaterialCutPlanQueryDTO extends PageInfo {

    @ApiModelProperty(value ="任务号")
    private String taskCode;

    @ApiModelProperty(value ="产线号")
    private String lineCode;

    @ApiModelProperty(value = "工单号")
    private String workOrder;

    @ApiModelProperty(value ="开始时间")
    private Date beginTime;

    @ApiModelProperty(value ="结束时间")
    private Date endTime;

}
