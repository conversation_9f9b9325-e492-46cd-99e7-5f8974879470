package com.hvisions.pms.enums;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

public enum XcCutPlanStatusEnum {

    //0:未下发，1：已下发 2：下发失败 3：备料中、4：备料完成、5：开始切割、
    // 6：完成
    UN_SEND(0, "未下发", null, null),
    SEND_OK(1, "已下发", null, null),
    SEND_ERROR(2, "下发失败", null, null),
    PREPARATION_ING(3, "备料中", null, null),
    PREPARATION_FINISH(4, "备料完成",null , null),
    START_CUT(5, "开始切割", null, null),
    FINISH(6, "完成", null, null);

    static Map<Integer, XcCutPlanStatusEnum> enumMap = new HashMap<>();

    static {
        for (XcCutPlanStatusEnum value : XcCutPlanStatusEnum.values()) {
            if (value.code != null)
                enumMap.put(value.code, value);
        }
    }

    public static XcCutPlanStatusEnum getEnumByCode(@NotNull Integer code) {
        return enumMap.get(code);
    }


    private final Integer code;
    private final String name;

    private final Integer hgCode;

    private final String defaultStation;

    XcCutPlanStatusEnum(Integer code, String name, Integer hgCode, String defaultStation) {
        this.code = code;
        this.name = name;
        this.hgCode = hgCode;
        this.defaultStation = defaultStation;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getHgCode() {
        return this.hgCode;
    }

    public String getDefaultStation() {
        return this.defaultStation;
    }
}
