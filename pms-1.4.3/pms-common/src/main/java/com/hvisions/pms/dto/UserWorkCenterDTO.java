package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: UserEquipmentDTO</p >
 * <p>Description: 人员设备绑定DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class UserWorkCenterDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID列表")
    private List<Integer> userId;

    /**
     * 设备Id
     */
    @ApiModelProperty(value = "设备Id")
    private Integer workCenterId;


}
