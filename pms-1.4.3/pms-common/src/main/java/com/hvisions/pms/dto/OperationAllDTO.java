package com.hvisions.pms.dto;

import com.hvisions.pms.task.dto.OrderTaskDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: OperationAllDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019-09-20</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OperationAllDTO extends OrderTaskDTO {
    /**
     * 工序投入料列表
     */
    @ApiModelProperty(value = "工序投入料列表")
    private List<OperationMaterialDTO> operationMaterialDTOS;
    /**
     * 工序产出料列表
     */
    @ApiModelProperty(value = "工序产出料列表")
    private List<OperationOutPutMaterialDTO> operationOutPutMaterialDTOS;
    /**
     * 工序参数列表
     */
    @ApiModelProperty(value = "工序参数列表")
    private List<OperationParameterDTO> operationParameterDTOS;

    /**
     * 工序工作指南
     */
    @ApiModelProperty(value = "工序工作指南")
    private List<OperationFileDTO> fileDTOS;
}