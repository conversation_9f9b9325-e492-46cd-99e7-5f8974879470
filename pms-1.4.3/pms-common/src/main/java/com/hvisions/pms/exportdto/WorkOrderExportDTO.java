package com.hvisions.pms.exportdto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.common.interfaces.IExtendObject;
import com.hvisions.common.interfaces.IObjectType;
import com.hvisions.pms.SysBaseDTO;
import com.hvisions.pms.dto.TypeExtendRelationDTO;
import com.hvisions.pms.enums.OrderManageObjectTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-04-09 16:07
 */
@Data
public class WorkOrderExportDTO extends SysBaseDTO implements IObjectType, IExtendObject, Serializable {


    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id", required = true)
    private Integer orderId;


    /**
     * 工单编码
     */
    @ApiModelProperty(value = "工单编码", required = true)
    private String workOrderCode;

    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态(0-新建，1-已下发，2-撤销，3-运行，4-暂停，5-结束，6-终止，7-报废，8-报工)", readOnly = true)
    private Integer workOrderState;

    /**
     * 来源(0:MES ,1:自建)
     */
    @ApiModelProperty(value = "来源")
    private Integer comeFrom;


    /**
     * 齐套校验状态(0:NG ,1:OK)
     */
    @ApiModelProperty(value = "齐套校验状态(0:NG ,1:OK)")
    private Integer completeSetCheckStatus;


    /**
     * 车间名称
     */
//    @ApiModelProperty(value = "车间名称")
//    private String areaName;



    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称", readOnly = true)
    private String materialName;


    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipNo;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipModel;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

    /**
     * 是否外发 0 否 1 是  （零件工单必填）
     */
    @ApiModelProperty(value = "是否外发:0 否 1 是  ")
    private String outFlag;

    /**
     * 自制/外协  2 自制 1外协（零件工单必填）
     */
    @ApiModelProperty(value = "自制/外协 2 自制 1外协  ")
    private String specialPurchaseTypeCode;

    /**
     * 流向代码
     */
    @ApiModelProperty(value = "流向代码")
    private String blockCode;

    /**
     * 执行顺序
     */
    @ApiModelProperty(value = "执行顺序")
    private Integer executeSequence;


    /**
     * bomCode
     */
//    @ApiModelProperty(value = "BOM编码", readOnly = true)
//    private String bomCode;

    /**
     * bomName
     */
//    @ApiModelProperty(value = "BOM名称", readOnly = true)
//    private String bomName;

    /**
     * bom版本
     */
//    @ApiModelProperty(value = "bom版本", readOnly = true)
//    private String bomVersion;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 工艺路线ID
     */
    @ApiModelProperty(value = "工艺路线ID", required = true)
    private Integer routeId;

    /**
     * 工艺路线编码
     */
    @ApiModelProperty(value = "工艺路线编码", readOnly = true)
    private String routeCode;

    /**
     * 工艺路线名称
     */
    @ApiModelProperty(value = "工艺路线名称", readOnly = true)
    private String routeName;

    /**
     * 工艺路线版本
     */
    @ApiModelProperty(value = "工艺路线版本", readOnly = true)
    private String routeVersion;

    /**
     * 计划开始时间
     */

    @ApiModelProperty(value = "计划开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planStartTime;

    /**
     * 计划结束时间
     */

    @ApiModelProperty(value = "计划结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planEndTime;


    /**
     * 生产计划 计划开始时间
     */
    @ApiModelProperty(value = "生产计划计划开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workPlanStartTime;

    /**
     * 生产计划 计划结束时间
     */
    @ApiModelProperty(value = "生产计划计划结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date workPlanEndTime;





    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号", required = true)
    private String planCode;


    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码", readOnly = true)
    private String materialCode;


    /**
     * 工单类型名称
     */
    @ApiModelProperty(value = "工单类型名称")
    private String orderTypeName;


    /**
     * 班次 名称
     */
    @ApiModelProperty(value = "班次 名称", readOnly = true)
    private String shiftName;


    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称", readOnly = true)
    private String crewName;


    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间", readOnly = true)
    private Date actualStartTime;

    /**
     * 实际结束时间
     */
    @ApiModelProperty(value = "实际结束时间", readOnly = true)
    private Date actualEndTime;


    /**
     * 工单下发时间
     */
    @ApiModelProperty(value = "工单下发时间", readOnly = true)
    private Date issuedTime;

    /**
     * 用于区分计划下发工单还是手工创建工单 计划 1 手动 0
     */
    @ApiModelProperty(value = "用于区分计划下发工单还是手工创建工单", readOnly = true)
    private Integer planOrNew;

    /**
     * 工单下发方式
     */
    @ApiModelProperty(value = "工单下发方式 1 下发全部工序，2 逐步下发工序", readOnly = true)
    private Integer orderMode;


    /**
     * 当前工序名
     */
    @ApiModelProperty(value = "当前工序名")
    private String orderOperationName;

    /**
     * 工单分类 0：组立工单, 1:板材工单,2:型材工单
     */
    @ApiModelProperty(value = "工单分类 0：组立工单, 1:板材工单,2:型材工单")
    private Integer usedType;

    /**
     * 父物料编码
     */
    @ApiModelProperty(value = "父物料编码")
    private String parentMaterialCode;

    /**
     * 父工单
     */
    @ApiModelProperty(value = "父工单")
    private String parentWorkOrderCode;

    /**
     * 齐套时间
     */
    @ApiModelProperty(value = "齐套时间")
    private Date completeTime;



    /**
     * 工单类型扩展属性
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(name = "工单类型扩展属性", value = "扩展属性")
    private Map<String, Object> typeExtend = new HashMap<>();

    /**
     * 工单类型属性扩展字段名称
     */
    private List<TypeExtendRelationDTO> relationDTOS;

    @Override
    public Integer getObjectType() {
        return OrderManageObjectTypeEnum.ORDER_MANAGE_DTO.getCode();
    }


    /**
     * 扩展属性
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(name = "扩展属性", value = "扩展属性")
    private Map<String, Object> extend = new HashMap<>();

}
