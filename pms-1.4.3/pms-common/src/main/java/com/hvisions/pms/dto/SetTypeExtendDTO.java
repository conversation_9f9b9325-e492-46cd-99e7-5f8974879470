package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: SetTypeExtend</p >
 * <p>Description: 设置工单类型扩展字段</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/28</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class SetTypeExtendDTO {


    /**
     * 工单类型ID
     */
    @ApiModelProperty(value = "工单类型ID")
    private Integer workOrderTypeId;

    /**
     * 扩展属性字段名称
     */
    @ApiModelProperty(value = "扩展属性字段编码列表")
    private List<TypeExtendDTO> extendDTOS;
}