package com.hvisions.pms.viewdto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: MaterialByCrewDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@ApiModel(value = "班组产出物料报表DTO")
public class MaterialByCrewDTO {


    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Integer materialId;


    /**
     * 工单实际开始时间
     */
    @ApiModelProperty(value = "工单实际开始时间")
    private Date actualStartTime;
    /**
     * 工单实际结束时间
     */
    @ApiModelProperty(value = "工单实际结束时间")
    private Date actualEndTime;

    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称")
    private String crewName;


    /**
     * 工单编码
     */
    @ApiModelProperty(value = "工单编码")
    private String workOrderCode;


    /**
     * 工序编码
     */
    @ApiModelProperty(value = "工序编码")
    private String operationCode;

    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private Integer operationOid;

    /**
     * 工序开始时间
     */
    @ApiModelProperty(value = "工序开始时间")
    private Date startTime;

    /**
     * 工序结束时间
     */
    @ApiModelProperty(value = "工序结束时间")
    private Date endTime;


    /**
     * 产出数量
     */
    @ApiModelProperty(value = "产出数量")
    private BigDecimal outPutCount;


    /**
     * 产线ID
     */
    @ApiModelProperty(value = "产线ID")
    private Integer cellId;

    /**
     * 工序所用工时
     */
    @ApiModelProperty(value = "工序所用工时")
    private Integer workTime = 0;

}
