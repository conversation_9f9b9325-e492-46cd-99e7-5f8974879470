package com.hvisions.pms.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2025/1/8
 */
@Data
@ApiModel(description = "零件情况查询条件")
public class HvPmPartSituationQueryDTO extends PageInfo {


    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipNo;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipModel;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;


    /**
     * 所需数量
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

}
