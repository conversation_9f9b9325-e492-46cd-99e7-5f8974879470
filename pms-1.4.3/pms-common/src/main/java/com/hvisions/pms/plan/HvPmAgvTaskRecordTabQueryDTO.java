package com.hvisions.pms.plan;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>    <P>
 *
 * <AUTHOR>
 * @date 2024/12/25
 */
@Data
@ApiModel(description = "物流调度查询条件")
public class HvPmAgvTaskRecordTabQueryDTO extends PageInfo {


    /**
     * 任务号
     */
    @ExcelProperty(value = "任务号")
    private String taskCode;

    /**
     * 请求码
     */
    @ExcelProperty(value = "请求码")
    private String requestCode;

    /**
     * 起点
     */
    @ExcelProperty(value = "起点")
    private String startPoint;

    /**
     * 终点
     */
    @ExcelProperty(value = "终点")
    private String endPoint;

    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    @ExcelProperty(value = "料框编号")
    private String frameCode;

    /**
     * 任务类型 字典：task_type：10 空框请求、20：满框调度、30：空框回库、40：生产叫料、50：空框流转
     */
    @ExcelProperty(value = "任务类型")
    @ExcelIgnore
    private Integer taskType;


    /**
     * 调度状态 0：调度中、1;调度完成、
     2：取消 3：失败 4:新建
     */
    @ExcelIgnore
    private Integer schedulingState;

    /**
     * 查询时间(开始时间)
     */
    @ExcelIgnore
    private String startTimeUTC;

    /**
     * 查询时间(结束时间)
     */
    @ExcelIgnore
    private String endTimeUTC;

    /**
     * 物料号
     */
    @ExcelProperty(value = "物料号")
    private String materialCode;


    //    @ApiModelProperty(value = "请求系统")
//    @ExcelProperty(value = "请求系统")
//    private String requestSystem;//请求系统

//    @ApiModelProperty(value = "请求用户编号")
//    @ExcelProperty(value = "请求用户编号")
//    private String requestUser;//请求系统


//    /**
//     * 工单编号
//     */
//    @ExcelProperty(value = "工单编号")
//    private String workOrderCode;

//    @ApiModelProperty(value = "任务类型描述")
//    @ExcelProperty(value = "物料类型")
//    private String taskTypeDescription;

//    /**
//     * AGV编号
//     */
//    @ExcelProperty(value = "AGV编号")
//    private String agvCode;


//    @ApiModelProperty(value = "调度状态描述")
//    @ExcelProperty(value = "调度状态")
//    private String schedulingStateDescription;


}


