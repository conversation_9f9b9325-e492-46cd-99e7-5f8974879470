package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <P>生产调度信息和物料信息 <P>
 *
 * <AUTHOR>
 * @date 2024/7/24
 */
@Data
public class HvPmAgvTaskRecordAndMaterialDTO {

    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    private String taskCode;

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    private Integer taskType;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer quality;
}
