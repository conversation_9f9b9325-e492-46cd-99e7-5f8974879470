package com.hvisions.pms.dto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>Title: HvRouteParamDataDetail</p >
 * <p>Description: 设备参数详情</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/15</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RouteParamDataDetailDto extends SysBaseDTO {

    @ApiModelProperty(value = "参数记录头表id")
    private Integer paramDataId;


    @ApiModelProperty(value = "参数编码")
    private String parameterCode;


    @ApiModelProperty(value = "参数名称")
    private String parameterName;


    @ApiModelProperty(value = "标识")
    private String iotTag;


    @ApiModelProperty(value = "单位id")
    private String unit;

    @ApiModelProperty(value = "单位名称")
    private String unitName;


    @ApiModelProperty(value = "标准上限")
    private String maxValue;


    @ApiModelProperty(value = "标准下限")
    private String minValue;


    @ApiModelProperty(value = "当前值")
    private String currentValue;

    @ApiModelProperty(value = "标准值")
    private String defaultValue;
}
