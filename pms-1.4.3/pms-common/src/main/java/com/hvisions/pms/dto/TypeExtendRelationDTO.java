package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: TypeExtendRelationDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2021/9/29</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class TypeExtendRelationDTO {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 工单类型ID
     */
    @ApiModelProperty(value = "工单类型ID")
    private Integer workOrderTypeId;

    /**
     * 扩展属性字段名称
     */
    @ApiModelProperty(value = "扩展属性字段名称")
    private String extendCode;

    /**
     * 中文显示名
     */
    @ApiModelProperty(value = "中文显示名")
    private String chName;

    /**
     * 英文显示名
     */
    @ApiModelProperty(value = "英文显示名")
    private String enName;
}