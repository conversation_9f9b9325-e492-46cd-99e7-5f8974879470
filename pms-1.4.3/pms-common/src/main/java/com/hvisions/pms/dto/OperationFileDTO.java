package com.hvisions.pms.dto;

import com.hvisions.framework.dto.file.FileDTO;
import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: OperationFileDTO</p >
 * <p>Description: 工序文件关系DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/21</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@ApiModel(value = "工序文件关系")
@Data
public class OperationFileDTO extends SysBaseDTO {

    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private Integer operationId;
    /**
     * 文件ID
     */
    @ApiModelProperty(value = "文件ID")
    private Integer fileId;

    /**
     * 文件数据列表
     */
    @ApiModelProperty(value = "文件数据列表")
    private FileDTO fileDTOS;

}
