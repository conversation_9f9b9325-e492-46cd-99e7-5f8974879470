package com.hvisions.pms.dto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
@Data
public class HvPmXcMaterialOutStockResultDTO {
    /**
     * id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    private Long id;

    /**
     * 任务号
     */
    @NotNull(message = "任务号不能为空")
    @ApiModelProperty(value = "任务号")
    private String taskNo;

    /**
     * 出库时间
     */
    @ApiModelProperty(value = "出库时间")
    private Date outTime;

    /**
     * 出库人编号
     */
    @ApiModelProperty(value = "出库人编号")
    private String operationUserCode;

    /**
     * 创建时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建时间", notes = "此字段不必传递", readOnly = true)
    private Date createTime = new Date();

    /**
     * detail1列表
     */
    @ApiModelProperty(value = "detail1列表")
    private List<HvPmXcMaterialOutStockResultDetail1DTO> detail1List;
}
