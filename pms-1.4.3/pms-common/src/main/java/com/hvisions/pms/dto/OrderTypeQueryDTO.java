package com.hvisions.pms.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: OrderTypeQueryDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/11/6</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OrderTypeQueryDTO extends PageInfo {


    /**
     * 工单类型编码
     */
    @ApiModelProperty(value = "工单类型编码")
    private String orderTypeCode;

    /**
     * 工单类型名称
     */
    @ApiModelProperty(value = "工单类型名称")
    private String orderTypeName;
}