package com.hvisions.pms.dto;

/**
 * <p>Title: HvPmMaterialTaoPicDTO</p>
 * <p>Description:  </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2024年4月19日</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.*;
import java.util.Date;

@Getter
@Setter
@ToString
public class HvPmMaterialTaoPicDTO  {
    /**
     * id
     */
    @ApiModelProperty(value = "id", notes = "id")
    private Long id;
    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号", notes = "任务号")
    private String taskCode;
    /**
     * 产线号
     */
    @ApiModelProperty(value = "产线号", notes = "产线号")
    private String lineCode;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", notes = "工单号")
    private String workOrder;
    /**
     * 船型
     */
    @ApiModelProperty(value = "船型", notes = "船型")
    private String model;
    /**
     * 分段
     */
    @ApiModelProperty(value = "分段", notes = "分段" )
    private String segmentationCode;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间", notes = "计划结束时间")
    private Date planEndTime;
    /**
     * 下发操作人ID
     */
    @ApiModelProperty(value = "下发操作人ID", notes = "下发操作人ID")
    private Integer sendUserId;
    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间", notes = "下发时间")
    private Date sendTime;
    /**
     * 下发操作人
     */
    @ApiModelProperty(value = "下发操作人", notes = "下发操作人")
    private String userName;
}









