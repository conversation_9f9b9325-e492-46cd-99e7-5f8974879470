package com.hvisions.pms.viewdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: MaterialConsumeDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/18</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class MaterialConsumeDTO {

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Integer materialId;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 物料特征值
     */
    @ApiModelProperty(value = "物料特征值")
    private String eigenvalue;
    /**
     * 实际消耗物料数量
     */
    @ApiModelProperty(value = "实际消耗物料数量")
    private BigDecimal actualCount;
    /**
     * 计划消耗数量
     */
    @ApiModelProperty(value = "计划消耗数量")
    private BigDecimal planCount;
    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private Integer operationId;

    /**
     * 工序编码
     */
    @ApiModelProperty(value = "工序编码")
    private String operationCode;

    /**
     * 工单编码
     */
    @ApiModelProperty(value = "工单编码")
    private String workOrderCode;

    /**
     * 工单计划开始时间
     */
    @ApiModelProperty(value = "工单计划开始时间")
    private Date planStartTime;

    /**
     * 工单计划结束时间
     */
    @ApiModelProperty(value = "工单计划结束时间")
    private Date planEndTime;


    /**
     * 工单实际开始时间
     */
    @ApiModelProperty(value = "工单实际开始时间")
    private Date actualStartTime;
    /**
     * 工单实际结束时间
     */
    @ApiModelProperty(value = "工单实际结束时间")
    private Date actualEndTime;


    /**
     * 产线ID
     */
    @ApiModelProperty(value = "产线ID")
    private Integer cellId;


    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称")
    private String crewName;

}
