package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: RouteStepAndEquipmentDTO</p>
 * <p>Description: 工艺步骤和设备关系表</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/1/18</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
public class RouteStepAndEquipmentDTO {
    /**
    *   工艺步骤id
    */
    @ApiModelProperty(value = "工艺步骤id")
    private Integer routeStepId;
    /**
    *   设备id
    */
    @ApiModelProperty(value = "设备id")
    private Integer equipmentId;
}









