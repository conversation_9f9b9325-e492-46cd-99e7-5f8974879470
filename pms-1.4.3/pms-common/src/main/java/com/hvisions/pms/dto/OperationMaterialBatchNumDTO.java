package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: OperationMaterialBatchNumDTO</p >
 * <p>Description: 录入批次号DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/7</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class OperationMaterialBatchNumDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    protected Integer id;


    /**
     * 生产任务Id
     */
    @ApiModelProperty(value = "生产任务ID")
    private Integer taskId;

    /**
     * 实际投料量
     */
    @ApiModelProperty(value = "实际投料量")
    @DecimalMin(value = "0", message = "投料量不能小于0")
    private BigDecimal actualCount;


    /**
     * 物料批次号
     */
    @ApiModelProperty(value = "物料批次号")
    private String batchNumber;

    /**
     * 回料数量
     */
    @ApiModelProperty(value = "回料数量")
    private BigDecimal returnMaterial;

    /**
     * 回料时间
     */
    private Date returnMaterialTime;


}
