package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: RecordParamDto</p >
 * <p>Description: 记录设备参数</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/16</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
@Builder
public class RecordParamDto {
    @ApiModelProperty("工艺操作id")
    private Integer operationId;

    @ApiModelProperty("位置id")
    private Integer positionId;

    @ApiModelProperty("位置类型")
    private Integer positionType;

    @ApiModelProperty(value = "设备参数")
    List<EquipParamDto> equipParamDtos;

}
