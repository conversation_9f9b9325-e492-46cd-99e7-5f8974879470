package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>Title: HvRouteCollectData</p >
 * <p>Description: 设备采集数据</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/15</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
public class RouteParamDataDto {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "记录时间")
    private LocalDateTime recordTime;

    @ApiModelProperty(value = "记录时状态")
    private Integer status;

    @ApiModelProperty(value = "记录类型: 1: 设备采集数据 2: 设备下发数据")
    private Integer recordType;


    @ApiModelProperty(value = "工艺操作id")
    private Integer operationId;
}
