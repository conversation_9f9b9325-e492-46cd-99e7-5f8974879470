package com.hvisions.pms.plan;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "型材切割计划查询条件")
public class HvPmXcMaterialCutPlanTabQueryDTO extends PageInfo {

    /**
     * 作业号
     */
    @ApiModelProperty(value = "作业号")
    private String orderNo;

    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号")
    private String materialCode;

    /**
     * 物料类型
     */
//    @ApiModelProperty(value = "物料类型")
//    private String materialType;


    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String workOrderCode;

    /**
     * 产线ID
     */
    @ApiModelProperty(value = "产线ID")
    private Integer lineId;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipMode;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipCode;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;


    /**
     * 下发开始时间
     */
    @ApiModelProperty(value = "下发开始时间")
    private Date sendBeginTime;

    /**
     * 下发结束时间
     */
    @ApiModelProperty(value = "下发结束时间")
    private Date sendEndTime;

    /**
     * 完成开始时间
     */
    @ApiModelProperty(value = "完成开始时间")
    private Date completionStartTime;

    /**
     * 完成结束时间
     */
    @ApiModelProperty(value = "完成结束时间")
    private Date completionEndTime;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

}
