package com.hvisions.pms.viewdto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: MaterialUsedQueryDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/27</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class MaterialUsedQueryDTO {

    /**
     * 工单实际开始时间
     */
    @ApiModelProperty(value = "工单实际开始时间")
    private Date actualStartTime;
    /**
     * 工单实际结束时间
     */
    @ApiModelProperty(value = "工单实际结束时间")
    private Date actualEndTime;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;
}
