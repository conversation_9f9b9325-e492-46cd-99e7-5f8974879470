package com.hvisions.pms.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: WorkOrderQueryDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/2/13</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class WorkOrderQueryDTO extends PageInfo {
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;

    /**
     * 工单编码
     */
    @ApiModelProperty(value = "工单编码")
    private String workOrderCode = "";

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode = "";


    /**
     * 计划编码
     */
    @ApiModelProperty(value = "计划编码")
    private String planCode;

    /**
     * 工艺名称
     */
    @ApiModelProperty(value = "工艺名称")
    private String routeName;
    /**
     * 用于区分计划下发工单还是手工创建工单 计划 1 手动 0
     */
    @ApiModelProperty(value = "用于区分计划下发工单还是手工创建工单")
    private Integer planOrNew;
    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态(0-新建，1-已下发，2-撤销，3-运行，4-暂停，5-结束，6-终止，7-报废，8-报工)")
    private Integer workOrderState;


}
