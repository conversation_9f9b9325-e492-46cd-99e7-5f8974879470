package com.hvisions.pms.dto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: OrderOperationDTO</p>
 * <p>Description: 工单步骤DTO</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/1/17</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderOperationDTO extends SysBaseDTO {
    /**
     * 工单id
     */
    @ApiModelProperty(value = "工单id")
    private Integer orderId;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String workOrderCode;

    /**
     * 数量
     */
    @ApiModelProperty(value = "工单数量")
    private BigDecimal workOrderQuantity;
    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号,")
    private String serialNum;

    /**
     * 工序编码
     */
    @ApiModelProperty(value = "工序编码")
    private String operationCode;
    /**
     * 设备id
     */
    @ApiModelProperty(value = "设备id")
    private Integer workCenterId;

    /**
     * 车间id
     */
    @ApiModelProperty(value = "车间id")
    private Integer areaId;
    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id")
    private Integer cellId;
    /**
     * 班组id
     */
    @ApiModelProperty(value = "班组id")
    private Integer crewId;
    /**
     * 班次id
     */
    @ApiModelProperty(value = "班次id")
    private Integer shiftId;
    /**
     * 工艺步骤id
     */
    @ApiModelProperty(value = "工艺步骤id")
    private Integer routeStepId;

    /**
     * 工艺节点编码
     */
    @ApiModelProperty(value = "工艺节点编码")
    private String nodeCode;
    /**
     * 工单步骤顺序号
     */
    @ApiModelProperty(value = "工单步骤顺序号，10为第一步骤")
    private Integer operationOrder;
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String workCenterName;
    /**
     * 车间名称
     */
    @ApiModelProperty(value = "车间名称")
    private String areaName;


    /**
     * 产线名称
     */
    @ApiModelProperty(value = "产线名称")
    private String cellName;
    /**
     * 班次名称
     */
    @ApiModelProperty(value = "班次名称")
    private String shiftName;
    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称")
    private String crewName;

    /**
     * 工艺路线版本
     */
    @ApiModelProperty(value = "工艺路线版本")
    private String routeVersion;


    /**
     * 工艺路线名称
     */
    @ApiModelProperty(value = "工艺路线名称")
    private String routeName;

    /**
     * 工单物料编码
     */
    @ApiModelProperty(value = "工单物料编码")
    private String materialCode;


    /**
     * 工单物料编码
     */
    @ApiModelProperty(value = "工单物料名称")
    private String materialName;

    /**
     * 物料特征值
     */
    @ApiModelProperty(value = "物料特征值")
    private String eigenvalue;

    /**
     * 父级工序ID
     */
    @ApiModelProperty(value = "父级工序ID")
    private Integer parentOrderId;


    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 模版url
     */
    @ApiModelProperty(value = "url")
    private String templateUrl;


    /**
     * weburl
     */
    @ApiModelProperty(value = "web模版url")
    private String webTemplateUrl;

    /**
     * 生产计划 计划开始时间
     */
    @ApiModelProperty(value = "生产计划计划开始时间", required = true)
    private Date workPlanStartTime;
    /**
     * 生产计划 计划结束时间
     */
    @ApiModelProperty(value = "生产计划计划结束时间", required = true)
    private Date workPlanEndTime;

}









