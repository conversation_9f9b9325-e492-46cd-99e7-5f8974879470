package com.hvisions.pms.plan;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title:HvPmWorkPlanAddAndUpdateDTO </p >
 * <p>Description: 新增生产计划和更新生产计划DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/15</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "新增和更新生产计划 DTO")
public class HvPmWorkPlanAddAndUpdateDTO extends SysBaseDTO {

    /**
     * 计划编码
     */
    @ApiModelProperty(value = "计划编码 ")
    private String planCode;

    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    private Integer materialId;


    /**
     * 工艺路线id
     */
    @ApiModelProperty(value = "工艺路线id")
    private Integer routeId;


    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;
    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    private BigDecimal quantity = BigDecimal.ZERO;

    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id")
    private Integer cellId;

    /**
     * 车间id
     */
    @ApiModelProperty(value = "车间id")
    private Integer  areaId;

    /**
     * 车间名称
     */
    @ApiModelProperty(value = "车间名称")
    private String areaName;


    /**
     * 产线名称
     */
    @ApiModelProperty(value = "车间产线名称")
    private String cellName;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 0:组立订单、1：零件订单
     */
    @ApiModelProperty(value = "使用类型 0:组立订单、1：零件订单")
    private Integer usedType;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipNo;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipModel;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;


}