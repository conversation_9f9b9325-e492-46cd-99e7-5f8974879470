package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: EquipmentUserQueryDTO</p >
 * <p>Description: 人员工位关系关联查询</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/12</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class WorkCenterUserQueryDTO {

    /**
     * 工位ID
     */
    @ApiModelProperty(value = "工位ID")
    private Integer id;

    /**
     * 工位编码(唯一）
     */
    @ApiModelProperty(value = "工位编码,不为空")
    private String code;
    /**
     * 工位名称
     */
    @ApiModelProperty(value = "工位名称,不为空")
    private String name;


    /**
     * 人员ID与名称DTO
     */
    @ApiModelProperty(value = "人员ID与名称DTO")
    private List<WorkCenterUserNameDTO> userNameDTO;


    /**
     * 班组ID与名称DTO
     */
    @ApiModelProperty(value = "班组ID与名称DTO")
    private List<WorkCenterCrewNameDTO> crewNameDTO;


}
