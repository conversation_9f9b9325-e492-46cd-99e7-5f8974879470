package com.hvisions.pms.dto;


import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description HvPmMaterialPreparationDetailDTO
 * <AUTHOR>
 * @Date 2024-05-21
 */
@Data
public class HvPmMaterialPreparationDetailDTO extends PageInfo {

    @ApiModelProperty(value = "主键ID")
    private Long id;
    @ApiModelProperty(value = "主表ID")
    private Long preparationId;
    @ApiModelProperty(value = "任务号")
    private String taskCode;
    @ApiModelProperty(value = "物料号")
    private String materialCode;
    @ApiModelProperty(value = "型材原材规格")
    private String sepces;
    @ApiModelProperty(value = "需求数量")
    private Integer reqQuantity;
    @ApiModelProperty(value = "实际数量")
    private Integer actQuantity;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "状态：0-新建,1-已下发,2-下发失败,3-完成")
    private Integer status=0;
    @ApiModelProperty(value = "是否占用，0:未占用，1:已占用")
    private Integer occupy;
    @ApiModelProperty(value = "工单号")
    private String workOrderCode;
    @ApiModelProperty(value = "船号")
    private String shipCode;
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;

}
