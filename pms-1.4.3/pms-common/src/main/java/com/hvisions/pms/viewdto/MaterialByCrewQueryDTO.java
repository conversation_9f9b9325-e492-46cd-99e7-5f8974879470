package com.hvisions.pms.viewdto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <p>Title: MaterialByCrewQueryDTO</p >
 * <p>Description: 时间查询条件</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/19</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
@ApiModel(value = "班组产出物料查询条件DTO")
public class MaterialByCrewQueryDTO {


    /**
     * 工单实际开始时间
     */
    @ApiModelProperty(value = "工单实际开始时间")
    private Date actualStartTime;
    /**
     * 工单实际结束时间
     */
    @ApiModelProperty(value = "工单实际结束时间")
    private Date actualEndTime;

    /**
     * 产线ID
     */
    @ApiModelProperty(value = "产线ID")
    private Integer cellId;

}
