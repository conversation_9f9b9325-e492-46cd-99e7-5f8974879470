package com.hvisions.pms.plan;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: HvPmWorkPlanDetailDTO</p >
 * <p>Description: 生产计划详情DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "生产计划明细 DTO")
public class HvPmWorkPlanDetailDTO extends SysBaseDTO {

    /**
     * 生产计划id
     */
    @ApiModelProperty(value = "生产计划id")
    private Integer workPlanId;


    /**
     * 生产计划编号
     */
    @ApiModelProperty(value = "生产计划编号")
    private String workPlanCode;

    /**
     * 下发工单id
     */
    @ApiModelProperty(value = "下发工单id")
    private Integer workOrderId;


    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String workOrderCode;
    /**
     * 下发数量
     */
    @ApiModelProperty(value = "下发数量")
    private BigDecimal quantity;
    /**
     * 是否已经报工
     */
    @ApiModelProperty(value = "是否已经报工")
    private Boolean finished;
    /**
     * 报工时间
     */
    @ApiModelProperty(value = "报工时间")
    private Date finishTime;

    /**
     * 报工数量
     */
    @ApiModelProperty(value = "报工数量")
    private Integer finishQuantity;




}