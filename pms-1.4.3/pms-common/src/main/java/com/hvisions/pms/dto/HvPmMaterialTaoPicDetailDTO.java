package com.hvisions.pms.dto;

/**
 * <p>Title: HvPmMaterialTaoPicDetailDTO</p>
 * <p>Description:  </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2024年4月19日</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.*;

@Getter
@Setter
@ToString
public class HvPmMaterialTaoPicDetailDTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id", notes = "id")
    private Long id;
    /**
     * tao_pic_id
     */
    @ApiModelProperty(value = "tao_pic_id", notes = "tao_pic_id")
    private Long taoPicId;
    /**
     * 套料图编号
     */
    @ApiModelProperty(value = "套料图编号", notes = "套料图编号")
    private String taoFileCode;
    /**
     * 原料规格
     */
    @ApiModelProperty(value = "原料规格", notes = "原料规格")
    private String specifications;
    /**
     * 长度
     */
    @ApiModelProperty(value = "长度", notes = "长度")
    private String length;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量", notes = "数量")
    private Integer quantity;
    /**
     * 重量
     */
    @ApiModelProperty(value = "重量", notes = "重量")
    private String weight;
}









