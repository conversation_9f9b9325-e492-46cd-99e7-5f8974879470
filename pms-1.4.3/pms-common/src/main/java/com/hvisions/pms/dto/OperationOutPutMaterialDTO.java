package com.hvisions.pms.dto;

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <p>Title: OperationOutPutMaterialDTO</p >
 * <p>Description: 工序产出物料DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperationOutPutMaterialDTO extends SysBaseDTO {

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 产出物料ID
     */
    @ApiModelProperty(value = "产出物料ID")
    private Integer materialId;

    /**
     * 工序ID
     */
    @ApiModelProperty(value = "工序ID")
    private Integer operationId;

    /**
     * 任务Id
     */
    @ApiModelProperty(value = "任务ID")
    private Integer taskId;

    /**
     * 产出物料编码
     */
    @ApiModelProperty(value = "产出物料编码")
    private String materialCode;

    /**
     * 产出物料名称
     */
    @ApiModelProperty(value = "产出物料名称")
    private String materialName;

    /**
     * 产出物料特征值
     */
    @ApiModelProperty(value = "产出物料特征值")
    private String eigenvalue;

    /**
     * 产出物料数量
     */
    @ApiModelProperty(value = "产出物料数量")
    private BigDecimal outPutCount;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "物料批次号")
    private String batchNumber;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Integer userId;

}
