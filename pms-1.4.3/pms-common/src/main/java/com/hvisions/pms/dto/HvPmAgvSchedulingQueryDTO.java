package com.hvisions.pms.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("调度任务查询参数")
public class HvPmAgvSchedulingQueryDTO extends PageInfo {

//    @ApiModelProperty(value = "调度任务编号")
//    private String taskId;
    @ApiModelProperty(value = "任务类型 0-空框请求；1-满框调度；2-其他")
    private Integer taskType;

    @ApiModelProperty(value = "AGV编号")
    private Integer agvNo;

    @ApiModelProperty(value = "AGV类型")
    private String agvType;

    @ApiModelProperty(value = "料框/托盘编号")
    private String palletNo;

    @ApiModelProperty(value = "料框/托盘类型")
    private String palletType;

    @ApiModelProperty(value = "调度状态 0-带开始；1-待走出储位；2-运输中；3-待入储位；4-完成；5-取消")
    private Integer schedulingState;

    @ApiModelProperty(value = "计划开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date plannedStartTime;

    @ApiModelProperty(value = "计划结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date plannedEndTime;


}
