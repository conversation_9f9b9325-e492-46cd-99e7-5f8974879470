package com.hvisions.pms.dto;

/**
 * <p>Title: HvPmXcMaterialCutPlanDTO</p>
 * <p>Description:  </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2024年4月15日</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */

import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.*;

@Getter
@Setter
@ToString
@Deprecated
public class HvPmXcMaterialCutPlanDTO {
    /**
     * id
     */
    @ApiModelProperty(value = "id", notes = "id")
    private Integer id;
    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号", notes = "任务号")
    private String taskCode;
    /**
     * 产线号
     */
    @ApiModelProperty(value = "产线号", notes = "产线号")
    private String lineCode;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", notes = "工单号")
    private String workOrder;
    /**
     * 船型
     */
    @ApiModelProperty(value = "船型", notes = "船型")
    private String model;
    /**
     * 分段
     */
    @ApiModelProperty(value = "分段", notes = "分段")
    private String segmentationCode;
    /**
     * 套料图编号
     */
    @ApiModelProperty(value = "套料图编号", notes = "套料图编号")
    private String taoFileCode;
    /**
     * 原料规格
     */
    @ApiModelProperty(value = "原料规格", notes = "原料规格")
    private String specifications;
    /**
     * 长度
     */
    @ApiModelProperty(value = "长度", notes = "长度")
    private String length;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量", notes = "数量")
    private String quality;
    /**
     * 重量
     */
    @ApiModelProperty(value = "重量", notes = "重量")
    private String weight;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间", notes = "计划结束时间")
    private String planEndTime;
    /**
     * 下发操作人
     */
    @ApiModelProperty(value = "下发操作人", notes = "下发操作人")
    private Integer sendUserId;
    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间", notes = "下发时间")
    private String sendTime;
    /**
     * 下发人名字
     */
    @ApiModelProperty(value = "下发人名字", notes = "下发人名字")
    private String userName;
}









