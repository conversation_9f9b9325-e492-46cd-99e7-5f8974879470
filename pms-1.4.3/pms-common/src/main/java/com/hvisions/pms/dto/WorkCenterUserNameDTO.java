package com.hvisions.pms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>Title: EquipmentUserNameDTO</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class WorkCenterUserNameDTO {


    /**
     * id
     */
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 人员名称
     */
    @ApiModelProperty(value = "人员名称")
    private String userName;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID列表")
    private Integer userId;


    public String getUserName() {
        if (userName == null) {
            return "用户已被删除";
        } else {
            return userName;
        }
    }
}
