package com.hvisions.pms.dto;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description HvPmMaterialPreparationDTO
 * <AUTHOR>
 * @Date 2024-05-21
 */
@Data
public class HvPmMaterialPreparationDTO extends PageInfo {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID", notes = "主键ID")
    private Long id;
    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号", notes = "任务号")
    private String taskCode;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号", notes = "工单号")
    private String workOrderCode;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间", notes = "申请时间")
    private Date applyTime;
    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间", notes = "完成时间")
    private Date finishTime;
    /**
     * 状态（0：新建，1：已下发，2：下发失败,3:完成）
     */
    @ApiModelProperty(value = "状态", notes = "状态")
    private Integer status = 0;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", notes = "创建时间")
    private Date createTime = new Date();
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", notes = "更新时间")
    private Date updateTime = new Date();

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipCode;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;
}
