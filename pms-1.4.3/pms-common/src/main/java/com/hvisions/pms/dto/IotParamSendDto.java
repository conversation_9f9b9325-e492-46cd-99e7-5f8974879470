package com.hvisions.pms.dto;

import lombok.Data;

import java.util.List;

/**
 * <p>Title: IotParmSendDto</p >
 * <p>Description: 参数下发</p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/19</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
@Data
public class IotParamSendDto {

    private String configName;

    private List<IotSendData> sendDatas;

    @Data
    public static class IotSendData {

        private String code;

        private List<IotFieldValue> values;
    }
}
