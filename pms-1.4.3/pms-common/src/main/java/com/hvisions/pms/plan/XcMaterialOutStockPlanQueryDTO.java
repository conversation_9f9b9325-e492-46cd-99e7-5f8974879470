package com.hvisions.pms.plan;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/5/7
 */
@Data
@ApiModel(description = "型材出库计划查询条件")
public class XcMaterialOutStockPlanQueryDTO extends PageInfo {
    /**
     * 任务号
     */
    @ApiModelProperty(value = "任务号")
    private String taskNo;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String  workOrderCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    /**
     * 状态(0:新建,1:已下发,2:下发失败)
     */
    @ApiModelProperty(value = "状态(0:新建,1:已下发,2:下发失败)")
    private Integer status;
}
