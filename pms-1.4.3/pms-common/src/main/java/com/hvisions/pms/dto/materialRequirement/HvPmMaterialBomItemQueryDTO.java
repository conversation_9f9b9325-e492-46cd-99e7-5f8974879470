package com.hvisions.pms.dto.materialRequirement;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 中间表
 */
@Data
@ApiModel(description = "物料需求清单Bom（子项Bom）")
public class HvPmMaterialBomItemQueryDTO extends PageInfo {
//    子项物料对应的物料需求编号
    @ApiModelProperty(value = "物料需求编号")
    private String materialRequirementCode;
}
