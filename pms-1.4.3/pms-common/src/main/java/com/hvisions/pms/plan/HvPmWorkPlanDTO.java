package com.hvisions.pms.plan;

import com.hvisions.common.annotation.ExcelAnnotation;
import com.hvisions.pms.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>Title: HvPmWorkPlanDTO</p >
 * <p>Description: 生产计划DTO</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/3/14</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "生产计划 DTO")
public class HvPmWorkPlanDTO extends SysBaseDTO {

    /**
     * 计划编码
     */
    @ApiModelProperty(value = "计划编码")
    private String planCode;

    /**
     * 物料id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "物料id")
    private Integer materialId;


    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String materialCode;


    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    /**
     * 物料特征值
     */
    @ApiModelProperty(value = "物料特征值")
    private String eigenvalue;
    /**
     * ,    /  //
     * 工艺路线id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "工艺路线id")
    private Integer routeId;
    /**
     * 工艺路线编码
     */
    @ApiModelProperty(value = "工艺路线编码")
    private String routeCode;
    /**
     * 工艺路线名称
     */
    @ApiModelProperty(value = "工艺路线名称")
    private String routeName;
    /**
     * 工艺路线版本
     */
    @ApiModelProperty(value = "工艺路线版本")
    private String routeVersion;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private Date planStartTime;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private Date planEndTime;
    /**
     * 计划数量
     */
    @ApiModelProperty(value = "计划数量")
    private BigDecimal quantity;


    /**
     * 下发数量
     */
    @ApiModelProperty(value = "下发数量")
    private BigDecimal issuedQuantity;

    /**
     * 实际下发时间
     */
    @ApiModelProperty(value = "实际下发时间")
    private Date actualIssuedTime;

    /**
     * 计划开始时间早于
     */
    @ApiModelProperty(value = "计划开始时间早于")
    private Date timeBeforeStartTime;
    /**
     * 计划开始时间晚于
     */
    @ApiModelProperty(value = "计划开始时间晚于")
    private Date timeAfterStartTime;
    /**
     * 计划结束时间早于
     */
    @ApiModelProperty(value = "计划结束时间早于")
    private Date timeBeforeEndTime;
    /**
     * 计划结束时间晚于
     */
    @ApiModelProperty(value = "计划结束时间晚于")
    private Date timeAfterEndTime;


    /**
     * 产线id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "产线id")
    private Integer cellId;


    /**
     * 车间id
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "车间id")
    private Integer areaId;


    /**
     * 计划状态
     */
    @ApiModelProperty(value = "计划状态", allowableValues = "0,1,2,3")
    private Integer planStatus;

    /**
     * 车间名称
     */
    @ApiModelProperty(value = "车间名称")
    private String areaName;

    /**
     * 产线名称
     */
    @ApiModelProperty(value = "产线名称")
    private String cellName;


    /**
     * 当前完成数量
     */
    @ApiModelProperty(value = "计划当前完成数量")
    private BigDecimal finishedQuantity;


    /**
     * 完成百分比
     */
    @ApiModelProperty(value = "完成百分比")
    private Float progress;

    /**
     * 完工状态
     */
    @ApiModelProperty(value = "完工状态")
    private String planFinishStatus;


    /**
     * 0:组立订单、1：零件订单
     */
    @ApiModelProperty(value = "使用类型 0:组立订单、1：零件订单")
    private Integer usedType;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String shipNo;

    /**
     * 船型
     */
    @ApiModelProperty(value = "船型")
    private String shipModel;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentationCode;
}