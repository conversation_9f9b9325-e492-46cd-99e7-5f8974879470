package com.hvisions.pms.plan;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <P> 打磨计划查询条件   <P>
 *
 * <AUTHOR>
 * @date 2024/9/22
 */
@Data
@ApiModel(description = "打磨计划查询条件")
public class HvPmPolishPlanTabQueryDTO extends PageInfo {



    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号")
    private String code;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String work_order_code;


    /**
     * 物料号
     */
    @ApiModelProperty(value = "物料号")
    private String material_code;

    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    private String pod_code;


    /**
     * 产线ID
     */
    @ApiModelProperty(value = "产线ID")
    private Integer lineId;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String ship_number;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentation_code;


    /**
     * 工位编号
     */
    @ApiModelProperty(value = "工位编号")
    private String station_type;

    /**
     * 下发开始时间
     */
    @ApiModelProperty(value = "下发开始时间")
    private Date sendBeginTime;

    /**
     * 下发结束时间
     */
    @ApiModelProperty(value = "下发结束时间")
    private Date sendEndTime;

    /**
     * 完成开始时间
     */
    @ApiModelProperty(value = "完成开始时间")
    private Date completionStartTime;

    /**
     * 完成结束时间
     */
    @ApiModelProperty(value = "完成结束时间")
    private Date completionEndTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;


}
