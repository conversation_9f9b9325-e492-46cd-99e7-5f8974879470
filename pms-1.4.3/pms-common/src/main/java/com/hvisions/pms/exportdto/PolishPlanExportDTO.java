package com.hvisions.pms.exportdto;

import com.hvisions.common.annotation.ExcelAnnotation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <P>  打磨计划导出：主表DTO  <P>
 *
 * <AUTHOR>
 * @date 2025/1/2
 */
@Data
public class PolishPlanExportDTO {

    /**
     * 主键;
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "主键", example = "1")
    protected Long id;

    /**
     * 创建时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建时间", notes = "此字段不必传递", readOnly = true)
    protected Date createTime = new Date();

    /**
     * 修改时间
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新时间", notes = "此字段不必传递", readOnly = true)
    protected Date updateTime = new Date();

    /**
     * 创建人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "创建用户Id", notes = "创建记录时传递", readOnly = true)
    protected Integer creatorId = 0;

    /**
     * 修改人
     */
    @ExcelAnnotation(ignore = true)
    @ApiModelProperty(value = "更新用户Id", notes = "更新记录时传递", readOnly = true)
    protected Integer updaterId = 0;

    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String work_order_code;

    /**
     * 任务编号
     */
    @ApiModelProperty(value = "任务编号")
    private String code;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String name;

    /**
     * 产线名称
     */
    @ApiModelProperty(value = "产线名称")
    private String lineName;



    /**
     * 料框编号
     */
    @ApiModelProperty(value = "料框编号")
    private String pod_code;


    /**
     * 产线id
     */
    @ApiModelProperty(value = "产线id")
    private Integer lineId;

    /**
     * 线体编号
     */
    @ApiModelProperty(value = "线体编号")
    private String line_code;

    /**
     * 船号
     */
    @ApiModelProperty(value = "船号")
    private String ship_number;

    /**
     * 分段号
     */
    @ApiModelProperty(value = "分段号")
    private String segmentation_code;

    /**
     * 工位编号
     */
    @ApiModelProperty(value = "工位编号")
    private String station_type;

    /**
     * 工位名称
     */
    @ApiModelProperty(value = "工位名称")
    private String station_name;

    /**
     * 加工数量
     */
    @ApiModelProperty(value = "加工数量")
    private Integer qty;

    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    private String type;

    /**
     * 班次
     */
    @ApiModelProperty(value = "班次")
    private String batch_no;


    /**
     * 实际开始时间
     */
    @ApiModelProperty(value = "实际开始时间")
    private String actual_start_time;

    /**
     * 实际完成时间
     */
    @ApiModelProperty(value = "实际完成时间")
    private String actual_completion_time;

    /**
     * 消耗工时(分)
     */
    @ApiModelProperty(value = "消耗工时(分)")
    private Integer deplete_time;

    /**
     * 生产设备编码
     */
    @ApiModelProperty(value = "生产设备编码")
    private String equipment_code;

    /**
     * 报工用户编码
     */
    @ApiModelProperty(value = "报工用户编码")
    private String report_user_code;

    /**
     * 报工用户姓名
     */
    @ApiModelProperty(value = "报工用户姓名")
    private String report_user_name;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 下发操作人
     */
    @ApiModelProperty(value = "下发操作人")
    private Integer sendUserId;

    /**
     * 下发操作人姓名
     */
    @ApiModelProperty(value = "下发操作人姓名")
    private String userName;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    private Date sendTime;
}


