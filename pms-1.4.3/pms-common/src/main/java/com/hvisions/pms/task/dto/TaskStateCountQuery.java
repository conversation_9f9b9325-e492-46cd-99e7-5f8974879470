package com.hvisions.pms.task.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>Title: TaskStateCountQuery</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2022/2/11</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Data
public class TaskStateCountQuery {

    @ApiModelProperty(value = "工位id")
    private Integer workCenterId;

    @ApiModelProperty(value = "人员id")
    private Integer userId;
}