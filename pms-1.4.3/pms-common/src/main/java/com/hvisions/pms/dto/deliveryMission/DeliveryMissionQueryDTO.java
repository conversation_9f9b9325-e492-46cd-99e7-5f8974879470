package com.hvisions.pms.dto.deliveryMission;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DeliveryMissionQueryDTO extends PageInfo {

    /**
     * 配送任务编号(PK)
     */
    private String deliveryMissionCode;

    /**
     * 关联生产工单号
     */
    private String productWorkOrderCode;

    /**
     * 关联需求单号
     */
    private String requirementCode;

    /**
     * 优先级（默认为1，数字越大，优先级越高）
     */
    private Integer priority;

    /**
     * 托盘编号
     */
    private String palletNo;

    /**
     * 配送状态(0-待开始、1-进行中、2-已完成)
     */
    private Integer status;

    /**
     * 配送类型（0-自动配送、1-人工配送）
     */
    private Integer deliveryType;

    /**
     * 计划开始配送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @ApiModelProperty(value = "计划开始配送时间")
    private LocalDateTime planDeliveryStartTime;

    /**
     * 计划结束配送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    @ApiModelProperty(value = "计划结束配送时间")
    private LocalDateTime planDeliveryEndTime;

}
