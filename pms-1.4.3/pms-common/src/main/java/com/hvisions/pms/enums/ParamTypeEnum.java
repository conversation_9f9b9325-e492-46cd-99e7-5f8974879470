package com.hvisions.pms.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: ParamTypeEnum</p >
 * <p>Description: </p >
 * <p>Company: <a href="www.h-visions.com">www.h-visions.com</a></p >
 * <p>create date: 2022/9/19</p >
 *
 * <AUTHOR> x.l
 * @version :1.0.0
 */
public enum ParamTypeEnum {

    INTEGER(1, "整数"),
    FLOAT(2, "浮点数"),
    STRING(3, "字符串"),
    Boolean(4, "布尔值"),
    DATE(5, "日期"),
    TIME(5, "时间"),
    BIGDECIMAL(7, "精确小数");

    private final Integer type;
    private final String name;

    ParamTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return this.type;
    }

    public String getName() {
        return this.name;
    }


    public static Boolean isNumber(Integer type) {
        if (type == null) {
            return false;
        }
        List<Integer> numberTypes = new ArrayList<>();
        numberTypes.add(INTEGER.getType());
        numberTypes.add(FLOAT.getType());
        numberTypes.add(BIGDECIMAL.getType());
        return numberTypes.contains(type);
    }
}
