<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>qms</artifactId>
        <groupId>com.hvisions</groupId>
        <version>1.1.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>qms-client</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-feign</artifactId>
            <version>1.4.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>qms-common</artifactId>
            <version>1.1.4</version>
        </dependency>
    </dependencies>

</project>
