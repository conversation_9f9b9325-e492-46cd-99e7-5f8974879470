package com.hvisions.qms.client;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.rule.ResultCountRulesDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: InspectionCountingRulesClientFallBack</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/10/31</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class InspectionCountingRulesClientFallBack extends BaseFallbackFactory<InspectionCountingRulesClient> {
    @Override
    public InspectionCountingRulesClient getFallBack(ResultVO vo) {
        return new InspectionCountingRulesClient() {
            @Override
            public ResultVO<List<ResultCountRulesDTO>> getAllCountingRules(Integer standardId) {
                return vo;
            }
        };
    }
}