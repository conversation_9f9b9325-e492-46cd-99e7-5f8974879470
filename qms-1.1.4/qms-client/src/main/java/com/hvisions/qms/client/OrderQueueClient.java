package com.hvisions.qms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.inspection.OrderQueueDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: OrderQueueClient</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/21</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@FeignClient(name = "qms", path = "/orderQueue", fallback = OrderQueueFallClient.class)
public interface OrderQueueClient {

    /**
     * 创建质检单队列
     *
     * @param orderQueueDTO 质检单队列信息
     * @return 执行结果
     */
    @PostMapping(value = "/createOrderQueue")
    ResultVO createOrderQueue(@RequestBody OrderQueueDTO orderQueueDTO);

    /**
     * 删除质检单队列
     *
     * @param id 队列Id
     * @return 执行结果
     */
    @DeleteMapping(value = "/deleteOrderQueue/{id}")
    ResultVO deleteOrderQueue(@PathVariable int id);

    /**
     * 根据工位查询队列
     *
     * @param stepId 工位
     * @return 队列信息
     */
    @GetMapping(value = "/getOrderQueueByStep/{stepId}")
    ResultVO<List<OrderQueueDTO>> getOrderQueueByStep(@PathVariable Integer stepId);


    /**
     * 设定队列信息为当前队列
     *
     * @param id 队列ID
     * @return 执行结果
     */
    @PutMapping(value = "/setQueueCurrent/{id}")
    ResultVO setQueueCurrent(@PathVariable int id);

    /**
     * 设置质检单队列工位信息
     *
     * @param id     质检单队列id
     * @param stepId 工位Id
     */
    @PutMapping(value = "/setOrderQueueStep/{id}/{stepId}/{stepName}")
    ResultVO setOrderQueueStep(@PathVariable Integer id, @PathVariable Integer stepId,
                               @PathVariable String stepName);
}