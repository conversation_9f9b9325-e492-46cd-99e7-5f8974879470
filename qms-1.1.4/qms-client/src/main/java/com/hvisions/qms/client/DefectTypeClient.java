package com.hvisions.qms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.DefectTypeDTO;
import com.hvisions.qms.dto.DefectTypeQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "qms", fallbackFactory = DefectTypeClientFallBack.class)

public interface DefectTypeClient {

    /**
     * 新增缺陷类型信息
     *
     * @param defectTypeDTO 缺陷类型信息
     * @return 缺陷类型信息id
     */
    @ApiOperation(value = "新增缺陷类型信息")
    @RequestMapping(value = "/defectType/addDefectType", method = RequestMethod.POST)
    ResultVO<Integer> add(@RequestBody DefectTypeDTO defectTypeDTO) ;

    /**
     * 修改缺陷类型信息
     *
     * @param defectTypeDTO 缺陷类型信息
     * @return 缺陷类型信息id
     */
    @ApiOperation(value = "修改缺陷类型信息")
    @RequestMapping(value = "/defectType/updateDefectType", method = RequestMethod.PUT)
    ResultVO<Integer> update(@RequestBody DefectTypeDTO defectTypeDTO);

    /**
     * 根据id删除缺陷类型信息
     *
     * @param id 缺陷类型信息id
     * @return result
     */
    @ApiOperation(value = "删除缺陷类型信息")
    @RequestMapping(value = "/defectType/deleteDefectTypeById/{id}", method = RequestMethod.DELETE)
    ResultVO delete(@PathVariable Integer id);


    /**
     * 分页查询
     *
     * @param defectGradeQueryDTO 缺陷类型信息分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/defectType/getDefectTypePageQuery")
    @ApiOperation(value = "缺陷类型分页查询")
    ResultVO<HvPage<DefectTypeDTO>> getDefectTypePageQuery(@RequestBody DefectTypeQueryDTO defectGradeQueryDTO) ;

    /**
     * 获取所有类型信息
     *
     * @return 类型信息列表
     */
    @ApiOperation(value = "获取所有缺陷类型")
    @RequestMapping(value = "/defectType/getAllDefectType", method = RequestMethod.GET)
    ResultVO< List<DefectTypeDTO>> getAllDefectType();

    /**
     * 根据id获取类型信息
     * @param id 主键id
     * @return 类型信息
     */
    @ApiOperation(value = "根据id获取类型信息")
    @RequestMapping(value = "/defectType/getDutyDepartmentById/{id}", method = RequestMethod.GET)
    ResultVO<DefectTypeDTO> getDutyDepartmentById(@PathVariable Integer id);

    @ApiOperation(value = "根据code获取类型信息")
    @RequestMapping(value = "/defectType/getDefectTypeByCode/{code}", method = RequestMethod.GET)
    ResultVO<DefectTypeDTO> getDefectTypeByCode(@PathVariable String code);
}
