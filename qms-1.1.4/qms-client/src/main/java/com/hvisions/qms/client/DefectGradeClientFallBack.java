package com.hvisions.qms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.DefectGradeDTO;
import com.hvisions.qms.dto.DefectGradeQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DefectGradeClientFallBack extends BaseFallbackFactory<DefectGradeClient> {
    @Override
    public DefectGradeClient getFallBack(ResultVO vo) {
        return new DefectGradeClient() {
            @Override
            public ResultVO<Integer> add(DefectGradeDTO defectGradeDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> update(DefectGradeDTO defectGradeDTO) {
                return vo;
            }

            @Override
            public ResultVO delete(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<DefectGradeDTO>> getDefectGradePageQuery(DefectGradeQueryDTO defectGradeQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<DefectGradeDTO>> getAllDefectGrade() {
                return vo;
            }

            @Override
            public ResultVO<DefectGradeDTO> getGradeById(Integer id) {
                return vo;
            }
            @Override
            public ResultVO<DefectGradeDTO> getDefectGradeByCode(String code) {
                return vo;
            }
        };
    }
}
