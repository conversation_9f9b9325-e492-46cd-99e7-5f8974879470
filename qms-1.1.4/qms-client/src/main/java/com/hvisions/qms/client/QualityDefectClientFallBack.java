package com.hvisions.qms.client;

import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */
@Component
public class QualityDefectClientFallBack extends BaseFallbackFactory<QualityDefectClient> {
    @Override
    public QualityDefectClient getFallBack(ResultVO vo) {
        return new QualityDefectClient() {

            @Override
            public ResultVO<List<DefectDTO>> getAllDefect() {
                return vo;
            }

            @Override
            public ResultVO<DefectTypeDTO> getDutyDepartmentById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<List<DefectDTO>> getDefectsByRouteStepId(Integer routeStepId) {
                return vo;
            }

            @Override
            public ResultVO<List<DefectGradeDTO>> getAllDefectGrade() {
                return vo;
            }

            @Override
            public ResultVO<DefectDTO> getDefectById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<DefectGradeDTO> getGradeById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<Integer> add(DefectDTO defectDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> update(DefectDTO defectDTO) {
                return vo;
            }

            @Override
            public ResultVO delete(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<List<HvQmQualityDefectRouteOperationDTO>> getDefectByConQuery(DefectRouteDTO defectQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<Set<DefectDTO>> getDefectByConMoreQuery(DefectRouteMoreDTO defectQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<Integer>> getRouteStepIdsByDefectId(Integer defectId) {
                return vo;
            }

            @Override
            public ResultVO<List<Integer>> saveOrUpdateRouteBind(DefectDTO defectDTO) {
                return vo;
            }

            @Override
            public ResultVO defectBindOperationMore(DefectBindOperationDTO defectDTO) {
                return vo;
            }

            @Override
            public ResultVO<ExcelExportDto> exportDefect() {
                return vo;
            }

            @Override
            public ResultVO<ResponseEntity<byte[]>> export() {
                return vo;
            }

            @Override
            public ResultVO<ImportResult> importParameter(MultipartFile file) {
                return vo;
            }
        };
    }
}









