package com.hvisions.qms.client;


import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.position.PositionDTO;
import com.hvisions.qms.dto.position.PositionMoreDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class PositionClientFallBack extends BaseFallbackFactory<PositionClient> {
    @Override
    public PositionClient getFallBack(ResultVO vo) {
        return new PositionClient() {
            @Override
            public ResultVO<Integer> createPosition(PositionDTO positionDTO) {
                return vo;
            }

            @Override
            public ResultVO deletePositionById(int id) {
                return vo;
            }

            @Override
            public ResultVO<List<PositionDTO>> getPositionList(PositionDTO positionDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<PositionMoreDTO>> getPositionListByTemplateId(int id) {
                return vo;
            }

            @Override
            public ResultVO<PositionMoreDTO> getPositionListById(int id) {
                return vo;
            }
        };
    }
}
