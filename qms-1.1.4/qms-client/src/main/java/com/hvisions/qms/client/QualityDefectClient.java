package com.hvisions.qms.client;

import com.hvisions.common.annotation.ApiResultIgnore;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.interfaces.ImportResult;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */
@FeignClient(name = "qms", fallbackFactory = QualityDefectClientFallBack.class)
public interface QualityDefectClient {


    /**
     * 获取所有缺陷
     *
     * @return 返回所有缺陷信息
     */
    @GetMapping("/defect/getAllDefect")
    ResultVO<List<DefectDTO>> getAllDefect();

    @GetMapping(value = "/defectType/getDutyDepartmentById/{id}")
    ResultVO<DefectTypeDTO> getDutyDepartmentById(@PathVariable Integer id);

    /**
     * 根据工艺步骤id获取缺陷信息列表
     *
     * @param routeStepId 根据工艺步骤id
     * @return 工艺步骤id对应的缺陷信息列表
     */
    @GetMapping("/defect/getDefectsByRouteStepId/{routeStepId}")
    ResultVO<List<DefectDTO>> getDefectsByRouteStepId(@PathVariable Integer routeStepId);


    /**
     * 获取所有缺陷等级
     *
     * @return 返回所有缺陷等级
     */
    @GetMapping("/defectGrade/getAllDefectGrade")
    ResultVO<List<DefectGradeDTO>> getAllDefectGrade();

    /**
     * 根据id获取缺陷信息
     *
     * @param id 主键id
     * @return 缺陷信息
     */
    @GetMapping("/defect/getDefectById/{id}")
    ResultVO<DefectDTO> getDefectById(@PathVariable Integer id);


    /**
     * 根据id获取缺陷等级信息
     *
     * @param id 主键id
     * @return 缺陷等级信息
     */
    @GetMapping("/defectGrade/getGradeById/{id}")
    ResultVO<DefectGradeDTO> getGradeById(@PathVariable Integer id);


    /**
     * 新增缺陷信息
     *
     * @param defectDTO 缺陷信息
     * @return 缺陷信息id
     */
    @ApiOperation(value = "新增缺陷信息")
    @RequestMapping(value = "/defect/addDefect", method = RequestMethod.POST)
    ResultVO<Integer> add(@RequestBody DefectDTO defectDTO);

    /**
     * 修改缺陷信息
     *
     * @param defectDTO 缺陷信息
     * @return 缺陷信息id
     */
    @ApiOperation(value = "修改缺陷信息")
    @RequestMapping(value = "/defect/updateDefect", method = RequestMethod.PUT)
    ResultVO<Integer> update(@RequestBody DefectDTO defectDTO);

    /**
     * 根据id删除缺陷信息
     *
     * @param id 缺陷信息id
     * @return 对象
     */
    @ApiOperation(value = "删除缺陷信息")
    @RequestMapping(value = "/defect/deleteDefectById/{id}", method = RequestMethod.DELETE)
    ResultVO delete(@PathVariable Integer id);


    /**
     * @param defectQueryDTO 缺陷信息分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/defect/getDefectByConQuery")
    @ApiOperation(value = "根据工艺路线和工艺步骤查询")
    ResultVO<List<HvQmQualityDefectRouteOperationDTO>> getDefectByConQuery(@RequestBody DefectRouteDTO defectQueryDTO);

    /**
     * @param defectQueryDTO 缺陷信息分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/defect/getDefectByConMoreQuery")
    @ApiOperation(value = "根据工艺路线和自定义工艺步骤查询")
    ResultVO<Set<DefectDTO>> getDefectByConMoreQuery(@RequestBody DefectRouteMoreDTO defectQueryDTO);


    /**
     * 根据缺陷id获取工艺操作id列表
     *
     * @param defectId 缺陷id
     * @return 缺陷id对应的工艺操作id列表
     */
    @ApiOperation(value = "根据缺陷id获取工艺操作id列表")
    @RequestMapping(value = "/defect/getRouteOperationIdsByDefectId/{defectId}", method = RequestMethod.GET)
    ResultVO<List<Integer>> getRouteStepIdsByDefectId(@PathVariable Integer defectId);


    /**
     * 保存或更新缺陷-工艺操作绑定数据
     *
     * @param defectDTO 缺陷dto对象
     *                  @return 对象
     */
    @ApiOperation(value = "保存或更新缺陷-工艺操作绑定数据")
    @RequestMapping(value = "/defect/saveOrUpdateRouteBind", method = RequestMethod.PUT)
    ResultVO<List<Integer>> saveOrUpdateRouteBind(@RequestBody DefectDTO defectDTO);

    /**
     * 缺陷批量绑定缺陷
     *
     * @param defectDTO 缺陷dto对象
     *                  @return 对象
     */
    @ApiOperation(value = "缺陷批量绑定工艺操作")
    @RequestMapping(value = "/defect/defectBindOperationMore", method = RequestMethod.PUT)
    ResultVO defectBindOperationMore(@RequestBody DefectBindOperationDTO defectDTO);


    /**
     * 导出所有缺陷信息
     *
     * @return 缺陷信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/defect/exportDefect")
    @ApiOperation(value = "导出 数据导出")
    ResultVO<ExcelExportDto> exportDefect()  throws IOException, IllegalAccessException;

    /**
     * 导出所有缺陷信息
     *
     * @return 缺陷信息Excel
     * @throws IOException            io异常
     * @throws IllegalAccessException field访问异常
     */
    @ApiResultIgnore
    @GetMapping(value = "/defect/exportDefectLink")
    @ApiOperation(value = "导出 支持超链接")
    ResultVO<ResponseEntity<byte[]>> export()  throws IOException, IllegalAccessException;

    /**
     * 导入缺陷信息
     *
     * @param file 缺陷文档
     * @return 导入信息
     * @throws IllegalAccessException field访问异常
     * @throws ParseException         转换异常
     * @throws IOException            io异常
     */
    @PostMapping(value = "/defect/importDefectLink")
    @ApiOperation(value = "导入缺陷信息(code存在则更新，不存在则新增)")
    ResultVO<ImportResult> importParameter(@RequestParam("file") MultipartFile file)  throws IOException, IllegalAccessException,ParseException;

}
