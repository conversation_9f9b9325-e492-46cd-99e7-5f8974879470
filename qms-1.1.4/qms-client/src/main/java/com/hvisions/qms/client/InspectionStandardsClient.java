package com.hvisions.qms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.standard.CloneInspectionStandardDTO;
import com.hvisions.qms.dto.standard.InspectionStandardDTO;
import com.hvisions.qms.dto.standard.InspectionStandardQueryDTO;
import com.hvisions.qms.dto.standard.QueryStandardDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@FeignClient(name = "qms", fallbackFactory = InspectionStandardsClientFallBack.class)
public interface InspectionStandardsClient {


    /**
     * 新增质检标准
     *
     * @param inspectionStandardDTO 质检标准
     * @return 主键
     */
    @ApiOperation(value = "新增质检标准")
    @PostMapping(value = "/standards/createInspectionStandard")
    ResultVO<Integer> createInspectionStandard(@RequestBody InspectionStandardDTO inspectionStandardDTO);

    /**
     * 克隆质检标准
     *
     * @param cloneDTO 克隆信息
     * @return 新的标准主键
     */
    @ApiOperation(value = "克隆质检标准")
    @PostMapping(value = "/standards/cloneInspectionStandard")
    ResultVO<Integer> cloneInspectionStandard(@RequestBody CloneInspectionStandardDTO cloneDTO);


    /**
     * 更新质检标准
     *
     * @param inspectionStandardDTO 质检标准
     * @return 是否成功
     */
    @ApiOperation(value = "更新质检标准")
    @PutMapping(value = "/standards/editInspectionStandard")
    ResultVO editInspectionStandard(@RequestBody InspectionStandardDTO inspectionStandardDTO);

    /**
     * 质检标准生效
     *
     * @param standardId 质检标准id
     * @return 是否成功
     */
    @ApiOperation(value = "质检标准生效")
    @GetMapping(value = "/standards/updateInspectionStandardState/{standardId}")
    ResultVO updateInspectionStandardState(@PathVariable Integer standardId);


    /**
     * 删除质检标准
     *
     * @param id 质检标准id
     * @return 是否成功
     */
    @ApiOperation(value = "删除质检标准")
    @DeleteMapping(value = "/standards/deleteStandardById/{id}")
    ResultVO deleteStandardById(@PathVariable int id);

    /**
     * 批量删除质检标准
     *
     * @param ids 质检标准id列表
     * @return 是否成功
     */
    @ApiOperation(value = "批量删除质检标准")
    @DeleteMapping(value = "/standards/deleteInspectionStandardByIds")
    ResultVO deleteInspectionStandardByIds(@RequestBody List<Integer> ids);

    /**
     * 根据多条件分页查询，获取质检标准列表"
     *
     * @param templateDTO 查询条件
     * @return 质检标准分页信息
     */
    @ApiOperation(value = "根据多条件分页查询，获取质检标准列表")
    @PostMapping(value = "/standards/getInspectionStandardPageByConditions")
    ResultVO<HvPage<QueryStandardDTO>> getInspectionStandardPageByConditions(@RequestBody InspectionStandardQueryDTO templateDTO);

    /**
     * 根据质检标准id、获取质检标准结果集"
     *
     * @param id 质检标准id
     * @return 质检标准结果集"
     */
    @ApiOperation(value = "根据质检标准id、获取质检标准结果集")
    @GetMapping(value = "/standards/getInspectionStandardById/{id}")
    ResultVO<QueryStandardDTO> getInspectionStandardById(@PathVariable Integer id);

    /**
     * 根据质检标准id列表、获取质检标准结果集"
     *
     * @param ids 质检标准id列表
     * @return 质检标准结果集"
     */
    @ApiOperation(value = "根据质检标准id、获取质检标准结果集")
    @GetMapping(value = "/standards/getInspectionStandardByIds")
    ResultVO<List<InspectionStandardDTO>> getInspectionStandardById(@RequestParam List<Integer> ids);


    /**
     * 根据标准编码和版本号获取质检标准
     *
     * @param code    标准编码
     * @param version 版本号获
     * @return 质检标准
     */
    @ApiOperation(value = "根据标准编码和版本号获取质检标准")
    @GetMapping(value = "/standards/getInspectionStandardByCodeAndVersion/{code}/{version}")
    ResultVO<QueryStandardDTO> getInspectionStandardByCodeAndVersion(@PathVariable String code, @PathVariable String version);

    /**
     * 根据检测工位，获取质检标准
     *
     * @param operationId 工艺操作id
     * @param materialId  物料id
     * @return 质检标准列表
     */
    @ApiOperation(value = "根据检测工位,物料，获取质检标准")
    @GetMapping(value = "/standards/getStandards")
    ResultVO<List<InspectionStandardDTO>> getStandards(@RequestParam(required = false) Integer operationId,
                                                       @RequestParam(required = false) Integer materialId);


}
