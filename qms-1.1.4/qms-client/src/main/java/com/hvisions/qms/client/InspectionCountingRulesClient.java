package com.hvisions.qms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.rule.ResultCountRulesDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;


/**
 * <AUTHOR>
 */
@FeignClient(name = "qms", fallbackFactory = InspectionCountingRulesClientFallBack.class)
public interface InspectionCountingRulesClient {

    /**
     * 根据质检标准Id查询计算规则
     *
     * @param standardId 质检标准id
     * @return 计算规则
     */
    @GetMapping("/standardsRules/getCountingRulesById/{standardId}")
    @ApiOperation(value = "根据质检标准Id查询计算规则")
    ResultVO<List<ResultCountRulesDTO>> getAllCountingRules(@PathVariable Integer standardId) ;
}
