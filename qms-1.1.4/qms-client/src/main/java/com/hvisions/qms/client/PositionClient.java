package com.hvisions.qms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.position.PositionDTO;
import com.hvisions.qms.dto.position.PositionMoreDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@FeignClient(name = "qms", fallbackFactory = PositionClientFallBack.class)
public interface PositionClient {

    /**
     * 新增
     *
     * @param positionDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "新增")
    @RequestMapping(value = "/position/createPosition", method = RequestMethod.POST)
    ResultVO<Integer> createPosition(@RequestBody PositionDTO positionDTO);

    /**
     * 删除
     *
     * @param id 实体id
     * @return 对象
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/position/deletePositionById/{id}", method = RequestMethod.DELETE)
    ResultVO deletePositionById(@PathVariable int id);

    /**
     * 查询
     *
     * @param positionDTO 传入的对象
     * @return 查询后的位置点以及区域图片等
     */
    @ApiOperation(value = "区域名称查询所有")
    @RequestMapping(value = "/position/getPositionList", method = RequestMethod.POST)
    ResultVO<List<PositionDTO>> getPositionList(@RequestBody PositionDTO positionDTO);

    ;

    /**
     * 查询
     *
     * @param id 传入的对象
     * @return 区域
     */
    @ApiOperation(value = "通过模板Id查询相关区域")
    @GetMapping("/position/getPositionListByTemplateId/{id}")
    ResultVO<List<PositionMoreDTO>> getPositionListByTemplateId(@PathVariable int id);


    /**
     * 查询区域对象
     *
     * @param id 传入区域id
     * @return 区域对象
     */
    @ApiOperation(value = "通过区域Id查询区域,返回区域DTO")
    @GetMapping("/position/getPositionListById/{id}")
    ResultVO<PositionMoreDTO> getPositionListById(@PathVariable int id);
}
