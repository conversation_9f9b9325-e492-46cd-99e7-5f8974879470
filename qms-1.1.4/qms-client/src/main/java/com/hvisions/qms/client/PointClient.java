package com.hvisions.qms.client;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.position.PointDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

@FeignClient(name = "qms", fallbackFactory = PointClientFallBack.class)
/**
 * <AUTHOR>
 */
public interface PointClient {
    /**
     * 新增
     *
     * @param pointDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "非对称新增")
    @RequestMapping(value = "/point/createPointOne", method = RequestMethod.POST)
    ResultVO<Integer> createPointOne(@RequestBody PointDTO pointDTO);

    /**
     * 新增
     *
     * @param pointDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "对称新增")
    @RequestMapping(value = "/point/createPointTwo", method = RequestMethod.POST)
    ResultVO<Integer> createPointTwo(@RequestBody List<PointDTO> pointDTO);

    /**
     * 修改
     *
     * @param pointDTO 传入的对象
     * @return 添加后的实体Id
     */
    @ApiOperation(value = "更新")
    @RequestMapping(value = "/point/updatePoint", method = RequestMethod.PUT)
    ResultVO<Integer> updatePoint(@RequestBody PointDTO pointDTO);

    /**
     * 删除
     *
     * @param id 实体id
     * @return 结果集
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/point/deletePointById/{id}", method = RequestMethod.DELETE)
    ResultVO deletePointById(@PathVariable int id);

    /**
     * 查询
     *
     * @return 实体列表
     */
    @ApiOperation(value = "获取所有")
    @RequestMapping(value = "/point/getPointList", method = RequestMethod.GET)
    ResultVO<List<PointDTO>> getPointList();
}
