package com.hvisions.qms.client;


import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.position.PointDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class PointClientFallBack extends BaseFallbackFactory<PointClient> {

    @Override
    public PointClient getFallBack(ResultVO vo) {
        return new PointClient() {
            @Override
            public ResultVO<Integer> createPointOne(PointDTO pointDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> createPointTwo(List<PointDTO> pointDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updatePoint(PointDTO pointDTO) {
                return vo;
            }

            @Override
            public ResultVO deletePointById(int id) {
                return vo;
            }

            @Override
            public ResultVO<List<PointDTO>> getPointList() {
                return vo;
            }
        };
    }
}
