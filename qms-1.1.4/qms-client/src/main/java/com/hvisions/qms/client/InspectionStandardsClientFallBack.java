package com.hvisions.qms.client;


import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.standard.CloneInspectionStandardDTO;
import com.hvisions.qms.dto.standard.InspectionStandardDTO;
import com.hvisions.qms.dto.standard.InspectionStandardQueryDTO;
import com.hvisions.qms.dto.standard.QueryStandardDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <p>Title: TemplateClientFallBack</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/5/12</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
@Slf4j
public class InspectionStandardsClientFallBack extends BaseFallbackFactory<InspectionStandardsClient> {

    @Override
    public InspectionStandardsClient getFallBack(ResultVO vo) {

        return new InspectionStandardsClient() {
            @Override
            public ResultVO<Integer> createInspectionStandard(InspectionStandardDTO inspectionStandardDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> cloneInspectionStandard(CloneInspectionStandardDTO cloneDTO) {
                return vo;
            }

            @Override
            public ResultVO editInspectionStandard(InspectionStandardDTO inspectionStandardDTO) {
                return vo;
            }

            @Override
            public ResultVO updateInspectionStandardState(Integer standardId) {
                return vo;
            }

            @Override
            public ResultVO deleteStandardById(int id) {
                return vo;
            }

            @Override
            public ResultVO deleteInspectionStandardByIds(List<Integer> ids) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<QueryStandardDTO>> getInspectionStandardPageByConditions(InspectionStandardQueryDTO templateDTO) {
                return vo;
            }

            @Override
            public ResultVO<QueryStandardDTO> getInspectionStandardById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<List<InspectionStandardDTO>> getInspectionStandardById(List<Integer> ids) {
                return vo;
            }

            @Override
            public ResultVO<QueryStandardDTO> getInspectionStandardByCodeAndVersion(String code, String version) {
                return vo;
            }

            @Override
            public ResultVO<List<InspectionStandardDTO>> getStandards(Integer operationId, Integer materialId) {
                return vo;
            }
        };

    }
}







