package com.hvisions.qms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.DefectTypeDTO;
import com.hvisions.qms.dto.DefectTypeQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class DefectTypeClientFallBack extends BaseFallbackFactory<DefectTypeClient> {
    @Override
    public DefectTypeClient getFallBack(ResultVO vo) {
        return new DefectTypeClient() {
            @Override
            public ResultVO<Integer> add(DefectTypeDTO defectTypeDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> update(DefectTypeDTO defectTypeDTO) {
                return vo;
            }

            @Override
            public ResultVO delete(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<DefectTypeDTO>> getDefectTypePageQuery(DefectTypeQueryDTO defectGradeQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<DefectTypeDTO>> getAllDefectType() {
                return vo;
            }

            @Override
            public ResultVO<DefectTypeDTO> getDutyDepartmentById(Integer id) {
                return vo;
            }
            @Override
            public ResultVO<DefectTypeDTO> getDefectTypeByCode(String code) {
                return vo;
            }
        };
    }
}
