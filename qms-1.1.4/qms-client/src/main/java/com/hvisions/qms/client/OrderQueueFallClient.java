package com.hvisions.qms.client;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.inspection.OrderQueueDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: OrderQueueFallClient</p >
 * <p>Description: </p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2020/8/21</p >
 *
 * <AUTHOR> bzy
 * @version :1.0.0
 */
@Component
public class OrderQueueFallClient extends BaseFallbackFactory<OrderQueueClient> {
    @Override
    public OrderQueueClient getFallBack(ResultVO vo) {
        return new OrderQueueClient() {
            @Override
            public ResultVO createOrderQueue(OrderQueueDTO orderQueueDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteOrderQueue(int id) {
                return vo;
            }

            @Override
            public ResultVO<List<OrderQueueDTO>> getOrderQueueByStep(Integer stepId) {
                return vo;
            }

            @Override
            public ResultVO setQueueCurrent(int id) {
                return vo;
            }

            /**
             * 设置质检单队列工位信息
             *
             * @param id       质检单队列id
             * @param stepId   工位Id
             * @param stepName
             */
            @Override
            public ResultVO setOrderQueueStep(Integer id, Integer stepId, String stepName) {
                return vo;
            }
        };
    }
}