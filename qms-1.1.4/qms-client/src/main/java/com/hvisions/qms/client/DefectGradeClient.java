package com.hvisions.qms.client;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.qms.dto.DefectGradeDTO;
import com.hvisions.qms.dto.DefectGradeQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "qms", fallbackFactory = DefectGradeClientFallBack.class)
public interface DefectGradeClient {

    /**
     * 新增缺陷等级信息
     *
     * @param defectGradeDTO 缺陷等级信息
     * @return 缺陷等级信息id
     */
    @ApiOperation(value = "新增缺陷等级信息")
    @RequestMapping(value = "/defectGrade/addDefectGrade", method = RequestMethod.POST)
    ResultVO<Integer> add(@RequestBody DefectGradeDTO defectGradeDTO);

    /**
     * 修改缺陷等级信息
     *
     * @param defectGradeDTO 缺陷等级信息
     * @return 缺陷等级信息id
     */
    @ApiOperation(value = "修改缺陷等级信息")
    @RequestMapping(value = "/defectGrade/updateDefectGrade", method = RequestMethod.PUT)
    ResultVO<Integer> update(@RequestBody DefectGradeDTO defectGradeDTO);

    /**
     * 根据id删除缺陷等级信息
     *
     * @param id 缺陷等级信息id
     * @return 结果
     */
    @ApiOperation(value = "删除缺陷等级信息")
    @RequestMapping(value = "/defectGrade/deleteDefectGradeById/{id}", method = RequestMethod.DELETE)
    ResultVO delete(@PathVariable Integer id);

    /**
     * 分页查询
     *
     * @param defectGradeQueryDTO 缺陷等级信息分页查询条件
     * @return 分页信息
     */
    @PostMapping(value = "/defectGrade/getDefectGradePageQuery")
    @ApiOperation(value = "缺陷等级分页查询")
    ResultVO<HvPage<DefectGradeDTO>> getDefectGradePageQuery(@RequestBody DefectGradeQueryDTO defectGradeQueryDTO);

    /**
     * 获取所有缺陷等级
     *
     * @return 缺陷等级列表
     */
    @ApiOperation(value = "获取所有缺陷等级")
    @RequestMapping(value = "/defectGrade/getAllDefectGrade", method = RequestMethod.GET)
    ResultVO<List<DefectGradeDTO>> getAllDefectGrade();

    /**
     * 根据id获取缺陷等级信息
     *
     * @param id 主键id
     * @return 缺陷等级信息
     */
    @ApiOperation(value = "根据id获取缺陷等级")
    @RequestMapping(value = "/defectGrade/getGradeById/{id}", method = RequestMethod.GET)
    ResultVO<DefectGradeDTO> getGradeById(@PathVariable Integer id);

    @ApiOperation(value = "根据code获取类型信息")
    @RequestMapping(value = "/defectGrade/getDefectGradeByCode/{code}", method = RequestMethod.GET)
    ResultVO<DefectGradeDTO> getDefectGradeByCode(@PathVariable String code) ;

}
