package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.lub.LubTypeDTO;
import com.hvisions.eam.query.lub.LubTypeQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title:LubTypeClient</p>
 * <p>Description:油品类型</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/lubType", fallbackFactory = LubTypeClientFallBack.class)
public interface LubTypeClient {
    /**
     * 增加节点
     *
     * @param lubTypeDTO 油品型号DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加节点")
    @PostMapping(value = "/createAddChildNode")
    ResultVO<Integer> createAddChildNode(@RequestBody LubTypeDTO lubTypeDTO);

    /**
     * 修改节点
     *
     * @param lubTypeDTO 油品型号DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "修改节点")
    @PutMapping(value = "/updateLubType")
    ResultVO<Integer> updateLubType(@RequestBody LubTypeDTO lubTypeDTO);

    /**
     * 查询油品类型 通过ID
     *
     * @param id 备件型号DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "通过ID 查询油品类型信息 ")
    @GetMapping(value = "/getLubType/{id}")
    ResultVO<LubTypeDTO> getLubType(@PathVariable Integer id);

    /**
     * 删除油品型号 通过ID
     *
     * @param id 类型id
     * @return void
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteLubTypeById/{id}")
    ResultVO deleteLubTypeById(@PathVariable Integer id);

    /**
     * 分页查询 通过类型名称
     *
     * @param lubTypeQueryDTO 分页信息
     * @return 分页信息
     */
    @ApiOperation(value = "分页查询 通过类型名称 查询结果为全部父级")
    @PostMapping(value = "/getSparTypePageByTypeName")
    ResultVO<HvPage<LubTypeDTO>> getSparTypePageByTypeName(@RequestBody LubTypeQueryDTO lubTypeQueryDTO);

    /**
     * 查询全部
     *
     * @return 分页信息
     */
    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/getLubType")
    ResultVO<List<LubTypeDTO>> getLubType();

    /**
     * 通过父级ID查询 子级全部信息
     *
     * @param parentId 父级ID
     * @return 子级全部信息
     */
    @ApiOperation(value = "通过父级ID查询 子级全部信息 ")
    @PostMapping(value = "/getChildNode/{parentId}")
    ResultVO<List<LubTypeDTO>> getChildNode(@PathVariable Integer parentId);
}
