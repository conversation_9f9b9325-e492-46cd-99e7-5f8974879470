package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.ItemsLubDTO;
import com.hvisions.eam.activiti.sapre.ItemsSpareDTO;
import com.hvisions.eam.dto.publicstore.ActualUseDTO;
import com.hvisions.eam.dto.publicstore.ApplyDTO;
import com.hvisions.eam.query.publicstore.ActualUseQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:ActualUseClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class ActualUseClientFallBack extends BaseFallbackFactory<ActualUseClient> {
    @Override
    public ActualUseClient getFallBack(ResultVO vo) {
        return new ActualUseClient() {
            @Override
            public ResultVO<Integer> createActualUse(ActualUseDTO actualUseDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<Integer>> createActualUseList(List<ActualUseDTO> actualUseDTOs) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateActualUse(ActualUseDTO actualUseDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteByListId(List<Integer> ids) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<ActualUseDTO>> getSpareActual(ActualUseQueryDTO actualUseQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<ActualUseDTO>> getLubActual(ActualUseQueryDTO actualUseQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<ActualUseDTO>> getSpareApplyThrough(ActualUseQueryDTO actualUseQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<ActualUseDTO>> getSpareApplyThroughEquipment(ActualUseQueryDTO actualUseQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<ActualUseDTO>> getLubApplyThrough(ActualUseQueryDTO actualUseQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<ActualUseDTO>> getLubApplyThroughEquipment(ActualUseQueryDTO actualUseQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<ApplyDTO>> getSpareApply(String businessKey) {
                return vo;
            }

            @Override
            public ResultVO<List<ApplyDTO>> getLubApply(String businessKey) {
                return vo;
            }

            @Override
            public ResultVO<List<ItemsSpareDTO>> getSpareApplyList(String businessKey) {
                return vo;
            }

            @Override
            public ResultVO<List<ItemsLubDTO>> getLubApplyList(String businessKey) {
                return vo;
            }

            @Override
            public ResultVO<List<ActualUseDTO>> getActualUseByProcessInstanceIds(List<String> processInstanceIds) {
                return vo;
            }
        };
    }
}
