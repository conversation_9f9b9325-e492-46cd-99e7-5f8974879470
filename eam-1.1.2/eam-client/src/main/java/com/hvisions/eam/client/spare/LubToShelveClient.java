package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.lub.LubToShelveDTO;
import com.hvisions.eam.query.lub.LubToShelveQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>Title:LubToShelveClient</p>
 * <p>Description:油品库房</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/lubToShelve", fallbackFactory = LubricationClientFallBack.class)
public interface LubToShelveClient {

    /**
     * 增加油品库存关系
     *
     * @param lubToShelveDTO 油品库存关系DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加油品库存关系")
    @PostMapping(value = "/createLubToShelve")
    ResultVO<Integer> createLubToShelve(@RequestBody LubToShelveDTO lubToShelveDTO);

    /**
     * 通过ID修改油品库存关系
     *
     * @param lubToShelveDTO 油品库存关系DTO
     * @return 修改数据的ID
     */
    @ApiOperation(value = "通过ID修改油品库存关系")
    @PutMapping(value = "/updateLubToShelve")
    ResultVO<Integer> updateLubToShelve(@RequestBody LubToShelveDTO lubToShelveDTO);

    /**
     * 查询油品库存关系 通过ID
     *
     * @param id 油品库存关系DTO
     * @return 关系数据
     */
    @ApiOperation(value = "查询油品库存关系 通过ID")
    @GetMapping(value = "/getLubToShelveById/{id}")
    ResultVO<LubToShelveDTO> getLubToShelveById(@PathVariable Integer id);

    /**
     * 删除关系表通过ID
     *
     * @param id 关系id
     * @return void
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteLubToShelveById/{id}")
    ResultVO deleteLubToShelveById(@PathVariable Integer id);

    /**
     * 查询油品总数量 通过油品ID
     *
     * @param lubId 油品ID
     * @return 总数量
     */
    @ApiOperation(value = "查询油品总数量 通过油品ID")
    @PostMapping(value = "/getSumLubNumByLubId/{lubId}")
    ResultVO<BigDecimal> getSumLubNumByLubId(@PathVariable Integer lubId);

    /**
     * 通过油品ID 查询所在库存ID
     *
     * @param lubId 油品ID
     * @return 库存Id 集合
     */
    @ApiOperation(value = "通过油品ID 查询所在库存ID ")
    @PostMapping(value = "/getShelveIdByLubId/{lubId}")
    ResultVO<Set<Integer>> getShelveIdByLubId(@PathVariable Integer lubId);

    /**
     * 通过库存Id 查询当前库存的油品ID
     *
     * @param shelveId 库存ID
     * @return 油品Id 集合
     */
    @ApiOperation(value = "通过库存Id 查询当前库存的油品ID ")
    @PostMapping(value = "/getLubIdByShelveId/{shelveId}")
    ResultVO<Set<Integer>> getLubIdByShelveId(@PathVariable Integer shelveId);

    /**
     * 通过库房ID查询当前库房下油品的总数
     *
     * @param shelveId 库房id
     * @return 当前库房下油品的总数
     */
    @ApiOperation(value = "通过库房ID查询当前库房下油品的总数")
    @GetMapping(value = "/getLubSumByShelveId/{shelveId}")
    ResultVO<BigDecimal> getLubSumByShelveId(@PathVariable Integer shelveId);

    /**
     * 查询全部 通过 油品编码 油品名称 库房编码 库房名称 批次号
     *
     * @param lubToShelveQueryDTO 关系表分页
     * @return 全部
     */
    @Deprecated
    @ApiOperation(value = "查询全部 通过 油品编码 油品名称 库房编码 库房名称 批次号")
    @PostMapping(value = "/findAllByLubCodeAndLubNameAndShelveCodeAndShelveNameAndBatchNumber")
    ResultVO<HvPage<LubToShelveQueryDTO>> findAllByLubCodeAndLubNameAndShelveCodeAndShelveNameAndBatchNumber(@RequestBody LubToShelveQueryDTO lubToShelveQueryDTO);

    /**
     * 查询全部 通过 油品编码 油品名称 库房编码 库房名称 批次号
     *
     * @param lubToShelveQueryDTO 关系表分页
     * @return 全部
     */
    @ApiOperation(value = "查询全部 通过 油品名称 库房名称 批次号")
    @PostMapping(value = "/getLubToShelve")
    ResultVO<HvPage<LubToShelveDTO>> getLubToShelve(@RequestBody LubToShelveQueryDTO lubToShelveQueryDTO);

    /**
     * 通过 油品ID和库房ID查询批次号列表
     *
     * @param lubId    油品ID
     * @param shelveId 库房ID
     * @return 批次号集合
     */
    @ApiOperation(value = "通过 库房ID和库房ID查询批次号列表")
    @GetMapping(value = "/getBatchNumberListByLubIdAndShelveId/{lubId}/{shelveId}")
    ResultVO<List<String>> getBatchNumberListBySpareIdAndShelveId(@PathVariable Integer lubId, @PathVariable Integer shelveId);
}
