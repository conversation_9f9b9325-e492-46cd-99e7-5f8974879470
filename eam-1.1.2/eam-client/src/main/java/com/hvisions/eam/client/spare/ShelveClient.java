package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.publicstore.ShelveDTO;
import com.hvisions.eam.query.spare.ShelveQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title:ShelveClient</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/shelve", fallbackFactory = ShelveClientFallBack.class)
public interface ShelveClient {

    /**
     * 通过ID修改库房信息
     *
     * @param shelveDTO 库房DTO
     * @return 修改数据的ID
     */
    @ApiOperation(value = "通过ID修改库房信息")
    @PutMapping(value = "/updateShelveById")
    ResultVO<Integer> updateShelveById(@RequestBody ShelveDTO shelveDTO);

    /**
     * 增加库位
     *
     * @param shelveDTO 库位DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加库位")
    @PostMapping(value = "/createShelve")
    ResultVO<Integer> createShelve(@RequestBody ShelveDTO shelveDTO);

    /**
     * 通过ID查询库房信息
     *
     * @param id 备件DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "通过ID查询库房信息 ")
    @GetMapping(value = "/getShelveById/{id}")
    ResultVO<ShelveDTO> getShelveById(@PathVariable Integer id);

    /**
     * 删除库房
     * 当库位中有备件时不能删除
     *
     * @param id ID
     * @return void
     */
    @ApiOperation(value = "通过ID删除库房")
    @DeleteMapping(value = "deleteShelveById/{id}")
    ResultVO deleteShelveById(@PathVariable Integer id);

    /**
     * 分页查询 通过库位编码 库位名称 库位地点 联合查询
     *
     * @param shelveQueryDTO 分页查询信息
     * @return 分页
     */
    @ApiOperation(value = "分页查询 库位 通过库位编码 库位名称 库位地点")
    @PostMapping(value = "getShelveByShelveCodeAndShelveNameAndShelvePace")
    ResultVO<HvPage<ShelveDTO>> getShelveByShelveCodeAndShelveNameAndShelvePace(@RequestBody ShelveQueryDTO shelveQueryDTO);
}
