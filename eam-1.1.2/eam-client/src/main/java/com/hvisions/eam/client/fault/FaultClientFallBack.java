package com.hvisions.eam.client.fault;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.fault.FaultDTO;
import com.hvisions.eam.dto.fault.FaultQueryDTO;

/**
 * <p>Title:FaultClientFallBack</p>
 * <p>Description:设备故障</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public class FaultClientFallBack extends BaseFallbackFactory<FaultClient> {
    @Override
    public FaultClient getFallBack(ResultVO vo) {
        return new FaultClient() {
            @Override
            public ResultVO<Integer> addFault(FaultDTO faultDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteFault(Integer id) {
                return vo;
            }

            @Override
            public ResultVO updateFault(FaultDTO emFaultDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<FaultDTO>> listAllFaultByClassId(FaultQueryDTO faultQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<FaultDTO> getFaultById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteFaultCode(String faultCode) {
                return vo;
            }

            @Override
            public ResultVO<FaultDTO> getFaultByCode(String faultCode) {
                return vo;
            }

        };
    }
}
