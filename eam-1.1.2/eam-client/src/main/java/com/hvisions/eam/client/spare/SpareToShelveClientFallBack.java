package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareToShelveDTO;
import com.hvisions.eam.query.spare.SpareToShelveQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * <p>Title:SpareToShelveClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class SpareToShelveClientFallBack extends BaseFallbackFactory<SpareToShelveClient> {
    @Override
    public SpareToShelveClient getFallBack(ResultVO vo) {
        return new SpareToShelveClient() {
            @Override
            public ResultVO<Integer> createSpareToShelve(SpareToShelveDTO spareToShelveDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateSpareToShelve(SpareToShelveDTO spareToShelveDTO) {
                return vo;
            }

            @Override
            public ResultVO<SpareToShelveDTO> getSpareToShelveById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteSpareToShelveById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<Integer> getSumSpareNumBySpareId(Integer spareId) {
                return vo;
            }

            @Override
            public ResultVO<Set<Integer>> getShelveIdBySpareId(Integer spareId) {
                return vo;
            }

            @Override
            public ResultVO<Set<Integer>> getSpareIdByShelveId(Integer shelveId) {
                return vo;
            }

            @Override
            public ResultVO<Integer> getSpareSumByShelveId(Integer shelveId) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<SpareToShelveQueryDTO>> findAllBySpareCodeAndSpareNameAndShelveCodeAndShelveNameAndBatchNumber(SpareToShelveQueryDTO spareToShelveQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<SpareToShelveDTO>> getSpareToShelve(SpareToShelveQueryDTO spareToShelveQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<String>> getBatchNumberListBySpareIdAndShelveId(Integer spareId, Integer shelveId) {
                return vo;
            }
        };
    }
}
