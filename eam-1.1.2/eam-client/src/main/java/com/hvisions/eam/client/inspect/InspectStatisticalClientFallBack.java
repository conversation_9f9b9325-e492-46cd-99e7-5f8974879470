package com.hvisions.eam.client.inspect;

import com.hvisions.activiti.dto.history.HistoricTaskInstanceDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.statistical.InspectHistoryQueryDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalQueryDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalTimeQuantumDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: InspectStatisticalClientFallBack</p >
 * <p>Description: InspectStatisticalClientFallBack</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/24</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class InspectStatisticalClientFallBack extends BaseFallbackFactory<InspectStatisticalClient> {
    @Override
    public InspectStatisticalClient getFallBack(ResultVO vo) {
        return new InspectStatisticalClient() {
            @Override
            public ResultVO completeTaskAndStatistical(String taskId) {
                return vo;
            }

            @Override
            public ResultVO<List<InspectStatisticalDTO>> getStatistical(InspectStatisticalQueryDTO inspectStatisticalQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> getTodayInspectCount() {
                return vo;
            }

            @Override
            public ResultVO<Integer> getWeekCount() {
                return vo;
            }

            @Override
            public ResultVO<List<InspectStatisticalTimeQuantumDTO>> getCountByTimeQuantum(InspectStatisticalQueryDTO inspectStatisticalQueryDTO) {
                return vo;
            }


            @Override
            public ResultVO<List<HistoricTaskInstanceDTO>> getHistoryByEquipmentId(InspectHistoryQueryDTO inspectHistoryQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<ProcessInstanceDTO>> getOngoingByEquipmentId(InspectHistoryQueryDTO inspectHistoryQueryDTO) {
                return vo;
            }
        };
    }
}