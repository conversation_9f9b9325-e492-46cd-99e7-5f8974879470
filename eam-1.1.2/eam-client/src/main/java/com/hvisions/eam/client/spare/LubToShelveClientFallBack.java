package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.lub.LubToShelveDTO;
import com.hvisions.eam.query.lub.LubToShelveQueryDTO;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>Title:LubToShelveClientFallBack</p>
 * <p>Description:油品库房</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class LubToShelveClientFallBack extends BaseFallbackFactory<LubToShelveClient> {
    @Override
    public LubToShelveClient getFallBack(ResultVO vo) {
        return new LubToShelveClient() {
            @Override
            public ResultVO<Integer> createLubToShelve(LubToShelveDTO lubToShelveDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateLubToShelve(LubToShelveDTO lubToShelveDTO) {
                return vo;
            }

            @Override
            public ResultVO<LubToShelveDTO> getLubToShelveById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteLubToShelveById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<BigDecimal> getSumLubNumByLubId(Integer lubId) {
                return vo;
            }

            @Override
            public ResultVO<Set<Integer>> getShelveIdByLubId(Integer lubId) {
                return vo;
            }

            @Override
            public ResultVO<Set<Integer>> getLubIdByShelveId(Integer shelveId) {
                return vo;
            }

            @Override
            public ResultVO<BigDecimal> getLubSumByShelveId(Integer shelveId) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<LubToShelveQueryDTO>> findAllByLubCodeAndLubNameAndShelveCodeAndShelveNameAndBatchNumber(LubToShelveQueryDTO lubToShelveQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<LubToShelveDTO>> getLubToShelve(LubToShelveQueryDTO lubToShelveQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<String>> getBatchNumberListBySpareIdAndShelveId(Integer lubId, Integer shelveId) {
                return vo;
            }
        };
    }
}
