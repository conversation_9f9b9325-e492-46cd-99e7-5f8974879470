package com.hvisions.eam.client.spare;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.RootLubDTO;
import com.hvisions.eam.activiti.sapre.RootSpareDTO;
import com.hvisions.eam.dto.publicstore.ActualUseDTO;
import com.hvisions.eam.dto.publicstore.UseListingSpareDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:StoreClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/1/1</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class StoreClientFallBack implements StoreClient {

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<String> getSerialNumber(String service) {
        return new ResultVO<>();
    }

    /**
     * 批量增加 使用清单
     *
     * @param useListingSpareDTOS 清单List
     * @return id
     */
    @Override
    public ResultVO<List<Integer>> createList(List<UseListingSpareDTO> useListingSpareDTOS) {
        return new ResultVO<>();
    }

    /**
     * 生成唯一不重复的单号 生成规则  年 月 日 时 分 秒  随机数（四位）共计18位
     *
     * @return 唯一单号
     */
    @Override
    public ResultVO<String> getGodownReceiptNumber() {
        return new ResultVO<>();
    }

    /**
     * 申请备件
     *
     * @param rootSpareDTO 申请单
     * @return 申请单
     */
    @Override
    public ResultVO<RootSpareDTO> applySpare(RootSpareDTO rootSpareDTO) {
        return new ResultVO<>();
    }

    /**
     * 申请油品
     *
     * @param rootLubDTO 申请单
     * @return 申请单
     */
    @Override
    public ResultVO<RootLubDTO> applyLub(RootLubDTO rootLubDTO) {
        return new ResultVO<>();
    }

    /**
     * 通过processInstanceIds 获取实际使用获取油品申请通过的申请项数据
     *
     * @param processInstanceIds 流程实例Id
     * @return 申请项 list
     */
    @Override
    public ResultVO<List<ActualUseDTO>> getActualUseByProcessInstanceIds(List<String> processInstanceIds) {
        return new ResultVO<>();
    }


}
