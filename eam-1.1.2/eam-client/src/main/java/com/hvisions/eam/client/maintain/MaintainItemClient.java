package com.hvisions.eam.client.maintain;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.maintain.MaintainItemDTO;
import com.hvisions.eam.dto.maintain.MaintainItemQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: DemoClient</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/09</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "eam", path = "/maintainItem", fallbackFactory = MaintainItemClientFallBack.class)
public interface MaintainItemClient {

    /**
     * 新增保养项目
     *
     * @param maintainItemCreateOrUpdateDTO 保养项目DTO
     * @return 新增保养项目id
     */
    @ApiOperation(value = "新增保养项目信息")
    @PostMapping(value = "/createMaintainItem")
    ResultVO<Integer> createMaintainItem(@RequestBody MaintainItemDTO maintainItemCreateOrUpdateDTO);

    /**
     * 编辑保养项目
     *
     * @param maintainItemCreateOrUpdateDTO 保养项目DTO
     * @return 编辑保养项目id
     */
    @ApiOperation(value = "更新保养项目信息")
    @PutMapping(value = "/updateMaintainItem")
    ResultVO<Integer> updateMaintainItem(@RequestBody MaintainItemDTO maintainItemCreateOrUpdateDTO);

    /**
     * 删除单个保养项目
     *
     * @param id 要删除的保养项目的id
     * @return vo
     */
    @ApiOperation(value = "删除保养项目信息")
    @DeleteMapping(value = "/deleteMaintainItemInfoById/{id}")
    ResultVO deleteMaintainItemInfoById(@PathVariable Integer id);

    /**
     * 批量删除保养项目
     *
     * @param idList id集合
     * @return vo
     */
    @ApiOperation(value = "批量删除保养项目")
    @DeleteMapping(value = "/deleteMaintainItemInfoByIdList")
    ResultVO deleteMaintainItemInfoByIdList(@RequestBody List<Integer> idList);

    /**
     * 根据设备型号或保养部位查询保养项目信息，以及关联的备件，油品信息（模糊分页）
     *
     * @param maintainItemQueryDTO 分页查询条件
     * @return 保养项目DTO
     */
    @ApiOperation(value = "根据设备型号和保养部位，是否启用查询保养项目信息，以及关联的备件，油品信息（模糊分页）")
    @PostMapping(value = "/getItemByEquipmentModelIdAndPosition")
    ResultVO<HvPage<MaintainItemDTO>> getMaintainItemByEquipmentModelIdAndMaintainPosition(@RequestBody MaintainItemQueryDTO maintainItemQueryDTO);

    /**
     * 根据保养内容id获取保养项目
     *
     * @param maintainContentId 保养内容id
     * @return 保养项目分页数据
     */
    @ApiOperation(value = "根据保养内容id获取保养项目，以及保养项目关联的备件油品")
    @GetMapping(value = "/getMaintainItemInfoByMaintainContentId/{maintainContentId}")
    ResultVO<List<MaintainItemDTO>> getMaintainItemInfoByMaintainContentId(@PathVariable Integer maintainContentId);

    /**
     * 是否启用保养项目
     *
     * @param id 要启用或停止的保养项目id
     * @return 返回被操作的id
     */
    @ApiOperation(value = "是否启用保养项目")
    @PutMapping(value = "/startUsingItemById/{id}")
    ResultVO<Integer> startUsingItemById(@PathVariable Integer id);

    /**
     * 根据id获取保养项目详情
     *
     * @param id 保养项目id
     * @return 保养项目信息
     */
    @ApiOperation(value = "根据id获取保养项目信息")
    @GetMapping(value = "/getById/{id}")
    ResultVO<MaintainItemDTO> getById(@PathVariable Integer id);

    /**
     * 根据编码获取保养项目详情
     *
     * @param itemCode 保养项目编码
     * @return 保养项目信息
     */
    @ApiOperation(value = "根据编码获取保养项目信息")
    @GetMapping(value = "/getByCode")
    ResultVO<MaintainItemDTO> getByCode(@RequestParam String itemCode);
}
