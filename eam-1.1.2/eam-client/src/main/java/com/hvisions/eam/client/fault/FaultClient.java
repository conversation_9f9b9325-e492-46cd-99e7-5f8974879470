package com.hvisions.eam.client.fault;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.fault.FaultDTO;
import com.hvisions.eam.dto.fault.FaultQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title:HvEmFaultClient</p>
 * <p>Description:设备故障</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/EmFault", fallbackFactory = FaultClientFallBack.class)
public interface FaultClient {

    /**
     * 添加故障
     *
     * @param faultDTO 设备故障DTO
     * @return 新增故障id
     */
    @ApiOperation(value = "新增故障")
    @RequestMapping(value = "/addEmFault", method = RequestMethod.POST)
    ResultVO<Integer> addFault(@RequestBody FaultDTO faultDTO);


    /**
     * 删除故障
     *
     * @param id 故障id
     * @return ResultVO
     */
    @ApiOperation(value = "删除故障")
    @RequestMapping(value = "/deleteFault/{id}", method = RequestMethod.DELETE)
    ResultVO deleteFault(@PathVariable Integer id);


    /**
     * 更新故障
     *
     * @param emFaultDTO 故障DTO
     * @return ResultVO
     */
    @ApiOperation(value = "更新故障")
    @RequestMapping(value = "updateFault", method = RequestMethod.PUT)
    ResultVO updateFault(@RequestBody FaultDTO emFaultDTO);


    /**
     * 查询一类故障 表现
     *
     * @param faultQueryDTO 故障类查询故障DTO
     * @return 一类故障集合
     */
    @ApiOperation("查询一类故障")
    @PostMapping(value = "getFaultByFaultClass")
    ResultVO<HvPage<FaultDTO>> listAllFaultByClassId(@RequestBody FaultQueryDTO faultQueryDTO) ;


    /**
     * 根据id查询故障
     *
     * @param id 故障id
     * @return 故障
     */
    @ApiOperation(value = "根据id查询故障")
    @GetMapping(value = "getFaultById/{id}")
    ResultVO<FaultDTO> getFaultById(@PathVariable Integer id);


    /**
     * 通过code删除故障
     * @param faultCode 故障编码
     * @return ResultVO
     */
    @ApiOperation("通过code删除故障")
    @DeleteMapping(value = "/deleteFaultCode/{faultCode}")
    ResultVO deleteFaultCode(@PathVariable String faultCode);


    /**
     * 通过编码查询故障
     *
     * @param faultCode 故障编码
     * @return 故障
     */
    @ApiOperation(value = "通过code编码查询故障")
    @GetMapping(value = "/getFaultByCode/{faultCode}")
    ResultVO<FaultDTO> getFaultByCode(@PathVariable  String faultCode);

}
