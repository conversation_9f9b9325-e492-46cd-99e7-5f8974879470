package com.hvisions.eam.client.fault;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.fault.FaultSolutionDTO;
import com.hvisions.eam.dto.fault.FaultSolutionQueryDTO;

/**
 * <p>Title:FaultSolutionClientFallBack</p>
 * <p>Description:故障解决方案</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public class FaultSolutionClientFallBack extends BaseFallbackFactory<FaultSolutionClient> {
    @Override
    public FaultSolutionClient getFallBack(ResultVO vo) {
        return new FaultSolutionClient() {
            @Override
            public ResultVO<Integer> addEmFaultSolution(FaultSolutionDTO faultSolutionDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteEmFaultSolution(Integer id) {
                return vo;
            }

            @Override
            public ResultVO updateEmFaultSolution(FaultSolutionDTO faultSolutionDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<FaultSolutionDTO>> getSolutionsByFaultId(FaultSolutionQueryDTO faultSolutionQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<FaultSolutionDTO> getFaultSolutionById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<FaultSolutionDTO> getFaultSolutionBySolutionCode(String solutionCode) {
                return vo;
            }

            @Override
            public ResultVO deleteEmFaultSolutionBySolutionCode(String solutionCode) {
                return vo;
            }
        };
    }
}
