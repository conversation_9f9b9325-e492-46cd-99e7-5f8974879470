package com.hvisions.eam.client.fault;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.fault.FaultReasonDTO;
import com.hvisions.eam.dto.fault.FaultReasonQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title:HvEmFaultReasonClient</p>
 * <p>Description:设备故障原因</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/EmFaultReason", fallbackFactory = FaultReasonClientFallBack.class)
public interface FaultReasonClient {

    /**
     * 新增故障原因
     *
     * @param faultReasonDTO 故障原因DTO
     * @return 故障原因id
     */
    @ApiOperation(value = "新增故障原因")
    @PostMapping(value = "/add")
    ResultVO<Integer> addFaultReason(@RequestBody FaultReasonDTO faultReasonDTO);

    /**
     * 删除故障原因
     *
     * @param id 故障原因id
     * @return ResultVO
     */
    @ApiOperation(value = "删除故障原因")
    @DeleteMapping(value = "/delete/{id}")
    ResultVO deleteFaultReason(@PathVariable Integer id);

    /**
     * 更新故障原因
     *
     * @param faultReasonDTO 故障原因DTO
     * @return ResultVO
     */
    @ApiOperation(value = "更新故障原因")
    @PutMapping(value = "/update")
    ResultVO updateFaultReason(@RequestBody FaultReasonDTO faultReasonDTO);

    /**
     * 查询故障原因 表现找原因
     *
     * @param faultReasonQueryDTO 故障查询故障原因DTO
     * @return 故障原因集合
     */
    @ApiOperation(value = "查询故障原因")
    @PostMapping(value = "/getFaultReason")
    ResultVO<HvPage<FaultReasonDTO>> getFaultReason(@RequestBody FaultReasonQueryDTO faultReasonQueryDTO);

    /**
     * 根据id查询故障原因
     *
     * @param id 故障原因id
     * @return 故障原因
     */
    @ApiOperation(value = "根据id查询故障原因")
    @GetMapping(value = "getFaultReasonById/{id}")
    ResultVO<FaultReasonDTO> getFaultReasonById(@PathVariable Integer id);


    /**
     * 通过故障编码删除故障原因
     *
     * @param reasonCode 故障编码
     * @return ResultVO
     */
    @ApiOperation(value = "通过故障编码删除故障原因")
    @DeleteMapping(value = "/deleteFaultReasonCode/{reasonCode}")
    ResultVO deleteFaultReasonCode(@PathVariable String reasonCode);


    /**
     * 通过故障原因编码获取故障原因
     *
     * @param faultReasonCode 故障原因编码
     * @return 故障原因
     */
    @ApiOperation(value = "通过编码获取故障原因")
    @GetMapping(value = "/getFaultReasonByCode/{faultReasonCode}")
    ResultVO<FaultReasonDTO> getFaultReasonByCode(@PathVariable String faultReasonCode);


}
