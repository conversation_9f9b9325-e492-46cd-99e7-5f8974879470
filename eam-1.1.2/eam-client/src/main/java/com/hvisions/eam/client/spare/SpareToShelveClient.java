package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareToShelveDTO;
import com.hvisions.eam.query.spare.SpareToShelveQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <p>Title:SpareToShelveClient</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/spareToShelve", fallbackFactory = SpareToShelveClientFallBack.class)
public interface SpareToShelveClient {

    /**
     * 增加备件库存关系
     *
     * @param spareToShelveDTO 备件库存关系DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加备件库存关系")
    @PostMapping(value = "/createSpareToShelve")
    ResultVO<Integer> createSpareToShelve(@RequestBody SpareToShelveDTO spareToShelveDTO);

    /**
     * 通过ID修改备件库存关系
     *
     * @param spareToShelveDTO 备件库存关系DTO
     * @return 修改数据的ID
     */
    @ApiOperation(value = "通过ID修改备件库存关系")
    @PutMapping(value = "/updateSpareToShelve")
    ResultVO<Integer> updateSpareToShelve(@RequestBody SpareToShelveDTO spareToShelveDTO);

    /**
     * 查询备件库存关系 通过ID
     *
     * @param id 备件库存关系DTO
     * @return 关系数据
     */
    @ApiOperation(value = "查询备件库存关系 通过ID")
    @GetMapping(value = "/getSpareToShelveById/{id}")
    ResultVO<SpareToShelveDTO> getSpareToShelveById(@PathVariable Integer id);


    /**
     * 删除关系表通过ID
     *
     * @param id 关系ID
     * @return void
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteSpareToShelveById/{id}")
    ResultVO deleteSpareToShelveById(@PathVariable Integer id);

    /**
     * 查询备件总数量 通过备件ID
     *
     * @param spareId 备件ID
     * @return 总数量
     */
    @ApiOperation(value = "查询备件总数量 通过备件ID")
    @PostMapping(value = "/getSumSpareNumBySpareId/{spareId}")
    ResultVO<Integer> getSumSpareNumBySpareId(@PathVariable Integer spareId);

    /**
     * 通过备件ID 查询所在库存ID
     *
     * @param spareId 备件ID
     * @return 库存Id 集合
     */
    @ApiOperation(value = "通过备件ID 查询所在库存ID ")
    @PostMapping(value = "/getShelveIdBySpareId/{spareId}")
    ResultVO<Set<Integer>> getShelveIdBySpareId(@PathVariable Integer spareId);

    /**
     * 通过库存Id 查询当前库存的备件ID
     *
     * @param shelveId 库存ID
     * @return 备件Id 集合
     */
    @ApiOperation(value = "通过库存Id 查询当前库存的备件ID")
    @PostMapping(value = "/getSpareIdByShelveId/{shelveId}")
    ResultVO<Set<Integer>> getSpareIdByShelveId(@PathVariable Integer shelveId);


    /**
     * 通过库房ID查询当前库房下备件的总数
     *
     * @param shelveId 库房ID
     * @return 当前库房下备件的总数
     */
    @ApiOperation(value = "通过库房ID查询当前库房下备件的总数")
    @GetMapping(value = "/getSpareSumByShelveId/{shelveId}")
    ResultVO<Integer> getSpareSumByShelveId(@PathVariable Integer shelveId);

    /**
     * 查询全部 通过 备件编码 备件名称 库房编码 库房名称 批次号 备件类型名称
     *
     * @param spareToShelveQueryDTO 关系表分页
     * @return 全部
     */
    @Deprecated
    @ApiOperation(value = "查询全部 通过 备件编码 备件名称 库房编码 库房名称 批次号 备件类型名称")
    @PostMapping(value = "/findAllBySpareCodeAndSpareNameAndShelveCodeAndShelveNameAndBatchNumber")
    ResultVO<HvPage<SpareToShelveQueryDTO>> findAllBySpareCodeAndSpareNameAndShelveCodeAndShelveNameAndBatchNumber(
            @RequestBody SpareToShelveQueryDTO spareToShelveQueryDTO);

    /**
     * 库房查询 通过备件名称 批次号 库房名称 备件类型code 查询
     *
     * @param spareToShelveQueryDTO 关系表分页
     * @return 全部
     */
    @ApiOperation(value = "库房查询 通过备件名称 批次号 库房名称 备件类型code 查询")
    @PostMapping(value = "/getSpareToShelve")
    ResultVO<HvPage<SpareToShelveDTO>> getSpareToShelve(@RequestBody SpareToShelveQueryDTO spareToShelveQueryDTO);

    /**
     * 通过 备件ID和库房ID查询批次号列表
     *
     * @param spareId  备件ID
     * @param shelveId 库房ID
     * @return 批次号集合
     */
    @ApiOperation(value = "通过 备件ID和库房ID查询批次号列表")
    @GetMapping(value = "/getBatchNumberListBySpareIdAndShelveId/{spareId}/{shelveId}")
    ResultVO<List<String>> getBatchNumberListBySpareIdAndShelveId(@PathVariable Integer spareId, @PathVariable Integer shelveId);


}
