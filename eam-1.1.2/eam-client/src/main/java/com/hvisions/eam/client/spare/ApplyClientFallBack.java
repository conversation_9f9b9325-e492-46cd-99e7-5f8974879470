package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.RootLubDTO;
import com.hvisions.eam.activiti.sapre.RootSpareDTO;
import com.hvisions.eam.dto.publicstore.HeaderDTO;
import com.hvisions.eam.dto.publicstore.LineDTO;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title:ApplyClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class ApplyClientFallBack extends BaseFallbackFactory<ApplyClient> {
    @Override
    public ApplyClient getFallBack(ResultVO vo) {
        return new ApplyClient() {
            @Override
            public ResultVO<RootSpareDTO> applySpare(@Valid RootSpareDTO rootSpareDTO) {
                return vo;
            }

            @Override
            public ResultVO<RootLubDTO> applyLub(@Valid RootLubDTO rootLubDTO) {
                return vo;
            }

            @Override
            public ResultVO spareAndLubIsThrough(String taskId, Integer isPass) {
                return vo;
            }

            @Override
            public ResultVO spareIsThrough(String taskId, Integer isPass) {
                return vo;
            }

            @Override
            public ResultVO lubIsThrough(String taskId, Integer isPass) {
                return vo;
            }

            @Override
            public ResultVO<Integer> spareOut(String processInstanceID, Boolean isPass) {
                return vo;
            }

            @Override
            public ResultVO<Integer> lubOut(String processInstanceID, Boolean isPass) {
                return vo;
            }

            @Override
            public ResultVO deliveryOfCargoFromStorage(Integer headerId) {
                return vo;
            }

            @Override
            public ResultVO<String> getSerialNumber(String service) {
                return vo;
            }

            @Override
            public ResultVO<String> getGodownReceiptNumber() {
                return vo;
            }

            @Override
            public ResultVO<HvPage<HeaderDTO>> getHeader(HeaderDTO headerDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateHeaderLine(LineDTO lineDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> addHeaderLine(LineDTO lineDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteHeaderLine(Integer lineDTOId) {
                return vo;
            }

            @Override
            public ResultVO<List<String>> getAllSource() {
                return vo;
            }

        };
    }
}
