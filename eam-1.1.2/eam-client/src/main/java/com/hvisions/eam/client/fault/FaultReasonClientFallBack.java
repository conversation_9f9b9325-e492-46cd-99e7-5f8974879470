package com.hvisions.eam.client.fault;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.fault.FaultReasonDTO;
import com.hvisions.eam.dto.fault.FaultReasonQueryDTO;

/**
 * <p>Title:FaultReasonClientFallBack</p>
 * <p>Description:故障原因</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public class FaultReasonClientFallBack extends BaseFallbackFactory<FaultReasonClient> {
    @Override
    public FaultReasonClient getFallBack(ResultVO vo) {
        return new FaultReasonClient() {
            @Override
            public ResultVO<Integer> addFaultReason(FaultReasonDTO faultReasonDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteFaultReason(Integer id) {
                return vo;
            }

            @Override
            public ResultVO updateFaultReason(FaultReasonDTO faultReasonDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<FaultReasonDTO>> getFaultReason(FaultReasonQueryDTO faultReasonQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<FaultReasonDTO> getFaultReasonById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteFaultReasonCode(String reasonCode) {
                return vo;
            }

            @Override
            public ResultVO<FaultReasonDTO> getFaultReasonByCode(String faultReasonCode) {
                return vo;
            }
        };
    }
}
