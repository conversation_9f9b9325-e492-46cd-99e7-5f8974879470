package com.hvisions.eam.client.fault;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.fault.RootEquipmentFaultDTO;

import javax.validation.Valid;

/**
 * <p>Title:MaintenanceReportClientFallBack</p>
 * <p>Description:设备保修</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public class MaintenanceReportClientFallBack extends BaseFallbackFactory<MaintenanceReportClient> {
    @Override
    public MaintenanceReportClient getFallBack(ResultVO vo) {
        return new MaintenanceReportClient(){
            @Override
            public ResultVO<RootEquipmentFaultDTO> applyEquipmentFault(@Valid RootEquipmentFaultDTO rootEquipmentFaultDTO) {
                return vo;
            }
        };
    }
}
