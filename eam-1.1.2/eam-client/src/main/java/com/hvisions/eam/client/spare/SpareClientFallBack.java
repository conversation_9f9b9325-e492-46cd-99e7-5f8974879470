package com.hvisions.eam.client.spare;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:SpareClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/4/15</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class SpareClientFallBack implements SpareClient {

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<SpareDTO> getSpareById(Integer spareId) {
        return new ResultVO<>();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<List<SpareDTO>> getSpareListByIdList(List<Integer> ids) {
        return new ResultVO<>();
    }

    /**
     * 通过备件编码code 查询备件
     *
     * @param spareCode 编码
     * @return 备件
     */
    @Override
    public ResultVO<SpareDTO> getSpareBySpareCode(String spareCode) {
        return new ResultVO<>();
    }

    /**
     * 通过备件编码code 查询备件
     *
     * @param spareCodeList 编码列表
     * @return 备件
     */
    @Override
    public ResultVO<List<SpareDTO>> getSpareBySpareCodeList(List<String> spareCodeList) {
        return new ResultVO<>();
    }
}
