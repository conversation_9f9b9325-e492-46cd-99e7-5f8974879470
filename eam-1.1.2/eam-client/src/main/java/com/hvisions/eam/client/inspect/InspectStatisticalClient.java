package com.hvisions.eam.client.inspect;

import com.hvisions.activiti.dto.history.HistoricTaskInstanceDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.statistical.InspectHistoryQueryDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalQueryDTO;
import com.hvisions.eam.dto.inspect.statistical.InspectStatisticalTimeQuantumDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: InspectStatisticalClient</p >
 * <p>Description: InspectStatisticalClient</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/24</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "eam", path = "/inspectStatistical", fallbackFactory = InspectStatisticalClientFallBack.class)
public interface InspectStatisticalClient {

    /**
     * 点巡检任务完成
     *
     * @param taskId 任务id
     * @return vo
     */
    @ApiOperation(value = "点巡检任务完成")
    @PutMapping(value = "/completeTaskAndStatistical/{taskId}")
    ResultVO completeTaskAndStatistical(@PathVariable String taskId);

    /**
     * 查询统计信息
     *
     * @param inspectStatisticalQueryDTO 查询条件
     * @return 统计信息
     */
    @ApiOperation(value = "查询统计信息")
    @PostMapping(value = "/getStatistical")
    ResultVO<List<InspectStatisticalDTO>> getStatistical(@RequestBody InspectStatisticalQueryDTO inspectStatisticalQueryDTO);

    /**
     * 今日点巡检次数
     *
     * @return 今日润滑次数
     */
    @ApiOperation(value = "今日点巡检次数")
    @GetMapping(value = "/getTodayInspectCount")
    ResultVO<Integer> getTodayInspectCount();

    /**
     * 本周点巡检次数
     *
     * @return 次数
     */
    @ApiOperation(value = "本周点巡检次数")
    @GetMapping(value = "/getWeekCount")
    ResultVO<Integer> getWeekCount();

    /**
     * 根据时间段查询点检次数
     *
     * @param inspectStatisticalQueryDTO 查询条件
     * @return list
     */
    @ApiOperation(value = "根据时间段查询点检次数")
    @PostMapping(value = "/getCountByTimeQuantum")
    ResultVO<List<InspectStatisticalTimeQuantumDTO>> getCountByTimeQuantum(@RequestBody InspectStatisticalQueryDTO inspectStatisticalQueryDTO);


    /**
     * 根据设备id查已完成的历史任务
     *
     * @param inspectHistoryQueryDTO 查询条件
     * @return 历史
     */
    @ApiOperation(value = "根据设备id查已完成的历史任务 注：此方法不可用businessKey为查询条件")
    @PostMapping(value = "/getHistoryByEquipmentId")
    ResultVO<List<HistoricTaskInstanceDTO>> getHistoryByEquipmentId(@RequestBody InspectHistoryQueryDTO inspectHistoryQueryDTO);

    /**
     * 根据设备id查正在进行的任务
     *
     * @param inspectHistoryQueryDTO 查询条件
     * @return 分页
     */
    @ApiOperation(value = "根据设备id查正在进行的任务")
    @PostMapping(value = "/getOngoingByEquipmentId")
    ResultVO<HvPage<ProcessInstanceDTO>> getOngoingByEquipmentId(@RequestBody InspectHistoryQueryDTO inspectHistoryQueryDTO);

}
