package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.lub.LubricatingDTO;
import com.hvisions.eam.query.lub.LubricatingQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title:LubricationClient</p>
 * <p>Description:油品基础</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/lub", fallbackFactory = LubricationClientFallBack.class)
public interface LubricationClient {
    /**
     * 通过批次号 获取油品信息
     *
     * @param batchNumber 批次号
     * @return 备件信息
     */
    @ApiOperation(value = "通过批次号获取油品")
    @GetMapping(value = "/getLubricatingByBatchNumber/{batchNumber}")
    ResultVO<LubricatingDTO> getLubricatingByLubCode(@PathVariable String batchNumber);

    /**
     * 增加油品
     *
     * @param lubricatingDTO 油品DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加油品")
    @PostMapping(value = "/createLub")
    ResultVO<Integer> createLub(@RequestBody LubricatingDTO lubricatingDTO);

    /**
     * 修改油品 通过油品DTO
     *
     * @param lubricatingDTO 油品DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "修改油品")
    @PutMapping(value = "/updateLub")
    ResultVO<Integer> updateLub(@RequestBody LubricatingDTO lubricatingDTO);

    /**
     * 查询油品 通过ID
     *
     * @param id 油品DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "查询油品 通过ID")
    @GetMapping(value = "/getLubById/{id}")
    ResultVO<LubricatingDTO> getLubById(@PathVariable Integer id);

    /**
     * 通过ID删除油品
     *
     * @param id LubId
     * @return void
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteLubById/{id}")
    ResultVO deleteLubById(@PathVariable Integer id);

    /**
     * 分页查询
     * 通过 油品编码 油品名称 小类ID 是否关键部位  联合查询
     *
     * @param lubricatingQueryDTO 油品分页DTO
     * @return 分页信息
     */
    @ApiOperation(value = "分页查询 通过 油品编码 油品名称 类型ID 是否关键部位  联合查询")
    @PostMapping(value = "/getAllByLubCodeAndLubNameAndLubTypeSub")
    ResultVO<HvPage<LubricatingDTO>> getAllByLubCodeAndLubNameAndLubTypeSub(@RequestBody LubricatingQueryDTO lubricatingQueryDTO);

    /**
     * 通过id集合查询油品集合
     *
     * @param ids 油品分页DTO
     * @return 油品集合
     */
    @ApiOperation(value = "通过id集合查询油品集合")
    @PostMapping(value = "/getLubListByIdList")
    ResultVO<List<LubricatingDTO>> getLubListByIdList(@RequestBody List<Integer> ids);

    /**
     * 通过批次号查询备件信息
     *
     * @param batchNumber 批次号
     * @return 备件信息
     */
    @ApiOperation(value = "批次号查询备件")
    @GetMapping(value = "/getSpareDTOByBatchNumber/{batchNumber}")
    ResultVO<LubricatingDTO> getSpareDTOByBatchNumber(@PathVariable String batchNumber);


}
