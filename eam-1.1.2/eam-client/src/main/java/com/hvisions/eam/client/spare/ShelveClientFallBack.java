package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.publicstore.ShelveDTO;
import com.hvisions.eam.query.spare.ShelveQueryDTO;
import org.springframework.stereotype.Component;

/**
 * <p>Title:ShelveClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class ShelveClientFallBack extends BaseFallbackFactory<ShelveClient> {
    @Override
    public ShelveClient getFallBack(ResultVO vo) {
        return new ShelveClient() {
            @Override
            public ResultVO<Integer> updateShelveById(ShelveDTO shelveDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> createShelve(ShelveDTO shelveDTO) {
                return vo;
            }

            @Override
            public ResultVO<ShelveDTO> getShelveById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteShelveById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<ShelveDTO>> getShelveByShelveCodeAndShelveNameAndShelvePace(ShelveQueryDTO shelveQueryDTO) {
                return vo;
            }
        };
    }
}
