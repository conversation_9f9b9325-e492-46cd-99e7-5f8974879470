package com.hvisions.eam.client.autonomy;

import com.hvisions.eam.dto.autonomy.InspectionPlanCreateOrUpdateDTO;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(name = "eam", path = "/inspectionPlan")
public interface InspectionPlanClient {
    @ApiOperation(value = "新增")
    @PostMapping("/add")
    ResultVO<Integer> add(@RequestBody @Valid InspectionPlanCreateOrUpdateDTO dto);
}

