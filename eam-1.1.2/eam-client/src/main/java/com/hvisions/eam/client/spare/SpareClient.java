package com.hvisions.eam.client.spare;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>Title:SpareClient</p>
 * <p>Description:备件接口</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/26</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam",path = "/spare",fallback = SpareClientFallBack.class)
public interface SpareClient {

    /**
     * 通过备件ID获取备件
     *
     * @param spareId 备件id
     * @return 备件信息
     */
    @GetMapping(value = "/getSpareById/{spareId}")
    ResultVO<SpareDTO> getSpareById(@PathVariable Integer spareId);

    /**
     * 通过id集合查询备件集合
     *
     * @param ids 备件分页DTO
     * @return 备件集合
     */
    @PostMapping(value = "/getSpareListByIdList")
    ResultVO<List<SpareDTO>> getSpareListByIdList(@RequestBody List<Integer> ids);

    /**
     * 通过备件编码code 查询备件
     *
     * @param spareCode 编码
     * @return 备件
     */
    @ApiOperation(value = "通过备件编码code 查询备件")
    @PostMapping("/getSpareBySpareCode/{spareCode}")
    ResultVO<SpareDTO> getSpareBySpareCode(@PathVariable String spareCode);

    /**
     * 通过备件编码code 查询备件
     *
     * @param spareCodeList 编码列表
     * @return 备件
     */
    @ApiOperation(value = "通过备件编码code 查询备件")
    @PostMapping("/getSpareBySpareCodeList")
    ResultVO<List<SpareDTO>> getSpareBySpareCodeList(@RequestBody List<String> spareCodeList);
}

