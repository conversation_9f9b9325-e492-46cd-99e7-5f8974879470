package com.hvisions.eam.client.spare;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.publicstore.MatchingDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:MatchingClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class MatchingClientFallBack extends BaseFallbackFactory<MatchingClient> {
    @Override
    public MatchingClient getFallBack(ResultVO vo) {
        return new MatchingClient() {
            @Override
            public ResultVO<Integer> createMatching(MatchingDTO matchingDTO) {
                return vo;
            }

            @Override
            public ResultVO<MatchingDTO> getMatchingByService(String service) {
                return vo;
            }

            @Override
            public ResultVO<List<String>> getServiceName() {
                return vo;
            }

            @Override
            public ResultVO deleteById(Integer id) {
                return vo;
            }
        };
    }
}
