package com.hvisions.eam.client.fault;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.fault.FaultSolutionDTO;
import com.hvisions.eam.dto.fault.FaultSolutionQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <p>Title:HvEmFaultSolutionClient</p>
 * <p>Description:故障解决方案</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/EmFaultSolution", fallbackFactory = FaultSolutionClientFallBack.class)
public interface FaultSolutionClient {

    /**
     * 新增解决方案
     *
     * @param faultSolutionDTO 解决方案DTO
     * @return 新增实体id
     */
    @ApiOperation(value = "新增解决方案")
    @PostMapping(value = "/add")
    ResultVO<Integer> addEmFaultSolution(@RequestBody FaultSolutionDTO faultSolutionDTO);

    /**
     * 根据id删除解决方案
     *
     * @param id 解决方案id
     * @return ResultVO
     */
    @ApiOperation(value = "根据id删除解决方案")
    @DeleteMapping(value = "/delete/{id}")
    ResultVO deleteEmFaultSolution(@PathVariable Integer id);


    /**
     * 更新解决方案
     *
     * @param faultSolutionDTO 解决方案DTO
     * @return ResultVO
     */
    @ApiOperation(value = "更新解决方案")
    @PutMapping(value = "update")
    ResultVO updateEmFaultSolution(@RequestBody FaultSolutionDTO faultSolutionDTO);

    /**
     * 根据故障原因id查询解决方案
     *
     * @param faultSolutionQueryDTO 故障原因查询解决方案DTO
     * @return 解决方案集合
     */
    @ApiOperation(value = "根据故障原因id查询解决方案")
    @PostMapping(value = "getSolutionByFaultId")
    ResultVO<HvPage<FaultSolutionDTO>> getSolutionsByFaultId(@RequestBody FaultSolutionQueryDTO faultSolutionQueryDTO);


    /**
     * 根据id查询故障解决方案
     *
     * @param id 故障解决方案id
     * @return 故障解决方案
     */
    @ApiOperation(value = "根据id查询故障解决方案")
    @GetMapping(value = "getFaultSolutionById/{id}")
    ResultVO<FaultSolutionDTO> getFaultSolutionById(@PathVariable Integer id);

    /**
     * 通过解决方案编码获取解决方案
     *
     * @param solutionCode 解决方案名称
     * @return 解决方案
     */
    @ApiOperation(value = "通过解决方案编码获取解决方案")
    @GetMapping(value = "/getFaultSolutionBySolutionCode/{solutionCode}")
    ResultVO<FaultSolutionDTO> getFaultSolutionBySolutionCode(@PathVariable String solutionCode);

    /**
     * 通过解决方案编码获取解决方案
     *
     * @param solutionCode 解决方案编码
     * @return ResultVO
     */
    @ApiOperation(value = "通过解决方案编码获取解决方案")
    @DeleteMapping(value = "/deleteEmFaultSolutionBySolutionCode/{solutionCode}")
    ResultVO deleteEmFaultSolutionBySolutionCode(@PathVariable String solutionCode);

}
