package com.hvisions.eam.client.fault;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.fault.FaultClassDTO;

import java.util.List;

/**
 * <p>Title:FaultClassClientFallBack</p>
 * <p>Description:故障类</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
public class FaultClassClientFallBack extends BaseFallbackFactory<FaultClassClient> {
    @Override
    public FaultClassClient getFallBack(ResultVO vo) {
        return new FaultClassClient() {
            @Override
            public ResultVO<FaultClassDTO> addFaultClass(FaultClassDTO faultClassDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteFaultClass(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<FaultClassDTO> updateFaultClass(FaultClassDTO faultClassDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<FaultClassDTO>> getChildFaultClasses(Integer parentId) {
                return vo;
            }

            @Override
            public ResultVO<List<FaultClassDTO>> getFaultClassesByEquipmentClassId(Integer equipmentClassId) {
                return vo;
            }

            @Override
            public ResultVO<List<FaultClassDTO>> getChildFaultClasses() {
                return vo;
            }

            @Override
            public ResultVO<FaultClassDTO> getFaultClassById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteFaultClassByFaultClassName(String faultClassName) {
                return vo;
            }

            @Override
            public ResultVO<List<FaultClassDTO>> getFaultClass(String faultClassName) {
                return vo;
            }
        };
    }
}
