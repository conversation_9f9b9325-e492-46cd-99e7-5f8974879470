package com.hvisions.eam.client.fault;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.fault.RootEquipmentFaultDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <p>Title:MaintenanceReportClient</p>
 * <p>Description:设备报修</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/maintenanceReportController", fallbackFactory = MaintenanceReportClientFallBack.class)
public interface MaintenanceReportClient {
    /**
     * 设备报修申请
     *
     * @param rootEquipmentFaultDTO 申请单
     * @return 申请单
     */
    @ApiOperation(value = "设备报修申请")
    @PostMapping(value = "/applyEquipmentFault")
    ResultVO<RootEquipmentFaultDTO> applyEquipmentFault(@RequestBody @Valid RootEquipmentFaultDTO rootEquipmentFaultDTO);
}
