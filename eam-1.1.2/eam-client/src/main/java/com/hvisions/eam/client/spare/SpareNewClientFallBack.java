package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareDTO;
import com.hvisions.eam.query.spare.SpareQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:SpareClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class SpareNewClientFallBack extends BaseFallbackFactory<SpareNewClient> {
    @Override
    public SpareNewClient getFallBack(ResultVO vo) {
        return new SpareNewClient() {
            @Override
            public ResultVO<Integer> createSpare(SpareDTO spareDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateSpare(SpareDTO spareDTO) {
                return vo;
            }

            @Override
            public ResultVO<SpareDTO> getSpareById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<List<SpareDTO>> getSpareListByIdList(List<Integer> ids) {
                return vo;
            }

            @Override
            public ResultVO deleteSpareById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<SpareDTO>> getSpareBySpareCodeAndSpareNameAndSpareTypeSub(SpareQueryDTO spareQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<SpareDTO> getSpareDTOByBatchNumber(String batchNumber) {
                return vo;
            }

            @Override
            public ResultVO<List<Integer>> getFileBySpareId(Integer spareId) {
                return vo;
            }

            @Override
            public ResultVO<List<Integer>> addFileBySpareId(Integer spareId, List<Integer> files) {
                return vo;
            }

            @Override
            public ResultVO deleteBySpareIdAndFileId(Integer spareId, Integer fileId) {
                return vo;
            }

            @Override
            public ResultVO<SpareDTO> getSpareBySpareCode(String spareCode) {
                return vo;
            }
        };
    }
}
