package com.hvisions.eam.client.inspect;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.item.InspectItemDTO;
import com.hvisions.eam.dto.inspect.item.InspectItemQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: InspectItemClient</p >
 * <p>Description: InspectItemClient</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/24</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "eam", path = "/inspectItem", fallbackFactory = InspectItemClientFallBack.class)
public interface InspectItemClient {

    /**
     * 新增点检项目
     *
     * @param inspectItemDTO 点检项目dto
     * @return 新增保养项目id
     */
    @ApiOperation(value = "新增点检项目")
    @PostMapping(value = "/createInspectItem")
    ResultVO<Integer> createInspectItem(@RequestBody InspectItemDTO inspectItemDTO);

    /**
     * 编辑点检项目
     *
     * @param inspectItemDTO 点检项目DTO
     * @return 被编辑的项目id
     */
    @ApiOperation(value = "编辑点检内容")
    @PutMapping(value = "/updateInspectItem")
    ResultVO<Integer> updateInspectItem(@RequestBody InspectItemDTO inspectItemDTO);


    /**
     * 删除单个
     *
     * @param id 单个点检项目id
     * @return vo
     */
    @ApiOperation(value = "删除单个")
    @DeleteMapping(value = "/deleteById/{id}")
    ResultVO deleteById(@PathVariable Integer id);

    /**
     * 批量删除
     *
     * @param idList id集合
     * @return vo
     */
    @ApiOperation(value = "批量删除")
    @DeleteMapping(value = "/deleteByIdList")
    ResultVO deleteByIdList(@RequestBody List<Integer> idList);

    /**
     * 模糊查询
     *
     * @param inspectItemQueryDTO 查询条件
     * @return 点检项目分页DTO
     */
    @ApiOperation(value = "根据设备型号，点检部位，模糊查询")
    @PostMapping(value = "/getByEquipmentModelIdAndPosition")
    ResultVO<HvPage<InspectItemDTO>> getByEquipmentModelIdAndPosition(@RequestBody InspectItemQueryDTO inspectItemQueryDTO);

    /**
     * 根据id获取保养项目详情
     *
     * @param id 保养项目id
     * @return 保养项目信息
     */
    @ApiOperation(value = "根据id获取保养项目信息")
    @GetMapping(value = "/getById/{id}")
    ResultVO<InspectItemDTO> getById(@PathVariable Integer id);

    /**
     * 根据编码获取保养项目详情
     *
     * @param itemCode 保养项目编码
     * @return 保养项目信息
     */
    @ApiOperation(value = "根据编码获取保养项目信息")
    @GetMapping(value = "/getByCode")
    ResultVO<InspectItemDTO> getByCode(@RequestParam String itemCode);

    /**
     * 是否启用点检项目
     *
     * @param id id
     * @return id
     */
    @ApiOperation(value = "是否启用点检项目")
    @PutMapping(value = "startUsingById")
    ResultVO<Integer> startUsingById(@RequestParam Integer id);

    /**
     * 根据点检项目编码查询
     *
     * @param inspectItemCode 点检项目编码
     * @return 点检项目DTO
     */
    @ApiOperation(value = "根据点检项目编码查询")
    @GetMapping(value = "/findByInspectItemCode/{inspectItemCode}")
    ResultVO<InspectItemDTO> findByInspectItemCode(@PathVariable String inspectItemCode);

}
