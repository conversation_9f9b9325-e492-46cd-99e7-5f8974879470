package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareDTO;
import com.hvisions.eam.query.spare.SpareQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title:SpareClient</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/spare", fallbackFactory = SpareNewClientFallBack.class)
public interface SpareNewClient {

    /**
     * 增加备件
     *
     * @param spareDTO 备件DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加备件")
    @PostMapping(value = "/createSpare")
    ResultVO<Integer> createSpare(@RequestBody SpareDTO spareDTO);

    /**
     * 修改备件 通过备件DTO
     *
     * @param spareDTO 备件DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "修改备件")
    @PutMapping(value = "/updateSpare")
    ResultVO<Integer> updateSpare(@RequestBody SpareDTO spareDTO);

    /**
     * 查询备件 通过ID
     *
     * @param id 备件DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "查询备件 通过ID")
    @GetMapping(value = "/getSpareById/{id}")
    ResultVO<SpareDTO> getSpareById(@PathVariable Integer id);

    /**
     * 通过id集合查询备件集合
     *
     * @param ids 备件分页DTO
     * @return 备件集合
     */
    @ApiOperation(value = "通过id集合查询备件集合")
    @PostMapping(value = "/getSpareListByIdList")
    ResultVO<List<SpareDTO>> getSpareListByIdList(@RequestBody List<Integer> ids);

    /**
     * 通过ID删除备件
     *
     * @param id SpareId
     * @return void
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteSpareById/{id}")
    ResultVO deleteSpareById(@PathVariable Integer id);

    /**
     * 分页查询
     * 通过 备件编码 备件名称 小类ID 是否关键部位  联合查询
     *
     * @param spareQueryDTO 备件分页DTO
     * @return 分页信息
     */
    @ApiOperation(value = "分页查询 通过 备件编码 备件名称 小类ID 是否关键部位  联合查询")
    @PostMapping(value = "/getSpareBySpareCodeAndSpareNameAndSpareTypeSub")
    ResultVO<HvPage<SpareDTO>> getSpareBySpareCodeAndSpareNameAndSpareTypeSub(@RequestBody SpareQueryDTO spareQueryDTO);


    /**
     * 通过批次号查询备件信息
     *
     * @param batchNumber 批次号
     * @return 备件信息
     */
    @ApiOperation(value = "批次号查询备件")
    @GetMapping("/getSpareDTOByBatchNumber/{batchNumber}")
    ResultVO<SpareDTO> getSpareDTOByBatchNumber(@PathVariable String batchNumber);


    /**
     * 通过备件ID获取 相关文件列表
     *
     * @param spareId 备件ID
     * @return 文件ID列表
     */
    @ApiOperation(value = "通过备件ID获取 相关文件列表")
    @GetMapping("/getFileBySpareId/{spareId}")
    ResultVO<List<Integer>> getFileBySpareId(@PathVariable Integer spareId);

    /**
     * 通过备件ID添加文件
     *
     * @param spareId 备件ID
     * @param files   文件列表
     * @return 管理学
     */
    @PostMapping("/addFileBySpareId/{spareId}")
    @ApiOperation(value = "通过备件ID添加文件")
    ResultVO<List<Integer>> addFileBySpareId(@PathVariable Integer spareId, @RequestBody List<Integer> files);


    /**
     * 删除备件文件关系 通过备件id 和 文件id
     *
     * @param spareId 备件id
     * @param fileId  文件id
     * @return void
     */
    @DeleteMapping("/deleteBySpareIdAndFileId")
    @ApiOperation(value = "删除关系")
    ResultVO deleteBySpareIdAndFileId(@PathVariable Integer spareId, @PathVariable Integer fileId);

    /**
     * 通过备件编码code 查询备件
     *
     * @param spareCode 备件编码
     * @return 备件信息
     */
    @ApiOperation(value = "通过备件编码code 查询备件")
    @GetMapping("/getSpareBySpareCode/{spareCode}")
    ResultVO<SpareDTO> getSpareBySpareCode(@PathVariable String spareCode);
}
