package com.hvisions.eam.client.spare;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.publicstore.MatchingDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>Title:MatchingClient</p>
 * <p>Description:正则匹配规则</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/matchingController", fallbackFactory = MatchingClientFallBack.class)
public interface MatchingClient {
    /**
     * 增加 和 修改匹配规则
     *
     * @param matchingDTO 匹配规则DTO
     * @return id
     */
    @ApiOperation(value = "增加 和 修改匹配规则")
    @PostMapping(value = "/createMatching")
    ResultVO<Integer> createMatching(@RequestBody MatchingDTO matchingDTO);

    /**
     * 通过服务名查询匹配规则
     *
     * @param service 匹配规则DTO
     * @return id
     */
    @ApiOperation(value = "通过服务名查询匹配规则")
    @PostMapping(value = "/getMatchingByService/{service}")
    ResultVO<MatchingDTO> getMatchingByService(@PathVariable String service);

    /**
     * 获取全部匹配规则服务名
     *
     * @return 匹配规则
     */
    @ApiOperation(value = "获取全部匹配规则服务名")
    @PostMapping(value = "/getServiceName")
    ResultVO<List<String>> getServiceName();

    /**
     * 删除匹配规则
     *
     * @param id id
     * @return void
     */
    @ApiOperation(value = " 删除匹配规则 ")
    @DeleteMapping(value = "/deleteById/{id}")
    ResultVO deleteById(@PathVariable Integer id);

}
