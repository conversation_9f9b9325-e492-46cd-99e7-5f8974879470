package com.hvisions.eam.client.maintain;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalQueryDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalTimeQuantumDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>Title: MaintainStatisticalClient</p >
 * <p>Description: MaintainStatistical</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/23</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "eam", path = "/maintainStatistical", fallbackFactory = MaintainStatisticalClientFallBack.class)
public interface MaintainStatisticalClient {
    /**
     * 获取保养统计数据
     *
     * @param maintainStatisticalQueryDTO 查询条件
     * @return list
     */
    @ApiOperation(value = "获取保养统计数据")
    @PostMapping(value = "/getMaintainStatistical")
    ResultVO<List<MaintainStatisticalDTO>> getMaintainStatistical(@RequestBody MaintainStatisticalQueryDTO maintainStatisticalQueryDTO);

    /**
     * 今日保养次数
     *
     * @return 今日保养次数
     */
    @ApiOperation(value = "今日保养次数")
    @GetMapping(value = "getTodayMaintainCount")
    ResultVO<Integer> getTodayMaintainCount();


    /**
     * 获取本周保养次数
     *
     * @return 次数
     */
    @ApiOperation(value = "获取本周保养次数")
    @GetMapping(value = "getWeekCount")
    ResultVO<Integer> getWeekCount();

    /**
     * 根据时间段查询保养次数
     *
     * @param maintainStatisticalQueryDTO 查询条件
     * @return list
     */
    @ApiOperation(value = "根据时间段查询保养次数")
    @PostMapping(value = "/getCountByTimeQuantum")
    ResultVO<List<MaintainStatisticalTimeQuantumDTO>> getCountByTimeQuantum(@RequestBody MaintainStatisticalQueryDTO maintainStatisticalQueryDTO);
}
