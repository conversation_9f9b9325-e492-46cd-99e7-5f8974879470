package com.hvisions.eam.client.spare;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.activiti.lub.RootLubDTO;
import com.hvisions.eam.activiti.sapre.RootSpareDTO;
import com.hvisions.eam.dto.publicstore.ActualUseDTO;
import com.hvisions.eam.dto.publicstore.UseListingSpareDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>Title:StoreClient</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/1/1</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", fallback = StoreClientFallBack.class)
public interface StoreClient {

    /**
     * 生成唯一不重复的单号 生成规则  年 月 日 流水号（四位）共计12位
     *
     * @param service 唯一标识符 （推荐服务名）
     * @return 唯一不重复的单号
     */
    @ApiOperation("生成唯一不重复的单号 生成规则  年 月 日 流水号（四位）共计12位")
    @GetMapping(value = "/apply/getSerialNumber/{service}")
    ResultVO<String> getSerialNumber(@PathVariable String service);

    /**
     * 批量增加 使用清单
     *
     * @param useListingSpareDTOS 清单List
     * @return id
     */
    @ApiOperation("批量增加 使用清单")
    @PostMapping(value = "/UseListingController/createList")
    ResultVO<List<Integer>> createList(@RequestBody List<UseListingSpareDTO> useListingSpareDTOS);

    /**
     * 生成唯一不重复的单号 生成规则  年 月 日 时 分 秒  随机数（四位）共计18位
     *
     * @return 唯一单号
     */
    @ApiOperation("生成唯一不重复的单号 生成规则  年 月 日 时 分 秒  随机数（四位）共计18位")
    @GetMapping(value = "/apply/getGodownReceiptNumber")
    ResultVO<String> getGodownReceiptNumber();

    /**
     * 申请备件
     *
     * @param rootSpareDTO 申请单
     * @return 申请单
     */
    @ApiOperation("申请备件")
    @PostMapping(value = "/apply/applySpare")
    ResultVO<RootSpareDTO> applySpare(@RequestBody @Valid RootSpareDTO rootSpareDTO);

    /**
     * 申请油品
     *
     * @param rootLubDTO 申请单
     * @return 申请单
     */
    @ApiOperation("申请油品")
    @PostMapping(value = "/apply/applyLub")
    ResultVO<RootLubDTO> applyLub(@RequestBody @Valid RootLubDTO rootLubDTO);

    /**
     * 通过processInstanceIds 获取实际使用获取油品申请通过的申请项数据
     *
     * @param processInstanceIds 流程实例Id
     * @return 申请项 list
     */
    @ApiOperation(value = "通过processInstanceIds 获取实际使用获取油品申请通过的申请项数据")
    @PostMapping(value = "/getActualUseByProcessInstanceIds")
    ResultVO<List<ActualUseDTO>> getActualUseByProcessInstanceIds(@RequestBody List<String> processInstanceIds);

}
