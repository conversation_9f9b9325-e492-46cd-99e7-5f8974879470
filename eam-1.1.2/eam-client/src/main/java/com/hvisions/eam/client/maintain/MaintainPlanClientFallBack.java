package com.hvisions.eam.client.maintain;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.maintain.MaintainPlanDTO;
import com.hvisions.eam.dto.maintain.MaintainPlanQuery;
import com.hvisions.eam.dto.maintain.MaintainPlanRejectDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: MaintainPlanClientFallBack</p >
 * <p>Description: MaintainPlanClientFallBack</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/23</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class MaintainPlanClientFallBack extends BaseFallbackFactory<MaintainPlanClient> {
    @Override
    public MaintainPlanClient getFallBack(ResultVO vo) {
        return new MaintainPlanClient() {
            @Override
            public ResultVO<Map<Integer, String>> getPlanCondition() {
                return vo;
            }

            @Override
            public ResultVO<HvPage<MaintainPlanDTO>> getAllByMaintainPlanNameAndEquipmentIdAndMaintainPlanConditionId(MaintainPlanQuery maintainPlanQuery) {
                return vo;
            }

            @Override
            public ResultVO<Integer> createMaintainPlan(MaintainPlanDTO maintainPlanDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateMaintainPlan(MaintainPlanDTO maintainPlanDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteMaintainPlanById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteMaintainPlanByIdList(List<Integer> idList) {
                return vo;
            }

            @Override
            public ResultVO startPlan(List<String> taskIds) {
                return vo;
            }

            @Override
            public ResultVO refusePlan(MaintainPlanRejectDTO maintainPlanRejectDTO) {
                return vo;
            }

            /**
             * 新增保养计划，无保养内容
             *
             * @param maintainPlanCreateOrUpdateDTO 计划DTO
             * @return id
             */
            @Override
            public ResultVO<Integer> createNewMaintainPlan(MaintainPlanDTO maintainPlanCreateOrUpdateDTO) {
                return vo;
            }

            /**
             * 编辑保养计划
             *
             * @param maintainPlanCreateOrUpdateDTO 保养计划DTO
             * @return 编辑保养计划id
             */
            @Override
            public ResultVO<Integer> updateNewMaintainPlan(MaintainPlanDTO maintainPlanCreateOrUpdateDTO) {
                return vo;
            }

            @Override
            public ResultVO forcedStopMaintainPlan(List<Integer> ids) {
                return vo;
            }

            @Override
            public ResultVO<Integer> checkBindingByTimerId(Integer timerId) {
                return vo;
            }
        };
    }
}