package com.hvisions.eam.client.inspect;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanContentDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanQueryDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanRejectDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: InspectPlanClientFallBack</p >
 * <p>Description: InspectPlanClientFallBack</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/24</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class InspectPlanClientFallBack extends BaseFallbackFactory<InspectPlanClient> {
    @Override
    public InspectPlanClient getFallBack(ResultVO vo) {
        return new InspectPlanClient() {
            @Override
            public ResultVO<Map<Integer, String>> getPlanCondition() {
                return vo;
            }

            @Override
            public ResultVO<HvPage<InspectPlanDTO>> getAllByPlanNameAndConditionId(InspectPlanQueryDTO inspectPlanQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> createInspectPlan(InspectPlanDTO inspectPlanDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateInspectPlan(InspectPlanDTO inspectPlanDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteInspectPlanByIdList(List<Integer> idList) {
                return vo;
            }

            @Override
            public ResultVO<List<InspectPlanContentDTO>> getPlanInfoById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO startPlan(List<String> taskIds) {
                return vo;
            }

            @Override
            public ResultVO refusePlan(InspectPlanRejectDTO inspectPlanRejectDTO) {
                return vo;
            }


            @Override
            public ResultVO forcedStopInspectPlan(List<Integer> ids) {
                return vo;
            }

            @Override
            public ResultVO<Integer> checkBindingByTimerId(Integer timerId) {
                return vo;
            }

            @Override
            public ResultVO<InspectPlanDTO> findByInspectPlanNum(String inspectPlanNum) {
                return vo;
            }
        };
    }
}