package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareTypeDTO;
import com.hvisions.eam.query.spare.SpareTypeQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title:SpareTypeClient</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/spareType", fallbackFactory = SpareTypeClientFallBack.class)
public interface SpareTypeClient {

    /**
     * 增加节点
     *
     * @param spareTypeDTO 备件型号DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "增加节点")
    @PostMapping(value = "/createAddChildNode")
    ResultVO<Integer> createAddChildNode(@RequestBody SpareTypeDTO spareTypeDTO);

    /**
     * 修改节点
     *
     * @param spareTypeDTO 备件型号DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "修改节点")
    @PutMapping(value = "/updateSpareType")
    ResultVO<Integer> updateSpareType(@RequestBody SpareTypeDTO spareTypeDTO);

    /**
     * 查询备件类型 通过ID
     *
     * @param id 备件型号DTO
     * @return 新增数据的ID
     */
    @ApiOperation(value = "通过ID 查询备件类型信息 ")
    @GetMapping(value = "/getSpareType/{id}")
    ResultVO<SpareTypeDTO> getSpareType(@PathVariable Integer id);

    /**
     * 删除备件型号 通过ID
     *
     * @param id 备件类型ID
     * @return void
     */
    @ApiOperation(value = "删除通过ID")
    @DeleteMapping(value = "/deleteSpareTypeById/{id}")
    ResultVO deleteSpareTypeById(@PathVariable Integer id);

    /**
     * 分页查询 通过类型名称 查询所有的父级
     *
     * @param spareTypeQueryDTO 分页信息
     * @return 分页信息
     */
    @ApiOperation(value = "分页查询 通过类型名称 ")
    @PostMapping(value = "/getSparTypePageByTypeName")
    ResultVO<HvPage<SpareTypeDTO>> getSparTypePageByTypeName(@RequestBody SpareTypeQueryDTO spareTypeQueryDTO);

    /**
     * 查询全部
     *
     * @return 分页信息
     */
    @ApiOperation(value = "查询全部")
    @GetMapping(value = "/getSparType")
    ResultVO<List<SpareTypeDTO>> getSparType();

    /**
     * 通过父级ID查询 子级全部信息
     *
     * @param parentId 父级ID
     * @return 子级全部信息
     */
    @ApiOperation(value = "通过父级ID查询 子级全部信息 ")
    @PostMapping(value = "/getChildNode/{parentId}")
    ResultVO<List<SpareTypeDTO>> getChildNodeBySpareTypeId(@PathVariable Integer parentId);

    /**
     * 通过类型code获取类型
     *
     * @param spareTypeCode 类型code
     * @return 备件类型
     */
    @ApiOperation(value = "通过备件类型code获取")
    @PostMapping(value = "/getSpareTypeBySpareTypeCode/{spareTypeCode}")
    ResultVO<SpareTypeDTO> getSpareTypeBySpareTypeCode(@PathVariable String spareTypeCode);


}
