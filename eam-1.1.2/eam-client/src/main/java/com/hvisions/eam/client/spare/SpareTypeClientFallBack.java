package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.spare.SpareTypeDTO;
import com.hvisions.eam.query.spare.SpareTypeQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:SpareTypeClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class SpareTypeClientFallBack extends BaseFallbackFactory<SpareTypeClient> {
    @Override
    public SpareTypeClient getFallBack(ResultVO vo) {
        return new SpareTypeClient() {
            @Override
            public ResultVO<Integer> createAddChildNode(SpareTypeDTO spareTypeDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateSpareType(SpareTypeDTO spareTypeDTO) {
                return vo;
            }

            @Override
            public ResultVO<SpareTypeDTO> getSpareType(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteSpareTypeById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<SpareTypeDTO>> getSparTypePageByTypeName(SpareTypeQueryDTO spareTypeQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<SpareTypeDTO>> getSparType() {
                return vo;
            }

            @Override
            public ResultVO<List<SpareTypeDTO>> getChildNodeBySpareTypeId(Integer parentId) {
                return vo;
            }

            @Override
            public ResultVO<SpareTypeDTO> getSpareTypeBySpareTypeCode(String spareTypeCode) {
                return vo;
            }
        };
    }
}
