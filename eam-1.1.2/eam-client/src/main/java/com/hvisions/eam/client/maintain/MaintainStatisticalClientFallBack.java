package com.hvisions.eam.client.maintain;

import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalQueryDTO;
import com.hvisions.eam.dto.maintain.MaintainStatisticalTimeQuantumDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: MaintainStatisticalClientFallBack</p >
 * <p>Description: MaintainStatisticalClientFallBack</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/23</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class MaintainStatisticalClientFallBack extends BaseFallbackFactory<MaintainStatisticalClient> {
    @Override
    public MaintainStatisticalClient getFallBack(ResultVO vo) {
        return new MaintainStatisticalClient() {
            @Override
            public ResultVO<List<MaintainStatisticalDTO>> getMaintainStatistical(MaintainStatisticalQueryDTO maintainStatisticalQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> getTodayMaintainCount() {
                return vo;
            }

            @Override
            public ResultVO<Integer> getWeekCount() {
                return vo;
            }

            @Override
            public ResultVO<List<MaintainStatisticalTimeQuantumDTO>> getCountByTimeQuantum(MaintainStatisticalQueryDTO maintainStatisticalQueryDTO) {
                return vo;
            }
        };
    }
}