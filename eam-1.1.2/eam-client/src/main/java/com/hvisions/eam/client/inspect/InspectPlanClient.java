package com.hvisions.eam.client.inspect;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanContentDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanQueryDTO;
import com.hvisions.eam.dto.inspect.plan.InspectPlanRejectDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: InspectPlanClient</p >
 * <p>Description: InspectPlanClient</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/24</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "eam", path = "/inspectPlan", fallbackFactory = InspectPlanClientFallBack.class)
public interface InspectPlanClient {

    /**
     * 获取点巡检计划审批状态
     *
     * @return map
     */
    @ApiOperation(value = "获取点巡检计划审批状态")
    @GetMapping(value = "/getPlanCondition")
    ResultVO<Map<Integer, String>> getPlanCondition();

    /**
     * 查询点巡检计划（模糊分页）
     *
     * @param inspectPlanQueryDTO 分页查询条件
     * @return 点巡检计划DTO
     */
    @ApiOperation(value = "根据点巡检计划名称,计划审批状态查询点巡检计划，模糊，分页")
    @PostMapping(value = "/getAllByPlanNameAndConditionId")
    ResultVO<HvPage<InspectPlanDTO>> getAllByPlanNameAndConditionId(@RequestBody InspectPlanQueryDTO inspectPlanQueryDTO);

    /**
     * 新增点巡检计划
     *
     * @param inspectPlanDTO 点巡检计划DTO
     * @return 新增点巡检计划id
     */
    @ApiOperation(value = "新增点巡检计划")
    @PostMapping(value = "/createInspectPlan")
    ResultVO<Integer> createInspectPlan(@RequestBody InspectPlanDTO inspectPlanDTO);

    /**
     * 编辑点巡检计划
     *
     * @param inspectPlanDTO 点巡检计划DTO
     * @return 编辑点巡检计划id
     */
    @ApiOperation(value = "更新点巡检计划")
    @PutMapping(value = "/updateInspectPlan")
    ResultVO<Integer> updateInspectPlan(@RequestBody InspectPlanDTO inspectPlanDTO);

    /**
     * 批量删除点巡检计划
     *
     * @param idList id集合
     * @return vo
     */
    @ApiOperation(value = "批量删除点巡检计划")
    @DeleteMapping(value = "/deleteInspectPlanByIdList")
    ResultVO deleteInspectPlanByIdList(@RequestBody List<Integer> idList);

    /**
     * 根据计划id查询详情
     *
     * @param id id
     * @return list
     */
    @ApiOperation(value = "根据计划id查询详情")
    @GetMapping(value = "/getPlanInfoById/{id}")
    ResultVO<List<InspectPlanContentDTO>> getPlanInfoById(@PathVariable Integer id);

    /**
     * 审批通过计划
     *
     * @param taskIds 任务ids
     * @return vo
     */
    @ApiOperation(value = "审批通过点巡检计划")
    @PutMapping(value = "/startPlan")
    ResultVO startPlan(@RequestBody List<String> taskIds);

    /**
     * 驳回计划
     *
     * @param inspectPlanRejectDTO 驳回DTO
     * @return vo
     */
    @ApiOperation(value = "驳回计划")
    @PutMapping(value = "/refusePlan")
    ResultVO refusePlan(@RequestBody InspectPlanRejectDTO inspectPlanRejectDTO);

    /**
     * 强制停用点巡检计划
     *
     * @param ids id列表
     * @return vo
     */
    @ApiOperation(value = "强制停用点巡检计划")
    @PutMapping(value = "/forcedStopInspectPlan")
    ResultVO forcedStopInspectPlan(@RequestBody List<Integer> ids);

    /**
     * 根据timerId查看多少计划绑定本周期
     *
     * @param timerId timerId
     * @return 次数
     */
    @ApiOperation(value = "根据timerId查看多少计划绑定本周期")
    @GetMapping(value = "/checkBindingByTimerId/{timerId}")
    ResultVO<Integer> checkBindingByTimerId(@PathVariable Integer timerId);


    /**
     * 根据点巡检计划编码查询
     *
     * @param inspectPlanNum 点巡检计划编码
     * @return 保养计划
     */
    @ApiOperation(value = "根据点巡检计划编码查询")
    @GetMapping(value = "/findByInspectPlanNum/{inspectPlanNum}")
    ResultVO<InspectPlanDTO> findByInspectPlanNum(@PathVariable String inspectPlanNum);
}
