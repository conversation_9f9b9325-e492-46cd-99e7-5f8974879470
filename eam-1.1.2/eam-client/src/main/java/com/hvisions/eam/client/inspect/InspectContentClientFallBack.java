package com.hvisions.eam.client.inspect;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.content.InspectContentDTO;
import com.hvisions.eam.dto.inspect.content.InspectContentQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: InspectContentClientFallBack</p >
 * <p>Description: InspectContentClientFallBack</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/24</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class InspectContentClientFallBack extends BaseFallbackFactory<InspectContentClient> {
    @Override
    public InspectContentClient getFallBack(ResultVO vo) {
        return new InspectContentClient() {
            @Override
            public ResultVO<Integer> createInspectContent(InspectContentDTO inspectContentDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateInspectContent(InspectContentDTO inspectContentDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<InspectContentDTO>> getAllByContentNameAndEquipmentId(InspectContentQueryDTO inspectContentQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteInspectContentByIdList(List<Integer> idList) {
                return vo;
            }

            @Override
            public ResultVO<InspectContentDTO> findByInspectContentCode(String inspectContentCode) {
                return vo;
            }
        };
    }
}