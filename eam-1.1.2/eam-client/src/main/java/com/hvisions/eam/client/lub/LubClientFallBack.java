package com.hvisions.eam.client.lub;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.lub.LubricatingDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:SpareClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/4/15</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class LubClientFallBack implements LubClient {

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<LubricatingDTO> getLubById(Integer lubId) {
        return new ResultVO<>();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ResultVO<List<LubricatingDTO>> getLubListByIdList(List<Integer> ids) {
        return new ResultVO<>();
    }

    /**
     * 查询油品 通过编码
     *
     * @param code 油品编号
     * @return 油品数据
     */
    @Override
    public ResultVO<LubricatingDTO> getLubByCode(String code) {
        return new ResultVO<>();
    }

    /**
     * 查询油品 通过编码列表
     *
     * @param codes 油品编码列表
     * @return 油品
     */
    @Override
    public ResultVO<List<LubricatingDTO>> getLubByCodeList(List<String> codes) {
        return new ResultVO<>();
    }
}
