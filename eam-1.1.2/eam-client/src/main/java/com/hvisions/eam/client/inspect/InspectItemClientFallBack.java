package com.hvisions.eam.client.inspect;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.item.InspectItemDTO;
import com.hvisions.eam.dto.inspect.item.InspectItemQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: InspectItemClientFallBack</p >
 * <p>Description: InspectItemClientFallBack</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/24</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class InspectItemClientFallBack extends BaseFallbackFactory<InspectItemClient> {

    @Override
    public InspectItemClient getFallBack(ResultVO vo) {
        return new InspectItemClient() {
            @Override
            public ResultVO<Integer> createInspectItem(InspectItemDTO inspectItemDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateInspectItem(InspectItemDTO inspectItemDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteByIdList(List<Integer> idList) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<InspectItemDTO>> getByEquipmentModelIdAndPosition(InspectItemQueryDTO inspectItemQueryDTO) {
                return vo;
            }

            /**
             * 根据id获取保养项目详情
             *
             * @param id 保养项目id
             * @return 保养项目信息
             */
            @Override
            public ResultVO<InspectItemDTO> getById(Integer id) {
                return vo;
            }

            /**
             * 根据编码获取保养项目详情
             *
             * @param itemCode 保养项目编码
             * @return 保养项目信息
             */
            @Override
            public ResultVO<InspectItemDTO> getByCode(String itemCode) {
                return vo;
            }

            @Override
            public ResultVO<Integer> startUsingById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<InspectItemDTO> findByInspectItemCode(String inspectItemCode) {
                return vo;
            }
        };
    }
}