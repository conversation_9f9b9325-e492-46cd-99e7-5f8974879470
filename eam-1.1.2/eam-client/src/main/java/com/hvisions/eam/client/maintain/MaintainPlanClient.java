package com.hvisions.eam.client.maintain;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.maintain.MaintainPlanDTO;
import com.hvisions.eam.dto.maintain.MaintainPlanQuery;
import com.hvisions.eam.dto.maintain.MaintainPlanRejectDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: MaintainPlanClient</p >
 * <p>Description: MaintainPlanClient</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/23</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "eam", path = "/maintainPlan", fallbackFactory = MaintainPlanClientFallBack.class)
public interface MaintainPlanClient {
    /**
     * 获取保养保养计划审批状态
     *
     * @return 代码，状态
     */
    @ApiOperation(value = "获取保养计划审批状态")
    @GetMapping(value = "/getPlanCondition")
    ResultVO<Map<Integer, String>> getPlanCondition();

    /**
     * 查询保养计划（模糊分页）
     *
     * @param maintainPlanQuery 分页查询条件
     * @return 保养计划DTO
     */
    @ApiOperation(value = "根据保养计划名称，设备名称，计划审批状态查询保养计划，模糊，分页")
    @PostMapping(value = "/getAllByPlanNameAndEquipmentAndConditionId")
    ResultVO<HvPage<MaintainPlanDTO>> getAllByMaintainPlanNameAndEquipmentIdAndMaintainPlanConditionId(@RequestBody MaintainPlanQuery maintainPlanQuery);

    /**
     * 新增保养计划
     *
     * @param maintainPlanDTO 保养计划DTO
     * @return 新增保养计划id
     */
    @ApiOperation(value = "新增保养计划")
    @PostMapping(value = "/createMaintainPlan")
    ResultVO<Integer> createMaintainPlan(@RequestBody MaintainPlanDTO maintainPlanDTO);

    /**
     * 编辑保养计划
     *
     * @param maintainPlanDTO 保养计划DTO
     * @return 编辑保养计划id
     */
    @ApiOperation(value = "更新保养计划")
    @PutMapping(value = "/updateMaintainPlan")
    ResultVO<Integer> updateMaintainPlan(@RequestBody MaintainPlanDTO maintainPlanDTO);

    /**
     * 删除单个保养计划
     *
     * @param id 单个计划id
     * @return vo
     */
    @ApiOperation(value = "根据id删除单个保养计划")
    @DeleteMapping(value = "/deleteMaintainPlanById/{id}")
    ResultVO deleteMaintainPlanById(@PathVariable Integer id);

    /**
     * 批量删除保养计划
     *
     * @param idList id集合
     * @return vo
     */
    @ApiOperation(value = "批量删除保养计划")
    @DeleteMapping(value = "/deleteMaintainPlanByIdList")
    ResultVO deleteMaintainPlanByIdList(@RequestBody List<Integer> idList);

    /**
     * 审批通过计划
     *
     * @param taskIds 任务ids
     * @return vo
     */
    @ApiOperation(value = "审批通过保养计划")
    @PutMapping(value = "/startPlan")
    ResultVO startPlan(@RequestBody List<String> taskIds);

    /**
     * 驳回计划
     *
     * @param maintainPlanRejectDTO 驳回DTO
     * @return vo
     */
    @ApiOperation(value = "驳回计划")
    @PutMapping(value = "/refusePlan")
    ResultVO refusePlan(@RequestBody MaintainPlanRejectDTO maintainPlanRejectDTO);

    /**
     * 新增保养计划，无保养内容
     *
     * @param maintainPlanCreateOrUpdateDTO 计划DTO
     * @return id
     */
    @ApiOperation(value = "新增保养计划，无保养内容")
    @PostMapping(value = "/createNewMaintainPlan")
    ResultVO<Integer> createNewMaintainPlan(@RequestBody MaintainPlanDTO maintainPlanCreateOrUpdateDTO);

    /**
     * 编辑保养计划
     *
     * @param maintainPlanCreateOrUpdateDTO 保养计划DTO
     * @return 编辑保养计划id
     */
    @ApiOperation(value = "更新保养计划（无保养内容）")
    @PutMapping(value = "/updateNewMaintainPlan")
    ResultVO<Integer> updateNewMaintainPlan(@RequestBody MaintainPlanDTO maintainPlanCreateOrUpdateDTO);




    /**
     * 强制停用保养计划
     *
     * @param ids id列表
     * @return vo
     */
    @ApiOperation(value = "强制停用保养计划")
    @PutMapping(value = "/forcedStopMaintainPlan")
    ResultVO forcedStopMaintainPlan(@RequestBody List<Integer> ids);

    /**
     * 根据周期id查看有多少保养计划绑定
     *
     * @param timerId timerId
     * @return 绑定个数
     */
    @ApiOperation(value = "根据周期id查看有多少保养计划绑定")
    @GetMapping(value = "/checkBindingByTimerId/{timerId}")
    ResultVO<Integer> checkBindingByTimerId(@PathVariable Integer timerId);

}