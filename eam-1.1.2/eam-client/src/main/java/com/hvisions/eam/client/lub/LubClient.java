package com.hvisions.eam.client.lub;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.lub.LubricatingDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>Title:SpareClient</p>
 * <p>Description:油品接口</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/3/26</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam",path = "/lub",fallback = LubClientFallBack.class)
public interface LubClient {

    /**
     * 通过油品ID获取油品
     *
     * @param lubId 油品id
     * @return 油品信息
     */
    @GetMapping(value = "/getLubById/{lubId}")
    ResultVO<LubricatingDTO> getLubById(@PathVariable Integer lubId);

    /**
     * 通过id集合查询油品集合
     *
     * @param ids 油品分页DTO
     * @return 油品集合
     */
    @PostMapping(value = "/getLubListByIdList")
    ResultVO<List<LubricatingDTO>> getLubListByIdList(@RequestBody List<Integer> ids);

    /**
     * 查询油品 通过编码
     *
     * @param code 油品编号
     * @return 油品数据
     */
    @ApiOperation(value = "查询油品 通过编码")
    @PostMapping(value = "/getLubByCode/{code}")
    ResultVO<LubricatingDTO> getLubByCode(@PathVariable(value = "code") String code);

    /**
     * 查询油品 通过编码列表
     *
     * @param codes 油品编码列表
     * @return 油品
     */
    @ApiOperation(value = "查询油品 通过编码列表")
    @PostMapping(value = "/getLubByCodeList")
    ResultVO<List<LubricatingDTO>> getLubByCodeList(@RequestBody List<String> codes);
}