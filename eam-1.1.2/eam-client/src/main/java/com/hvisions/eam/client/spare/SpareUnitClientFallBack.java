package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.publicstore.UnitDTO;
import com.hvisions.eam.query.spare.SpareUnitQueryDTO;
import org.springframework.stereotype.Component;

/**
 * <p>Title:SpareUnitClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class SpareUnitClientFallBack extends BaseFallbackFactory<SpareUnitClient> {
    @Override
    public SpareUnitClient getFallBack(ResultVO vo) {
        return new SpareUnitClient() {
            @Override
            public ResultVO<Integer> createSpareUnit(UnitDTO unitDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateSpareUnit(UnitDTO unitDTO) {
                return vo;
            }

            @Override
            public ResultVO<UnitDTO> getSpareUnitById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteSpareUnitById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<UnitDTO>> getSpareUnitPageBySpareName(SpareUnitQueryDTO spareUnitQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<UnitDTO> getSpareUnitByUnitName(String unitName) {
                return vo;
            }
        };
    }
}
