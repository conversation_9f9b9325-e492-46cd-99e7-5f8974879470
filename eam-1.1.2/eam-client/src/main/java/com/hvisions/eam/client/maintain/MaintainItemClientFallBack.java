package com.hvisions.eam.client.maintain;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.maintain.MaintainItemDTO;
import com.hvisions.eam.dto.maintain.MaintainItemQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title: MaintainItemClientFallBack</p >
 * <p>Description: MaintainItemClientFallBack</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/23</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class MaintainItemClientFallBack extends BaseFallbackFactory<MaintainItemClient> {
    @Override
    public MaintainItemClient getFallBack(ResultVO vo) {
        return new MaintainItemClient() {


            /**
             * 新增保养项目
             *
             * @param maintainItemCreateOrUpdateDTO 保养项目DTO
             * @return 新增保养项目id
             */
            @Override
            public ResultVO<Integer> createMaintainItem(MaintainItemDTO maintainItemCreateOrUpdateDTO) {
                return vo;
            }

            /**
             * 编辑保养项目
             *
             * @param maintainItemCreateOrUpdateDTO 保养项目DTO
             * @return 编辑保养项目id
             */
            @Override
            public ResultVO<Integer> updateMaintainItem(MaintainItemDTO maintainItemCreateOrUpdateDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteMaintainItemInfoById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteMaintainItemInfoByIdList(List<Integer> idList) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<MaintainItemDTO>> getMaintainItemByEquipmentModelIdAndMaintainPosition(MaintainItemQueryDTO maintainItemQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<MaintainItemDTO>> getMaintainItemInfoByMaintainContentId(Integer maintainContentId) {
                return vo;
            }

            @Override
            public ResultVO<Integer> startUsingItemById(Integer id) {
                return vo;
            }

            /**
             * 根据id获取保养项目详情
             *
             * @param id 保养项目id
             * @return 保养项目信息
             */
            @Override
            public ResultVO<MaintainItemDTO> getById(Integer id) {
                return vo;
            }

            /**
             * 根据编码获取保养项目详情
             *
             * @param itemCode 保养项目编码
             * @return 保养项目信息
             */
            @Override
            public ResultVO<MaintainItemDTO> getByCode(String itemCode) {
                return vo;
            }

        };
    }
}