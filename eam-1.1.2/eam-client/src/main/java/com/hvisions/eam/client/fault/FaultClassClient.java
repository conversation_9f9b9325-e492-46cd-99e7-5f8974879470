package com.hvisions.eam.client.fault;

import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.fault.FaultClassDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title:HvEmFaultClassClient</p>
 * <p>Description:故障类</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/8/20</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/EmFaultClass", fallbackFactory = FaultClassClientFallBack.class)
public interface FaultClassClient {

    /**
     * 添加故障类型
     *
     * @param faultClassDTO 故障类型DTO
     * @return 故障类型id
     */
    @ApiOperation(value = "新增故障类型")
    @RequestMapping(value = "/addEmFaultClass", method = RequestMethod.POST)
    ResultVO<FaultClassDTO> addFaultClass(@RequestBody FaultClassDTO faultClassDTO);

    /**
     * 删除故障类型
     *
     * @param id 故障类型id
     * @return null
     */
    @ApiOperation(value = "删除故障类型")
    @RequestMapping(value = "/deleteFaultClass/{id}", method = RequestMethod.DELETE)
    ResultVO deleteFaultClass(@PathVariable Integer id);

    /**
     * 更新故障类型
     *
     * @param faultClassDTO faultClassDTO
     * @return FaultClassDTO
     */
    @ApiOperation(value = "更新故障类型")
    @RequestMapping(value = "updateFaultClass", method = RequestMethod.PUT)
    ResultVO<FaultClassDTO> updateFaultClass(@RequestBody FaultClassDTO faultClassDTO);

    /**
     * 查询子故障类
     *
     * @param parentId 父类id
     * @return 子故障类集合
     */
    @ApiOperation(value = "查询子故障类")
    @RequestMapping(value = "getChildFaultClass/{parentId}", method = RequestMethod.GET)
    ResultVO<List<FaultClassDTO>> getChildFaultClasses(@PathVariable Integer parentId);

    /**
     * 根据设备类型id查询故障类型
     *
     * @param equipmentClassId 设备类型id
     * @return 故障类集合
     */
    @ApiOperation(value = "根据设备类型id查询故障类型")
    @GetMapping(value = "getFaultClassesByEquipmentClassId/{equipmentClassId}")
    ResultVO<List<FaultClassDTO>> getFaultClassesByEquipmentClassId(@PathVariable Integer equipmentClassId);

    /**
     * 查询根故障类
     *
     * @return 根故障类集合
     */
    @ApiOperation(value = "根故障类集合")
    @RequestMapping(value = "getRootFaultClass", method = RequestMethod.GET)
    ResultVO<List<FaultClassDTO>> getChildFaultClasses();

    /**
     * 根据id查询故障类
     *
     * @param id 故障类id
     * @return 故障类
     */
    @ApiOperation(value = "根据id查询故障类")
    @GetMapping(value = "getFaultClassById/{id}")
    ResultVO<FaultClassDTO> getFaultClassById(@PathVariable Integer id);

    /**
     * 通过故障类型名称删除
     *
     * @param faultClassName 故障名称
     * @return ResultVO
     */
    @ApiOperation(value = " 通过故障类型名称删除 ")
    @DeleteMapping("/deleteFaultClassByFaultClassName/{faultClassName}")
    ResultVO deleteFaultClassByFaultClassName(@PathVariable String faultClassName);

    /**
     * 通过故障名称查询故障类
     *
     * @param faultClassName 故障名称
     * @return 故障名称
     */
    @ApiOperation(value = "通过故障名称查询故障类")
    @GetMapping(value = "getFaultClass/{faultClassName}")
    ResultVO<List<FaultClassDTO>> getFaultClass(@PathVariable String faultClassName);

}
