package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.lub.LubTypeDTO;
import com.hvisions.eam.query.lub.LubTypeQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:LubTypeClientFallBack</p>
 * <p>Description:油品类型</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class LubTypeClientFallBack extends BaseFallbackFactory<LubTypeClient> {
    @Override
    public LubTypeClient getFallBack(ResultVO vo) {
        return new LubTypeClient() {
            @Override
            public ResultVO<Integer> createAddChildNode(LubTypeDTO lubTypeDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateLubType(LubTypeDTO lubTypeDTO) {
                return vo;
            }

            @Override
            public ResultVO<LubTypeDTO> getLubType(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteLubTypeById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<LubTypeDTO>> getSparTypePageByTypeName(LubTypeQueryDTO lubTypeQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<LubTypeDTO>> getLubType() {
                return vo;
            }

            @Override
            public ResultVO<List<LubTypeDTO>> getChildNode(Integer parentId) {
                return vo;
            }
        };
    }
}
