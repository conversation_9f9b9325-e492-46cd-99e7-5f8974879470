package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.publicstore.UseListingLubDTO;
import com.hvisions.eam.dto.publicstore.UseListingSpareDTO;
import com.hvisions.eam.query.publicstore.UseListingQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:UseListingClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class UseListingClientFallBack extends BaseFallbackFactory<UseListingClient> {
    @Override
    public UseListingClient getFallBack(ResultVO vo) {
        return new UseListingClient() {
            @Override
            public ResultVO<Integer> create(UseListingSpareDTO useListingSpareDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<Integer>> createList(List<UseListingSpareDTO> useListingSpareDTOS) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<UseListingSpareDTO>> getSpareByProcessInstanceId(UseListingQueryDTO useListingQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<UseListingLubDTO>> getLubByProcessInstanceId(UseListingQueryDTO useListingQueryDTO) {
                return vo;
            }
        };
    }
}
