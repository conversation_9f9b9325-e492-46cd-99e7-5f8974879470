package com.hvisions.eam.client.inspect;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.inspect.content.InspectContentDTO;
import com.hvisions.eam.dto.inspect.content.InspectContentQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>Title: InspectContentClient</p >
 * <p>Description: InspectContentClient</p >
 * <p>Company: www.h-visions.com</p >
 * <p>create date: 2019/7/24</p >
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "eam", path = "/inspectItem", fallbackFactory = InspectContentClientFallBack.class)
public interface InspectContentClient {
    /**
     * 新增点检内容
     *
     * @param inspectContentDTO 点检内容DTO
     * @return 新增内容id
     */
    @ApiOperation(value = "新增点检内容")
    @PostMapping(value = "/createInspectContent")
    ResultVO<Integer> createInspectContent(@RequestBody InspectContentDTO inspectContentDTO);

    /**
     * 编辑点检内容
     *
     * @param inspectContentDTO 点检内容DTO
     * @return 被编辑的id
     */
    @ApiOperation(value = "编辑保养内容")
    @PutMapping(value = "/updateInspectContent")
    ResultVO<Integer> updateInspectContent(@RequestBody InspectContentDTO inspectContentDTO);

    /**
     * 根据点检内容名称，设备型号，查询
     *
     * @param inspectContentQueryDTO 点检内容查询条件
     * @return 点检内容分页DTO
     */
    @ApiOperation(value = "根据点检内容名称，设备型号，查询")
    @PostMapping(value = "/getAllByContentNameAndEquipmentId")
    ResultVO<HvPage<InspectContentDTO>> getAllByContentNameAndEquipmentId(@RequestBody InspectContentQueryDTO inspectContentQueryDTO);

    /**
     * 批量删除点检内容
     *
     * @param idList 点检内容的id集合
     * @return vo
     */
    @ApiOperation(value = "批量删除点检内容")
    @DeleteMapping(value = "/deleteInspectContentByIdList")
    ResultVO deleteInspectContentByIdList(@RequestBody List<Integer> idList);

    /**
     * 根据点检内容编码查询
     *
     * @param inspectContentCode 点检内容code
     * @return 点检内容DTO
     */
    @ApiOperation(value = "根据点检内容编码查询")
    @GetMapping(value = "/inspectContentCode/{inspectContentCode}")
    ResultVO<InspectContentDTO> findByInspectContentCode(@PathVariable String inspectContentCode);
}
