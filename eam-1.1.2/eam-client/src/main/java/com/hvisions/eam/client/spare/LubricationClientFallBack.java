package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.lub.LubricatingDTO;
import com.hvisions.eam.query.lub.LubricatingQueryDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>Title:LubricationClientFallBack</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@Component
public class LubricationClientFallBack extends BaseFallbackFactory<LubricationClient> {

    @Override
    public LubricationClient getFallBack(ResultVO vo) {
        return new LubricationClient() {
            @Override
            public ResultVO<LubricatingDTO> getLubricatingByLubCode(String batchNumber) {
                return vo;
            }

            @Override
            public ResultVO<Integer> createLub(LubricatingDTO lubricatingDTO) {
                return vo;
            }

            @Override
            public ResultVO<Integer> updateLub(LubricatingDTO lubricatingDTO) {
                return vo;
            }

            @Override
            public ResultVO<LubricatingDTO> getLubById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO deleteLubById(Integer id) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<LubricatingDTO>> getAllByLubCodeAndLubNameAndLubTypeSub(LubricatingQueryDTO lubricatingQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<LubricatingDTO>> getLubListByIdList(List<Integer> ids) {
                return vo;
            }

            @Override
            public ResultVO<LubricatingDTO> getSpareDTOByBatchNumber(String batchNumber) {
                return vo;
            }
        };
    }
}
