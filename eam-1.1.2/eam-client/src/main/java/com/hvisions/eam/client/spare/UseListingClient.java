package com.hvisions.eam.client.spare;

import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.eam.dto.publicstore.UseListingLubDTO;
import com.hvisions.eam.dto.publicstore.UseListingSpareDTO;
import com.hvisions.eam.query.publicstore.UseListingQueryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p>Title:UseListingClient</p>
 * <p>Description:</p>
 * <p>Company:www.h-visions.com</p>
 * <p>create date:2019/7/30</p>
 *
 * <AUTHOR>
 * @version : 1.0.0
 */
@FeignClient(name = "eam", path = "/UseListingController", fallbackFactory = UseListingClientFallBack.class)
public interface UseListingClient {

    /**
     * 增加 使用清单
     *
     * @param useListingSpareDTO 清单
     * @return id
     */
    @ApiOperation(value = "添加清单")
    @PostMapping(value = "/create")
    ResultVO<Integer> create(@RequestBody UseListingSpareDTO useListingSpareDTO);

    /**
     * 批量增加 使用清单
     *
     * @param useListingSpareDTOS 清单List
     * @return id
     */
    @ApiOperation(value = "批量添加清单")
    @PostMapping(value = "/createList")
    ResultVO<List<Integer>> createList(@RequestBody List<UseListingSpareDTO> useListingSpareDTOS);

    /**
     * 获取备件与processInstanceId关联的清单
     *
     * @param useListingQueryDTO 清单
     * @return 关联清单
     */
    @ApiOperation(value = "获取备件与processInstanceId关联的清单")
    @PostMapping(value = "/getSpareByProcessInstanceId")
    ResultVO<HvPage<UseListingSpareDTO>> getSpareByProcessInstanceId(@RequestBody UseListingQueryDTO useListingQueryDTO);

    /**
     * 获取油品与processInstanceId关联的清单
     *
     * @param useListingQueryDTO 清单
     * @return 关联清单
     */
    @ApiOperation(value = "获取油品与processInstanceId关联的清单")
    @PostMapping(value = "/getLubByProcessInstanceId")
    ResultVO<HvPage<UseListingLubDTO>> getLubByProcessInstanceId(@RequestBody UseListingQueryDTO useListingQueryDTO);
}
