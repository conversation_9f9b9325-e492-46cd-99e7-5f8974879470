<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hvisions</groupId>
        <artifactId>eam</artifactId>
        <version>1.1.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>eam-client</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-feign</artifactId>
            <version>1.4.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.hvisions</groupId>
            <artifactId>eam-common</artifactId>
            <version>1.1.2</version>
        </dependency>
    </dependencies>

</project>
