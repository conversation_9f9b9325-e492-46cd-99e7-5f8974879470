#默认公共配置
spring:
  #  activiti配置
  activiti:
    check-process-definitions: false
    job-executor-activate: true
    database-schema-update: true
    db-identity-used: false
    activityFontName: "宋体"
    labelFontName: "宋体"
    annotationFontName: "宋体"
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: deployTest
      config:
        #nacos中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yaml
        namespace: deployTest
        override-none: true
        extension-configs:
          # 需共享的DataId，yaml后缀不能少，只支持yaml/properties
          # 越靠后，优先级越高
          - data-id: common.yaml
            refresh: true
  #freemarker模板配置
  freemarker:
    suffix: .ftl
    cache: false
    charset: UTF-8
    contentType: text/html
    requestContextAttribute: ctx
    templateEncoding: UTF-8
    templateLoaderPath: classpath:/templates/
    settings:
      defaultEncoding: UTF-8
      url_escaping_charset: UTF-8
      locale: zh_CN
  http:
    encoding:
      force: true
    charset: UTF-8
    enabled: true
  tomcat:
    uri-encoding: UTF-8
  #使用redis作为缓存
  cache:
    type: redis
  #redis 地址和端口号
  #序列化时间格式
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
    #是否打印Jpa生成的sql语句,生产环境可以关闭
    show-sql: false
    #数据库生成策略，如果打开会根据entity对象生成数据库。尽量不要使用
    hibernate:
      ddl-auto: update
  #服务注册名
  application:
    name: activiti
  #国际化配置
  messages:
    basename: i18n/messages
    cache-seconds: -1
    encoding: utf-8
  thymeleaf:
    prefix: classpath:/templates/
ribbon:
  #请求处理的超时时间
  ReadTimeout: 120000
  #请求连接的超时时间
  ConnectTimeout: 30000
mybatis:
  typeAliasesPackage: com.hvisions.activiti.dto.search
  mapperLocations: classpath:mapper/*.xml
server:
  port: 9100
#开启所有的健康监控信息
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
#info标签：可以在springboot admin的Insights界面Detail中进行展示,也可以再eureka界面点击实例名称查看
info:
  build:
    swagger: http://${spring.cloud.client.ip-address}:${server.port}/swagger-ui.html
    server-name: ${h-visions.service-name}
