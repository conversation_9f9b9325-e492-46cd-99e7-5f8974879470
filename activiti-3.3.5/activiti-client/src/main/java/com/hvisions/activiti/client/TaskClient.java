package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.task.*;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: TaskController</p>
 * <p>Description: 流程任务管理，任务提醒，任务完成，创建任务</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Api(description = "任务管理服务控制器")
@FeignClient(value = "activiti", path = "/task", fallbackFactory = TaskFallbackFactory.class)
public interface TaskClient {

    /**
     * 完成任务
     *
     * @param taskHandleDTO 任务执行dto
     * @return 是否成功
     */
    @ApiOperation(value = "完成任务")
    @PutMapping("/completeTask")
    ResultVO completeTask(@RequestBody TaskHandleDTO taskHandleDTO,@RequestHeader String token);

    /**
     * 开启任务
     *
     * @param taskIds 任务ID列表
     * @param token   token
     * @return 是否成功
     */
    @ApiOperation(value = "开启任务")
    @PutMapping("/startTask")
    ResultVO<List<Map<String, Variable>>> startTask(@RequestBody List<String> taskIds, @RequestHeader String token);

    /**
     * 根据id获取任务信息（附带参数）
     *
     * @param taskId 任务id
     * @return 任务信息（带参数）
     */
    @GetMapping("/getTaskByTaskId/{taskId}")
    @ApiOperation(value = "根据id获取任务信息（附带参数）")
    ResultVO<TaskWithVariableDTO> getTaskByTaskId(@PathVariable String taskId);

    /**
     * 设置执行人,不会检查任务是否已经分配
     *
     * @param dto 任务ID执行人
     * @return 是否成功
     */
    @ApiOperation(value = "设置执行人，不会检查任务是否已经分配")
    @PutMapping("/setAssignee")
    ResultVO setAssignee(@RequestBody TaskSetAndDeleteDTO dto);

    /**
     * 设置执行人，会检查任务是否已经分配
     *
     * @param taskId   任务id
     * @param assignee 执行人
     * @return 是否成功
     */
    @ApiOperation(value = "设置执行人,会检查任务是否已经分配")
    @PutMapping("/claim/{taskId}/{assignee}")
    ResultVO claim(@PathVariable String taskId, @PathVariable String assignee, @RequestHeader String token);


    /**
     * 取消任务执行人
     *
     * @param taskId 任务id
     * @return 是否成功
     */
    @ApiOperation(value = "取消任务执行人")
    @PutMapping("/unclaim/{taskId}")
    ResultVO unclaim(@PathVariable String taskId);

    /**
     * 添加授权班组
     *
     * @param taskId      任务id
     * @param userGroupId 用户组
     * @return 是否成功
     */
    @ApiOperation(value = "添加授权班组")
    @PutMapping("/addCandidateGroup/{taskId}/{userGroupId}")
    ResultVO addCandidateGroup(@PathVariable String taskId, @PathVariable String userGroupId);

    /**
     * 设置负责人
     *
     * @param taskId 任务id
     * @param owner  负责人
     * @return 是否成功
     */
    @ApiOperation(value = "设置负责人")
    @PutMapping("/setOwner/{taskId}/{owner}")
    ResultVO setOwner(@PathVariable String taskId, @PathVariable String owner);

    /**
     * 设置优先级
     *
     * @param taskId   任务ID
     * @param priority 优先级顺序
     * @return 是否成功
     */
    @ApiOperation(value = "设置优先级")
    @PutMapping(value = "/setPriority")
    ResultVO setPriority(@RequestParam String taskId, @RequestParam int priority);


    /**
     * 删除任务
     *
     * @param dto 任务ID列表及删除原因
     * @return 是否成功
     */
    @ApiOperation(value = "删除任务")
    @DeleteMapping("/deleteTask")
    ResultVO deleteTask(@RequestBody TaskSetAndDeleteDTO dto);

    /**
     * 根据任务id,获取对应的参数值
     *
     * @param taskId 任务id
     * @return 参数信息
     */
    @ApiOperation(value = "根据任务id,获取对应的参数信息")
    @GetMapping("/getVariables/{taskId}")
    ResultVO<Map<String, Object>> getVariables(@PathVariable String taskId);

    /**
     * 根据任务id，获取参数实例列表
     *
     * @param taskId 任务id
     * @return 参数实例列表
     */
    @ApiOperation(value = "根据任务id，获取参数实例列表")
    @GetMapping("/getVariableInstances/{taskId}")
    ResultVO<Map<String, Variable>> getVariableInstance(@PathVariable String taskId);

    /**
     * 根据运行任务id,设置对应的参数信息
     *
     * @param taskHandleDTO 任务操作DTO
     * @return 是否成功
     */
    @ApiOperation(value = "根据任务id，设置对应的参数信息")
    @PutMapping("/setVariables")
    ResultVO setVariables(@RequestBody TaskHandleDTO taskHandleDTO);

    /**
     * 复杂查询
     *
     * @param queryDTO 查询条件
     * @return 任务分页信息列表
     */
    @PostMapping("/getTask")
    @ApiOperation(value = "根据查询条件查询任务分页")
    ResultVO<HvPage<TaskDTO>> getTaskByQuery(@RequestBody TaskQueryDTO queryDTO);

    /**
     * 复杂查询
     *
     * @param queryDTO 查询条件
     * @return 任务DTO列表
     */
    @PostMapping("/getTaskListByQuery")
    @ApiOperation(value = "根据查询条件查询任务列表")
    ResultVO<List<TaskDTO>> getTaskListByQuery(@RequestBody TaskQueryDTO queryDTO);


    /**
     * 复杂查询
     *
     * @param queryDTO 查询条件
     * @return 任务分页信息列表
     */
    @PostMapping("/getTaskWithVariables")
    @ApiOperation(value = "根据查询条件查询任务分页(附带参数信息)")
    ResultVO<HvPage<TaskWithVariableDTO>> getTaskWithVariables(@RequestBody TaskQueryDTO queryDTO);


    /**
     * 获取任务紧急度名字
     *
     * @return 紧急度值对
     */
    @GetMapping("/getPriorityName")
    @ApiOperation(value = "获取任务紧急度名称")
    ResultVO<Map<Integer, String>> getPriorityName();

}









