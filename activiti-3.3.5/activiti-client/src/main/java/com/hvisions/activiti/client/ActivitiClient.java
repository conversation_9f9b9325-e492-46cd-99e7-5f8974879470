package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.history.HistoricProcessQuery;
import com.hvisions.activiti.dto.history.HistoricTaskInstanceDTO;
import com.hvisions.activiti.dto.history.HistoryProcessInstanceDTO;
import com.hvisions.activiti.dto.instance.ExcutionQueryDTO;
import com.hvisions.activiti.dto.instance.ExecutionDTO;
import com.hvisions.activiti.dto.instance.ProcessInstanceStartDTO;
import com.hvisions.activiti.dto.process.ProcessDefinationQuery;
import com.hvisions.activiti.dto.process.ProcessDefinitionDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.activiti.dto.task.*;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: ActivitiClient</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2018/11/09</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(name = "activiti", fallbackFactory = ActivitiFactory.class)
public interface ActivitiClient {

    /**
     * 根据流程id,或者流程key，启动流程实例，支持传入参数"
     *
     * @param startDTO 启动实例DTO
     * @param token    token
     * @return ProcessDefinitionDTO  流程定义DTO
     */
    @ApiOperation(value = "根据流程id,或者流程key，启动流程实例，支持传入参数")
    @PostMapping("/runtime/startProcessInstance")
    ResultVO<ProcessInstanceDTO> startProcessInstanceWithHeader(@RequestBody ProcessInstanceStartDTO startDTO,
                                                                @RequestHeader String token);

    /**
     * 根据流程id,或者流程key，启动流程实例，支持传入参数"
     *
     * @param startDTO 启动实例DTO
     * @return ProcessDefinitionDTO  流程定义DTO
     */
    @ApiOperation(value = "根据流程id,或者流程key，启动流程实例，支持传入参数")
    @PostMapping("/runtime/startProcessInstance")
    ResultVO<ProcessInstanceDTO> startProcessInstance(@RequestBody ProcessInstanceStartDTO startDTO);

    /**
     * 完成任务
     *
     * @param taskHandleDTO 任务执行dto
     * @return 是否成功
     */
    @ApiOperation(value = "完成任务")
    @PutMapping("/task/completeTask")
    ResultVO completeTask(@RequestBody TaskHandleDTO taskHandleDTO, @RequestHeader String token);


    /**
     * 查询所有流程定义
     *
     * @param definitionQuery 流程定义查询方法
     * @return ProcessDefinitionDTO  流程定义DTO
     */
    @PostMapping("/repository/getAllProcessList")
    @ApiOperation(value = "查询所有流程定义,分页查询,根据发布时间排序")
    ResultVO<List<ProcessDefinitionDTO>> getAllProcessList(@RequestBody ProcessDefinationQuery definitionQuery);

    /**
     * 根据任务id,获取对应的参数值
     *
     * @param taskId 任务id
     * @return task对应参数信息
     */
    @ApiOperation(value = "根据任务id,获取对应的参数信息")
    @GetMapping("/task/getVariables/{taskId}")
    ResultVO<Map<String, Object>> getVariables(@PathVariable String taskId);


    /**
     * 根据任务id，获取参数实例列表
     *
     * @param taskId 任务id
     * @return 参数实例列表
     */
    @ApiOperation(value = "根据任务id，获取参数实例列表")
    @GetMapping("/task/getVariableInstances/{taskId}")
    ResultVO<Map<String, Object>> getVariableInstance(@PathVariable String taskId);


    /**
     * 根据运行实例id,和参数列表，获取对应的参数信息
     *
     * @param executionId 流程实例id
     * @return 运行实例参数信息
     */
    @ApiOperation(value = "根据运行实例id,和参数列表，获取对应的参数信息")
    @PostMapping("/runtime/getRunTimeVariables")
    ResultVO<Map<String, Object>> getRunTimeVariables(@RequestParam String executionId);

    /**
     * 根据流程定义id查询所有流程实例,分页查询,根据发布时间排序
     *
     * @param queryDTO 查询对象
     * @return 流程实例分页信息
     */
    @PostMapping("/runtime/getAllExecutionList")
    @ApiOperation(value = "根据流程定义id查询所有流程实例")
    ResultVO<List<ExecutionDTO>> getAllProcessInstanceList(@RequestBody ExcutionQueryDTO queryDTO);


    /**
     * 根据执行id查询
     *
     * @param executionId 执行id
     * @return 执行实例
     */
    @GetMapping(value = "/runtime/getExecution/{executionId}")
    @ApiOperation(value = "根据执行id查询")
    ResultVO<ExecutionDTO> getExecution(@PathVariable String executionId);


    /**
     * 复杂查询
     *
     * @param queryDTO 查询条件
     * @return 任务列表信息
     */
    @PostMapping("/task/getTaskListByQuery")
    @ApiOperation(value = "根据传入条件查询任务列表")
    ResultVO<List<TaskDTO>> getTaskListByQuery(@RequestBody TaskQueryDTO queryDTO);


    /**
     * 根据id获取任务信息（附带参数）
     *
     * @param taskId 任务id
     * @return 任务信息（带参数）
     */
    @GetMapping("/task/getTaskByTaskId/{taskId}")
    @ApiOperation(value = "根据id获取任务信息（附带参数）")
    ResultVO<TaskWithVariableDTO> getTaskByTaskId(@PathVariable String taskId);


    /**
     * 根据Key查询历史记录"
     *
     * @param processInstanceBusinessKey 流程key
     * @return 历史列表信息
     */
    @GetMapping(value = "/history/getHistoricTaskInstanceByKey")
    @ApiOperation(value = " 根据Key查询历史记录")
    ResultVO<List<HistoricTaskInstanceDTO>> getHistoricTaskInstanceByKey(@RequestParam String processInstanceBusinessKey);


    /**
     * 查询流程实例
     *
     * @param historyQueryDTO 流程实例条件
     * @return 分页信息
     */
    @ApiOperation(value = "根据流程实例条件查询流程实例列表")
    @PostMapping(value = "/history/getHistoryProcessInstanceList")
    ResultVO<List<HistoryProcessInstanceDTO>> getHistoricProcessInstanceList(@RequestBody HistoricProcessQuery historyQueryDTO);

    /**
     * 设置执行人，会检查任务是否已经分配
     *
     * @param taskId   任务id
     * @param assignee 执行人
     * @return resultVo
     */
    @ApiOperation(value = "派发任务")
    @PutMapping("/task/claim/{taskId}/{assignee}")
    ResultVO claim(@PathVariable String taskId, @PathVariable String assignee);

    /**
     * 开启任务
     *
     * @param taskIds 任务ID列表
     * @return 参数列表
     */
    @ApiOperation(value = "开启任务")
    @PutMapping("/task/startTask")
    ResultVO<List<Map<String, Variable>>> startTask(@RequestBody List<String> taskIds);

}
