package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.instance.*;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: RuntimeFallbackFactory</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/7/24</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class RuntimeFallbackFactory extends BaseFallbackFactory<RuntimeClient> {
    @Override
    public RuntimeClient getFallBack(ResultVO vo) {
        return new RuntimeClient() {
            @Override
            public ResultVO<ProcessInstanceDTO> startProcessInstance(ProcessInstanceStartDTO startDTO) {
                return vo;
            }

            @Override
            public ResultVO deleteProcessInstance(String processInstanceId, String deleteReason) {
                return vo;
            }

            @Override
            public ResultVO suspendProcessInstanceById(String processInstanceId) {
                return vo;
            }

            @Override
            public ResultVO activateProcessInstanceById(String processInstanceId) {
                return vo;
            }

            @Override
            public ResultVO<Map<String, Object>> getVariables(String executionId) {
                return vo;
            }

            @Override
            public ResultVO setVariables(ExcutionParameUpdateDTO updateDTO) {
                return vo;
            }

            @Override
            public ResultVO<ExecutionDTO> getExecution(String executionId) {
                return vo;
            }

            @Override
            public ResultVO<ProcessInstanceDTO> getProcessInstance(String processInstanceId) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<ExecutionDTO>> getExecutionPage(ExcutionQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<ProcessInstanceDTO>> getProcessInstancePage(ProcessInstanceQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<ProcessInstanceDTO>> getProcessInstanceList(ProcessInstanceQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<ExecutionDTO>> getProcessInstanceList(ExcutionQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<Page<ExecutionAndVariablesDTO>> getProcessInstanceByVariables(ExcutionQueryDTO queryDTO) {
                return vo;
            }
        };
    }
}









