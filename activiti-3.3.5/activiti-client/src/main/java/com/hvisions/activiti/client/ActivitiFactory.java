package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.history.HistoricProcessQuery;
import com.hvisions.activiti.dto.history.HistoricTaskInstanceDTO;
import com.hvisions.activiti.dto.history.HistoryProcessInstanceDTO;
import com.hvisions.activiti.dto.instance.ExcutionQueryDTO;
import com.hvisions.activiti.dto.instance.ExecutionDTO;
import com.hvisions.activiti.dto.instance.ProcessInstanceStartDTO;
import com.hvisions.activiti.dto.process.ProcessDefinationQuery;
import com.hvisions.activiti.dto.process.ProcessDefinitionDTO;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.activiti.dto.task.*;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: ActivitiFactory</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/7/11</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class ActivitiFactory extends BaseFallbackFactory {
    @Override
    public Object getFallBack(ResultVO vo) {
        return new ActivitiClient() {
            /**
             * 根据流程id,或者流程key，启动流程实例，支持传入参数"
             *
             * @param startDTO 启动实例DTO
             * @param token    token
             * @return ProcessDefinitionDTO  流程定义DTO
             */
            @Override
            public ResultVO<ProcessInstanceDTO> startProcessInstanceWithHeader(ProcessInstanceStartDTO startDTO, String token) {
                return vo;
            }

            @Override
            public ResultVO<ProcessInstanceDTO> startProcessInstance(ProcessInstanceStartDTO startDTO) {
                return vo;
            }

            @Override
            public ResultVO completeTask(TaskHandleDTO taskHandleDTO, @RequestHeader String token) {
                return vo;
            }

            @Override
            public ResultVO<List<ProcessDefinitionDTO>> getAllProcessList(ProcessDefinationQuery definitionQuery) {
                return vo;
            }

            @Override
            public ResultVO<Map<String, Object>> getVariables(String taskId) {
                return vo;
            }

            @Override
            public ResultVO<Map<String, Object>> getVariableInstance(String taskId) {
                return vo;
            }

            @Override
            public ResultVO<Map<String, Object>> getRunTimeVariables(String executionId) {
                return vo;
            }

            @Override
            public ResultVO<List<ExecutionDTO>> getAllProcessInstanceList(ExcutionQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<ExecutionDTO> getExecution(String executionId) {
                return vo;
            }

            @Override
            public ResultVO<List<TaskDTO>> getTaskListByQuery(TaskQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<TaskWithVariableDTO> getTaskByTaskId(String taskId) {
                return vo;
            }

            @Override
            public ResultVO<List<HistoricTaskInstanceDTO>> getHistoricTaskInstanceByKey(String processInstanceBusinessKey) {
                return vo;
            }

            @Override
            public ResultVO<List<HistoryProcessInstanceDTO>> getHistoricProcessInstanceList(HistoricProcessQuery historyQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO claim(String taskId, String assignee) {
                return vo;
            }

            @Override
            public ResultVO<List<Map<String, Variable>>> startTask(List<String> taskIds) {
                return vo;
            }
        };
    }
}









