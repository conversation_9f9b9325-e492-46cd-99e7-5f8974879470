package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.task.*;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: TaskFallbackFactory</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/7/24</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class TaskFallbackFactory extends BaseFallbackFactory<TaskClient> {
    @Override
    public TaskClient getFallBack(ResultVO vo) {
        return new TaskClient() {



            /**
             * 完成任务
             *
             * @param taskHandleDTO 任务执行dto
             * @param token token
             * @return 是否成功
             */
            @Override
            public ResultVO completeTask(TaskHandleDTO taskHandleDTO, String token) {
                return vo;
            }

            /**
             * 开启任务
             *
             * @param taskIds 任务ID列表
             * @param token   token
             * @return 是否成功
             */
            @Override
            public ResultVO<List<Map<String, Variable>>> startTask(List<String> taskIds, String token) {
                return vo;
            }

            @Override
            public ResultVO<TaskWithVariableDTO> getTaskByTaskId(String taskId) {
                return vo;
            }

            @Override
            public ResultVO setAssignee(TaskSetAndDeleteDTO dto) {
                return vo;
            }

            /**
             * 设置执行人，会检查任务是否已经分配
             *
             * @param taskId   任务id
             * @param assignee 执行人
             * @param token token
             * @return 是否成功
             */
            @Override
            public ResultVO claim(String taskId, String assignee, String token) {
                return vo;
            }


            @Override
            public ResultVO unclaim(String taskId) {
                return vo;
            }

            @Override
            public ResultVO addCandidateGroup(String taskId, String userGroupId) {
                return vo;
            }

            @Override
            public ResultVO setOwner(String taskId, String owner) {
                return vo;
            }

            @Override
            public ResultVO setPriority(String taskId, int priority) {
                return vo;
            }

            @Override
            public ResultVO deleteTask(TaskSetAndDeleteDTO dto) {
                return vo;
            }

            @Override
            public ResultVO<Map<String, Object>> getVariables(String taskId) {
                return vo;
            }

            @Override
            public ResultVO<Map<String, Variable>> getVariableInstance(String taskId) {
                return vo;
            }

            @Override
            public ResultVO setVariables(TaskHandleDTO taskHandleDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<TaskDTO>> getTaskByQuery(TaskQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<TaskDTO>> getTaskListByQuery(TaskQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<TaskWithVariableDTO>> getTaskWithVariables(TaskQueryDTO queryDTO) {
                return vo;
            }

            @Override
            public ResultVO<Map<Integer, String>> getPriorityName() {
                return vo;
            }
        };
    }
}









