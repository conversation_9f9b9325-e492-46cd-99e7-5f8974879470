package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.process.ProcessDefinationQuery;
import com.hvisions.activiti.dto.process.ProcessDefinitionDTO;
import com.hvisions.activiti.dto.process.ProcessStepDTO;
import com.hvisions.activiti.dto.process.TaskInfos;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>Title: RepositoryController</p>
 * <p>Description: 管理流程部署和流程定义的API</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(value = "activiti", path = "/repository", fallbackFactory = RepositoryFallbackFactory.class)
public interface RepositoryFeignClient {

    /**
     * 根据bpmn文件内容进行发布
     *
     * @param resourceName 资源名称
     * @param bpmnFile     bpmn文件字符串
     * @return 返回结果
     */
    @PostMapping("/deployByText/{resourceName}")
    ResultVO deployByText(@PathVariable String resourceName, @RequestBody String bpmnFile);

    /**
     * 根据bpmn文件进行发布
     *
     * @param file bpmn文件
     * @return 返回结果
     */
    @PostMapping("/deployByFile")
    ResultVO deployByFile(@RequestParam("file") MultipartFile file);

    /**
     * 获取流程资源名称
     *
     * @param deploymentId 部署id
     * @return 返回结果
     */
    @GetMapping("/getDeployResourceNames/{deploymentId}")
    ResultVO<List<String>> getDeployResourceNames(@PathVariable String deploymentId);

    /**
     * 获取资源(附带文件名和流)
     *
     * @param deploymentId 部署id
     * @param resourceName 资源名称
     * @return 返回请求
     */
    @GetMapping("/getResourceWithStream/{deploymentId}/{resourceName}")
    ResultVO<ExcelExportDto> getResourceWithStream(
            @PathVariable String deploymentId,
            @PathVariable String resourceName);

    /**
     * 根据流程定义key值暂停流程
     *
     * @param processKey 流程定义key值
     * @return 返回值
     */
    @PutMapping("/suspendProcessDefinitionByKey/{processKey}")
    ResultVO suspendProcessDefinitionByKey(@PathVariable String processKey);

    /**
     * 根据流程定义Id暂停流程
     *
     * @param processId 流程定义Id值
     * @return 返回值
     */
    @PutMapping("/suspendProcessDefinitionById/{processId}")
    ResultVO suspendProcessDefinitionById(@PathVariable String processId);

    /**
     * 根据流程定义key值恢复流程
     *
     * @param processKey 流程定义key值
     * @return 返回结果
     */
    @PutMapping("/activateProcessDefinitionByKey/{processKey}")
    ResultVO activateProcessDefinitionByKey(@PathVariable String processKey);


    /**
     * 根据流程定义id值恢复流程
     *
     * @param processId 流程定义Id值
     * @return 返回结果
     */
    @PutMapping("/activateProcessDefinitionById/{processId}")
    ResultVO activateProcessDefinitionById(@PathVariable String processId);

    /**
     * 根据部署信息删除流程定义,如果级联删除，所有的信息都会被删除
     *
     * @param deploymentId 部署id
     * @param cascade      是否级联删除
     * @return 返回结果
     */
    @DeleteMapping("/deleteProcessDefinitionById/{deploymentId}/{cascade}")
    ResultVO deleteProcessDefinitionById(@PathVariable String deploymentId, @PathVariable Boolean cascade);

    /**
     * 查询所有流程定义
     *
     * @param definitionQuery 流程定义查询方法
     * @return 返回结果
     */
    @PostMapping("/getAllProcessList")
    ResultVO<List<ProcessDefinitionDTO>> getAllProcessList(@RequestBody ProcessDefinationQuery definitionQuery);

    /**
     * 获取流程定义图片
     *
     * @param processDefinitionKey 流程定义Key
     * @return 返回结果
     */
    @GetMapping("/getProcessImage/{processDefinitionKey}")
    ResultVO getProcessImage(@PathVariable String processDefinitionKey);


    /**
     * 根据流程定义获取最新任务信息
     *
     * @param processDefinitionKey 流程定义key
     * @return 流程定义key
     */
    @GetMapping("/getTaskInfosByProcessDefinitionKey/{processDefinitionKey}")
    ResultVO<List<TaskInfos>> getTaskInfosByProcessDefinitionKey(@PathVariable String processDefinitionKey);

    /**
     * 根据流程定义key获取最新的流程定义
     *
     * @param processDefinitionKey 流程定义Key
     * @return 流程定义
     */
    @GetMapping("/getProcessDefinitionByProcessDefinitionKey/{processDefinitionKey}")
    ResultVO<ProcessDefinitionDTO> getProcessDefinitionByProcessDefinitionKey(@PathVariable String processDefinitionKey);

    /**
     * 根据流程定义key获取最新的流程定义
     *
     * @param processDefinitionKeyLike 流程定义Key
     * @return 流程定义
     */
    @ApiOperation(value = "根据流程定义模糊获取流程信息")
    @GetMapping("/getProcessDefinitionLike/{processDefinitionKeyLike}")
    ResultVO<List<ProcessDefinitionDTO>> getProcessDefinitionLike(@PathVariable String processDefinitionKeyLike);

    /**
     * 根据流程定义id获取最新的流程定义
     *
     * @param processDefinitionId 流程定义id
     * @return 流程定义
     */
    @ApiOperation(value = "根据流程定义获取流程信息")
    @GetMapping("/getProcessDefinitionByprocessDefinitionId/{processDefinitionId}")
    ResultVO<ProcessDefinitionDTO> getProcessDefinitionByprocessDefinitionId(@PathVariable String processDefinitionId);

    /**
     * 根据流程定义idlist获取最新的流程定义
     *
     * @param ids 流程定义idlist
     * @return 流程定义
     */
    @ApiOperation(value = "根据流程定义获取流程信息")
    @GetMapping("/getProcessDefinitionByprocessDefinitionIdList")
    ResultVO<List<ProcessDefinitionDTO>> getProcessDefinitionByprocessDefinitionIdList(@RequestParam List<String> ids);

    /**
     * 根据流程定义获取最新任务信息
     *
     * @param processDefinitionKey 流程定义key
     * @return 流程定义key
     */
    @GetMapping("/getProcessStepDTO/{processDefinitionKey}")
    @ApiOperation(value = "根据流程定义获取任务图像信息")
    ResultVO<List<ProcessStepDTO>> getProcessStepDTO(@PathVariable String processDefinitionKey);

}









