package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.instance.*;
import com.hvisions.activiti.dto.process.ProcessInstanceDTO;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: RunTimerController</p>
 * <p>Description: 流程运行时对流程实例进行管理与控制</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@FeignClient(value = "activiti", path = "/runtime", fallbackFactory = RuntimeFallbackFactory.class)
@Api(description = "流程运行时对流程实例进行管理与控制")
public interface RuntimeClient {

    /**
     * 根据流程id,或者流程key，启动流程实例，支持传入参数"
     *
     * @param startDTO 启动实例DTO
     * @return 流程实例对象
     */
    @ApiOperation(value = "根据流程id,或者流程key，启动流程实例，支持传入参数")
    @PostMapping("/startProcessInstance")
    ResultVO<ProcessInstanceDTO> startProcessInstance(@RequestBody ProcessInstanceStartDTO startDTO);

    /**
     * 根据运行实例id删除运行实例
     *
     * @param processInstanceId 流程实例id
     * @param deleteReason      删除原因
     * @return 是否成功
     */
    @ApiOperation(value = "删除流程实例(删除的同时会关闭任务)")
    @DeleteMapping("/deleteProcessInstance")
    ResultVO deleteProcessInstance(@RequestParam String processInstanceId,
                                   @RequestParam(required = false, defaultValue = "") String deleteReason);


    /**
     * 根据运行实例id暂停流程
     *
     * @param processInstanceId 流程实例id
     * @return 是否成功
     */
    @ApiOperation(value = "根据运行实例id暂停流程")
    @PutMapping("/suspendProcessInstanceById/{processInstanceId}")
    ResultVO suspendProcessInstanceById(@PathVariable String processInstanceId);

    /**
     * 根据运行实例id恢复
     *
     * @param processInstanceId 流程实例id
     * @return 是否成功
     */
    @ApiOperation(value = "根据运行实例id恢复流程")
    @PutMapping("/activateProcessInstanceById/{processInstanceId}")
    ResultVO activateProcessInstanceById(@PathVariable String processInstanceId);

    /**
     * 根据运行实例id,和参数列表，获取对应的参数信息
     *
     * @param executionId 流程实例id
     * @return 参数信息
     */
    @ApiOperation(value = "根据运行实例id,和参数列表，获取对应的参数信息")
    @PostMapping("/getVariables")
    ResultVO<Map<String, Object>> getVariables(@RequestParam String executionId);

    /**
     * 根据运行实例id,和参数列表，更新参数
     *
     * @param updateDTO 更新对象
     * @return 是否成功
     */
    @ApiOperation(value = "根据运行实例id,和参数列表，更新参数")
    @PutMapping("/setVariables")
    ResultVO setVariables(@RequestBody ExcutionParameUpdateDTO updateDTO);

    /**
     * 根据执行id查询
     *
     * @param executionId 执行id
     * @return 执行实例
     */
    @GetMapping(value = "/getExecution/{executionId}")
    @ApiOperation(value = "根据执行id查询")
    ResultVO<ExecutionDTO> getExecution(@PathVariable String executionId);

    /**
     * 查询流程实例
     *
     * @param processInstanceId 执行id
     * @return 执行实例
     */
    @GetMapping(value = "/getProcessInstance/{processInstanceId}")
    @ApiOperation(value = "根据执行id查询")
    ResultVO<ProcessInstanceDTO> getProcessInstance(@PathVariable String processInstanceId);

    /**
     * 查询执行分页
     *
     * @param queryDTO 查询对象
     * @return 执行分页息
     */
    @PostMapping("/getAllExecution")
    @ApiOperation(value = "查询执行分页,根据发布时间排序")
    ResultVO<HvPage<ExecutionDTO>> getExecutionPage(@RequestBody ExcutionQueryDTO queryDTO);

    /**
     * 根据流程定义id查询所有流程实例列表
     *
     * @param queryDTO 查询对象
     * @return 流程实例列表
     */
    @PostMapping("/getProcessInstancePage")
    @ApiOperation(value = "根据流程定义id查询所有流程实例")
    ResultVO<HvPage<ProcessInstanceDTO>> getProcessInstancePage(@RequestBody ProcessInstanceQueryDTO queryDTO);

    /**
     * 根据流程定义id查询所有流程实例列表
     *
     * @param queryDTO 查询对象
     * @return 流程实例列表
     */
    @PostMapping("/getProcessInstanceList")
    @ApiOperation(value = "根据流程定义id查询所有流程实例")
    ResultVO<List<ProcessInstanceDTO>> getProcessInstanceList(@RequestBody ProcessInstanceQueryDTO queryDTO);


    /**
     * 根据流程定义id查询所有流程实例列表
     *
     * @param queryDTO 查询对象
     * @return 流程实例列表
     */
    @PostMapping("/getExecutionList")
    @ApiOperation(value = "根据流程定义id查询所有流程实例")
    ResultVO<List<ExecutionDTO>> getProcessInstanceList(@RequestBody ExcutionQueryDTO queryDTO);


    /**
     * 根据流程定义参数查询所有流程实例,参数列表"
     *
     * @param queryDTO 查询对象
     * @return 流程实例分页信息
     */
    @PostMapping("/getExecutionAndVariablesDTO")
    @ApiOperation(value = "根据流程定义参数查询所有流程实例,参数列表")
    ResultVO<Page<ExecutionAndVariablesDTO>> getProcessInstanceByVariables(@RequestBody ExcutionQueryDTO queryDTO);

}









