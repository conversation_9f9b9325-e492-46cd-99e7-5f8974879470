package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.history.*;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.vo.ResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.activiti.engine.history.HistoricTaskInstance;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 * <p>Title: HistoryService</p>
 * <p>Description: 提供对流程引擎进行管理和维护的服务</p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/3/25</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Api(description = "提供对流程引擎进行管理和维护的服务")
@FeignClient(value = "activiti", path = "/history", fallbackFactory = HistoryFallBackFactory.class)
public interface HistoryClient {


    /**
     * 根据任务责任人查询历史任务
     *
     * @param assignee 审批人
     * @return 已被审批的项目
     */
    @ApiOperation(value = "根据任务负责人查询历史任务")
    @GetMapping(value = "/getTaskInstance/{assignee}")
    ResultVO<List<HistoricTaskInstance>> getTaskInstance(@PathVariable String assignee);


    /**
     * 根据历史流程实例ID查看流程实例参数
     *
     * @param processInstanceId 流程实例ID查
     * @return 历史记录
     */
    @ApiOperation(value = "根据历史流程实例ID查看流程实例参数")
    @GetMapping(value = "/getHistoricProcessInstanceVariableInstance/{processInstanceId}")
    ResultVO<Map<String, Object>> getHistoricProcessInstanceVariableInstance(@PathVariable String processInstanceId);


    /**
     * 根据历史流程实例ID查看流程实例参数
     *
     * @param taskId 流程实例ID查
     * @return 历史记录
     */
    @ApiOperation(value = "根据历史流程实例ID查看流程实例参数")
    @GetMapping(value = "/getHistoricTaskLocalVariableInstance/{taskId}")
    ResultVO<Map<String, Object>> getHistoricTaskLocalVariableInstance(@PathVariable String taskId);

    /**
     * 查询流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 分页信息
     */
    @ApiOperation(value = "根据流程实例id查询流程实例")
    @GetMapping(value = "/getHistoryProcessInstanceById/{processInstanceId}")
    ResultVO<HistoryProcessInstanceDTO> getProcessInstance(@PathVariable String processInstanceId);

    /**
     * 查询流程实例
     *
     * @param historyQueryDTO 流程实例条件
     * @return 分页信息
     */
    @ApiOperation(value = "根据流程实例条件查询流程实例")
    @PostMapping(value = "/getHistoryProcessInstance")
    ResultVO<HvPage<HistoryProcessInstanceDTO>> getProcessInstance(@RequestBody HistoricProcessQuery historyQueryDTO);


    /**
     * 查询流程实例
     *
     * @param historyQueryDTO 流程实例条件
     * @return 分页信息
     */
    @ApiOperation(value = "根据流程实例条件查询流程实例")
    @PostMapping(value = "/getHistoryProcessInstanceList")
    ResultVO<List<HistoryProcessInstanceDTO>> getProcessInstanceList(@RequestBody HistoricProcessQuery historyQueryDTO);

    /**
     * 根据流程Key查询历史
     *
     * @param processInstanceBusinessKey 流程key
     * @return 历史数据
     */
    @ApiOperation(value = "根据流程Key查询历史")
    @GetMapping(value = "/getHistoricTaskInstanceByKey")
    ResultVO<List<HistoricTaskInstanceDTO>> getHistoricTaskInstanceByKey(@RequestParam String processInstanceBusinessKey);


    /**
     * 根据历史任务id查询任务
     *
     * @param taskId 任务id
     * @return 分页信息
     */
    @ApiOperation(value = "复杂查询 查询历史记录 分页查询")
    @GetMapping(value = "/getHistoricTaskInstanceById/{taskId}")
    ResultVO<HistoricTaskInstanceDTO> getHistoricTaskInstanceById(@PathVariable String taskId);


    /**
     * 复杂查询 查询历史记录 分页查询"
     *
     * @param historyTaskQueryDTO 条件
     * @return 分页信息
     */
    @ApiOperation(value = "复杂查询 查询历史记录 分页查询")
    @PostMapping(value = "/getHistoricTaskInstance")
    ResultVO<HvPage<HistoricTaskInstanceDTO>> getHistoricTaskInstance(@RequestBody HistoryTaskQueryDTO historyTaskQueryDTO);


}











    
    
    
    