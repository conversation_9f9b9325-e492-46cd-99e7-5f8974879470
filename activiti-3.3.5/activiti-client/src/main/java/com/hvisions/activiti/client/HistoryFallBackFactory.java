package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.history.*;
import com.hvisions.common.dto.HvPage;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import org.activiti.engine.history.HistoricTaskInstance;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: ActivitiHistoryFallBackFactory</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/7/24</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class HistoryFallBackFactory extends BaseFallbackFactory<HistoryClient> {
    @Override
    public HistoryClient getFallBack(ResultVO vo) {
        return new HistoryClient() {
            @Override
            public ResultVO<List<HistoricTaskInstance>> getTaskInstance(String assignee) {
                return vo;
            }

            @Override
            public ResultVO<Map<String, Object>> getHistoricProcessInstanceVariableInstance(String processInstanceId) {
                return vo;
            }

            @Override
            public ResultVO<Map<String, Object>> getHistoricTaskLocalVariableInstance(String taskId) {
                return vo;
            }

            @Override
            public ResultVO<HistoryProcessInstanceDTO> getProcessInstance(String processInstanceId) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<HistoryProcessInstanceDTO>> getProcessInstance(HistoricProcessQuery historyQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<HistoryProcessInstanceDTO>> getProcessInstanceList(HistoricProcessQuery historyQueryDTO) {
                return vo;
            }

            @Override
            public ResultVO<List<HistoricTaskInstanceDTO>> getHistoricTaskInstanceByKey(String processInstanceBusinessKey) {
                return vo;
            }

            @Override
            public ResultVO<HistoricTaskInstanceDTO> getHistoricTaskInstanceById(String taskId) {
                return vo;
            }

            @Override
            public ResultVO<HvPage<HistoricTaskInstanceDTO>> getHistoricTaskInstance(HistoryTaskQueryDTO historyTaskQueryDTO) {
                return vo;
            }
        };
    }
}









