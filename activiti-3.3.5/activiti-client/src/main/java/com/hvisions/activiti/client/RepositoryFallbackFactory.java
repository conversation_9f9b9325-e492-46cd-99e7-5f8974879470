package com.hvisions.activiti.client;

import com.hvisions.activiti.dto.process.ProcessDefinationQuery;
import com.hvisions.activiti.dto.process.ProcessDefinitionDTO;
import com.hvisions.activiti.dto.process.ProcessStepDTO;
import com.hvisions.activiti.dto.process.TaskInfos;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseFallbackFactory;
import com.hvisions.common.vo.ResultVO;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>Title: RepositoryFallbackFactory</p>
 * <p>Description: </p>
 * <p>Company: www.h-visions.com</p>
 * <p>create date: 2019/7/11</p>
 *
 * <AUTHOR>
 * @version :1.0.0
 */
@Component
public class RepositoryFallbackFactory extends BaseFallbackFactory {
    @Override
    public Object getFallBack(ResultVO vo) {
        return new RepositoryFeignClient() {
            @Override
            public ResultVO deployByText(String resourceName, String bpmnFile) {
                return vo;
            }

            @Override
            public ResultVO deployByFile(MultipartFile file) {
                return vo;
            }

            @Override
            public ResultVO<List<String>> getDeployResourceNames(String deploymentId) {
                return vo;
            }

            @Override
            public ResultVO<ExcelExportDto> getResourceWithStream(String deploymentId, String resourceName) {
                return vo;
            }

            @Override
            public ResultVO suspendProcessDefinitionByKey(String processKey) {
                return vo;
            }

            @Override
            public ResultVO suspendProcessDefinitionById(String processId) {
                return vo;
            }

            @Override
            public ResultVO activateProcessDefinitionByKey(String processKey) {
                return vo;
            }

            @Override
            public ResultVO activateProcessDefinitionById(String processId) {
                return vo;
            }

            @Override
            public ResultVO deleteProcessDefinitionById(String deploymentId, Boolean cascade) {
                return vo;
            }

            @Override
            public ResultVO<List<ProcessDefinitionDTO>> getAllProcessList(ProcessDefinationQuery definitionQuery) {
                return vo;
            }

            @Override
            public ResultVO getProcessImage(String processDefinitionKey) {
                return vo;
            }

            @Override
            public ResultVO<List<TaskInfos>> getTaskInfosByProcessDefinitionKey(String processDefinitionKey) {
                return vo;
            }

            /**
             * 根据流程定义key获取最新的流程定义
             *
             * @param processDefinitionKey 流程定义Key
             * @return 流程定义
             */
            @Override
            public ResultVO<ProcessDefinitionDTO> getProcessDefinitionByProcessDefinitionKey(String processDefinitionKey) {
                return vo;
            }

            @Override
            public ResultVO<List<ProcessDefinitionDTO>> getProcessDefinitionLike(String processDefinitionKeyLike) {
                return vo;
            }

            @Override
            public ResultVO<ProcessDefinitionDTO> getProcessDefinitionByprocessDefinitionId(String processDefinitionId) {
                return vo;
            }

            @Override
            public ResultVO<List<ProcessDefinitionDTO>> getProcessDefinitionByprocessDefinitionIdList(List<String> ids) {
                return vo;
            }

            /**
             * 根据流程定义获取最新任务信息
             *
             * @param processDefinitionKey 流程定义key
             * @return 流程定义key
             */
            @Override
            public ResultVO<List<ProcessStepDTO>> getProcessStepDTO(String processDefinitionKey) {
                return vo;
            }
        };
    }
}









